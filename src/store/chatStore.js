import { defineStore } from 'pinia'
import knowledgeApi from '@/api/knowledge'
import { streamChatWithKnowledge } from '@/api/chat'
import { useUserStore } from './user'

// 常量定义
const CONSTANTS = {
  DEFAULT_ICON: '📚',
  ID_SUFFIX: {
    USER: '_user',
    AI: '_ai'
  },
  PAGINATION: {
    PAGE_NUM: 1,
    PAGE_SIZE: 20,
    QUESTIONS_SIZE: 15
  },
  STREAM: {
    DATA_PREFIX: 'data: ',
    DONE_SIGNAL: '[DONE]'
  },
  HISTORY_ENABLED: ['1', 1],
  GENERATE_VALUE: 0
}

// 工具函数
const utils = {
  // 生成消息ID
  generateMessageId: (type) => `${Date.now()}${CONSTANTS.ID_SUFFIX[type.toUpperCase()]}`,

  // 创建用户消息
  createUserMessage: (content) => ({
    id: utils.generateMessageId('user'),
    type: 'user',
    content: content.trim(),
    timestamp: new Date().toISOString()
  }),

  // 创建AI消息占位符
  createAIMessage: () => ({
    id: utils.generateMessageId('ai'),
    type: 'assistant',
    content: '',
    timestamp: new Date().toISOString(),
    isStreaming: true,
    done: false,
    docs: null,
    chart_data: null
  }),

  // 检查是否启用历史记录
  isHistoryEnabled: (historyConfig) => CONSTANTS.HISTORY_ENABLED.includes(historyConfig),

  // 格式化历史记录
  formatHistoryMessages: (messages) => {
    const history = []
    for (let i = 0; i < messages.length; i += 2) {
      const userMsg = messages[i]
      const aiMsg = messages[i + 1]

      if (userMsg?.type === 'user' &&
          aiMsg?.type === 'assistant' &&
          aiMsg.done &&
          !aiMsg.isStreaming) {
        const userContent = (userMsg.content || userMsg.answer || '').trim()
        const aiContent = (aiMsg.content || aiMsg.answer || '').trim()

        if (userContent && aiContent) {
          history.push(
            { role: 'user', content: userContent },
            { role: 'assistant', content: aiContent }
          )
        }
      }
    }
    return history
  }
}

export const useChatStore = defineStore('chat', {
  state: () => ({
    messages: [],
    knowledgeList: [],
    currentKnowledge: null,
    chatSettings: {
      model_name: 'deepseek-r1:14b',
      temperature: 0.7,
      top_k: 4,
      score_threshold: 1,
      history: '1',
      max_tokens: 0,
      prompt_name: 'default',
      mix_type: 'bm25,faiss'
    },
    streaming: {
      active: false,
      messageId: null,
      content: ''
    },
    historyList: [],
    commonQuestions: [],
    isLoading: false,
    isSending: false,
    abortController: null
  }),

  getters: {
    canSendMessage: (state) => !state.isSending && !state.streaming.active,
    isStreaming: (state) => state.streaming.active,
    currentKnowledgeName: (state) => state.currentKnowledge?.label || '',
    formattedHistory: (state) => utils.formatHistoryMessages(state.messages)
  },

  actions: {
    async initChat() {
      try {
        this.isLoading = true
        await this.fetchKnowledgeList()
        await this.initializeFirstKnowledge()
      } catch (error) {
        console.error('初始化聊天失败:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async initializeFirstKnowledge() {
      if (this.knowledgeList.length === 0) return

      const firstKnowledge = this.knowledgeList[0]
      this.currentKnowledge = firstKnowledge

      if (firstKnowledge.label) {
        await Promise.all([
          this.fetchChatSettings(firstKnowledge.label),
          this.fetchHistoryList(firstKnowledge.label),
          this.fetchCommonQuestions(firstKnowledge.label)
        ])
      }
    },

    async fetchKnowledgeList() {
      try {
        const userStore = useUserStore()
        const nickName = userStore.userInfo.nickName || ''
        const userId = userStore.userInfo.userId || ''
        const administrator = userStore.userInfo.administrator || false

        // 获取用户的知识库权限列表（参考PC端实现）
        let modelIds = []
        if (userId) {
          try {
            const userRes = await knowledgeApi.getUser(userId)
            modelIds = userRes.modelIds || []
          } catch (error) {
            console.error('获取用户知识库权限失败:', error)
            // 如果获取失败，尝试使用userInfo中的models字段作为备用
            modelIds = userStore.userInfo.models || []
          }
        } else {
          // 如果没有userId，直接使用userInfo中的models字段
          modelIds = userStore.userInfo.models || []
        }

        const response = await knowledgeApi.getChatSetting()
        if (response.code !== 200) {
          throw new Error('获取知识库配置失败')
        }

        const knowledgeSettings = response.data || []
        const filteredList = this.filterKnowledgeList(knowledgeSettings, modelIds, nickName, userId, administrator)

        this.knowledgeList = filteredList
        this.setDefaultChatSettings(filteredList)

      } catch (error) {
        console.error('获取知识库列表失败:', error)
        throw error
      }
    },

    filterKnowledgeList(settings, modelIds, nickName, userId, administrator) {
      return settings
        .filter(setting => this.isValidKnowledge(setting, modelIds, nickName, userId, administrator))
        .map((setting, index) => ({
          label: setting.knowledge_name,
          value: index,
          id: setting.knowledge_name,
          settings: setting,
          icon: setting.icon || CONSTANTS.DEFAULT_ICON
        }))
    },

    isValidKnowledge(setting, modelIds, nickName, userId, administrator) {
      // 超级管理员特殊处理：administrator为true或userId为'1'的用户可以看到所有知识库（除了template-开头的）
      const isSuperAdmin = administrator === true || userId === '1'

      if (isSuperAdmin) {
        const isNotTemplate = !setting.knowledge_name.startsWith('template-')
        return isNotTemplate
      }

      // 普通用户的过滤逻辑
      const hasPermission = modelIds.includes(setting.knowledge_name)
      const isNotPersonalDoc = !(
        setting.knowledge_name.startsWith('个人文档-') &&
        setting.knowledge_name !== `个人文档-${nickName}`
      )
      const isNotTemplate = !setting.knowledge_name.startsWith('template-')

      return hasPermission && isNotPersonalDoc && isNotTemplate
    },

    setDefaultChatSettings(knowledgeList) {
      if (knowledgeList.length > 0) {
        this.chatSettings = knowledgeList[0].settings
      } else {
        throw new Error('用户没有可用的知识库权限')
      }
    },



    async switchKnowledge(knowledge) {
      this.currentKnowledge = knowledge

      if (knowledge.settings) {
        this.chatSettings = knowledge.settings
        this.handleHistorySettings(knowledge.settings)
        await this.loadKnowledgeData(knowledge.label)
      } else {
        console.warn('知识库配置未缓存，重新获取:', knowledge.label)
        await this.fetchChatSettings(knowledge.label)
      }
    },

    handleHistorySettings(settings) {
      const shouldUseHistory = utils.isHistoryEnabled(settings.history)
      if (!shouldUseHistory) {
        this.messages = []
      }
    },

    async loadKnowledgeData(knowledgeLabel) {
      await Promise.all([
        this.fetchHistoryList(knowledgeLabel),
        this.fetchCommonQuestions(knowledgeLabel)
      ])
    },

    async fetchChatSettings(knowledgeName) {
      if (!knowledgeName) return

      try {
        const response = await knowledgeApi.getChatSetting({ knowledgeName })

        this.chatSettings = response.code === 200 && response.data?.length > 0
          ? { ...response.data[0], knowledge_name: knowledgeName }
          : { ...this.chatSettings, knowledge_name: knowledgeName }
      } catch (error) {
        console.error('获取聊天设置失败:', error)
        this.chatSettings = { ...this.chatSettings, knowledge_name: knowledgeName }
      }
    },

    async fetchHistoryList(knowledgeName) {
      if (!knowledgeName) return

      try {
        const response = await knowledgeApi.getHistoryList({
          knowledge_name: knowledgeName,
          pageNum: CONSTANTS.PAGINATION.PAGE_NUM,
          pageSize: CONSTANTS.PAGINATION.PAGE_SIZE,
          generate: CONSTANTS.GENERATE_VALUE.toString()
        })

        if (response.code === 200) {
          this.historyList = response.rows || []
        }
      } catch (error) {
        console.error('获取历史对话列表失败:', error)
      }
    },

    async fetchCommonQuestions(knowledgeName) {
      if (!knowledgeName) return

      try {
        const response = await knowledgeApi.getCommonQuestions({
          pageNum: CONSTANTS.PAGINATION.PAGE_NUM,
          pageSize: CONSTANTS.PAGINATION.QUESTIONS_SIZE,
          knowledgeName,
          faq: ''
        })

        if (response.code === 200) {
          this.commonQuestions = response.rows || []
        }
      } catch (error) {
        console.error('获取常用问题列表失败:', error)
      }
    },

    async sendMessage(text) {
      if (!text.trim() || this.isSending) return

      this.isSending = true

      try {
        const userMessage = utils.createUserMessage(text)
        const aiMessage = utils.createAIMessage()

        this.messages.push(userMessage, aiMessage)
        await this.startStreamChat(text, aiMessage.id)

      } catch (error) {
        console.error('发送消息失败:', error)
        this.messages = this.messages.filter(msg => !msg.isStreaming)
        throw error
      } finally {
        this.isSending = false
      }
    },

    async startStreamChat(query, messageId) {
      if (!this.currentKnowledge) {
        throw new Error('请先选择知识库')
      }

      const historyData = this.prepareHistoryData()
      const requestParams = this.buildRequestParams(query, historyData)

      try {
        const response = await streamChatWithKnowledge(requestParams)
        await this.handleStreamResponse(response, messageId)
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('流式聊天失败:', error)
          this.streaming.active = false
          this.isSending = false

          // 更新消息状态为错误
          const message = this.messages.find(msg => msg.id === messageId)
          if (message) {
            message.content = '抱歉，回答生成失败，请重试'
            message.isStreaming = false
            message.done = true
          }

          throw error
        }
        // AbortError 不需要抛出，已经在 handleStreamResponse 中处理了
      }
    },

    prepareHistoryData() {
      const shouldUseHistory = utils.isHistoryEnabled(this.chatSettings.history)
      if (!shouldUseHistory) return []

      const completedMessages = this.messages.slice(0, -2)
      return utils.formatHistoryMessages(completedMessages)
    },

    buildRequestParams(query, historyData) {
      // 创建 AbortController 用于取消请求
      this.abortController = new AbortController()

      return {
        query,
        knowledge_base_name: this.currentKnowledge.label,
        stream: true,
        history: historyData,
        model_name: this.chatSettings.model_name,
        temperature: this.chatSettings.temperature,
        top_k: this.chatSettings.top_k,
        score_threshold: this.chatSettings.score_threshold,
        max_tokens: this.chatSettings.max_tokens || 0,
        prompt_name: this.chatSettings.prompt_name || 'default',
        mix_type: this.chatSettings.mix_type || 'bm25,faiss',
        abortController: this.abortController
      }
    },



    async handleStreamResponse(response, messageId) {
      if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      this.streaming = {
        active: true,
        messageId,
        content: ''
      }

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            this.processStreamLine(line, messageId)
          }
        }

        this.finishStreaming(messageId)
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('流式请求被用户取消')
          this.handleStreamAbort(messageId)
        } else {
          throw error
        }
      }
    },

    processStreamLine(line, messageId) {
      if (!line.startsWith(CONSTANTS.STREAM.DATA_PREFIX)) return

      const data = line.slice(CONSTANTS.STREAM.DATA_PREFIX.length)
      if (data === CONSTANTS.STREAM.DONE_SIGNAL) return

      try {
        const parsed = JSON.parse(data)
        const updateData = this.buildUpdateData(parsed)

        if (Object.keys(updateData).length > 0) {
          this.updateMessageData(messageId, updateData)
        }
      } catch (e) {
        console.warn('解析流式数据失败:', e, '原始数据:', data)
      }
    },

    buildUpdateData(parsed) {
      const updateData = {}

      if (parsed.answer) {
        this.streaming.content += parsed.answer
        updateData.content = this.streaming.content
      }

      if (parsed.docs?.length > 0) {
        updateData.docs = parsed.docs
      }

      if (parsed.chart_data) {
        updateData.chart_data = parsed.chart_data
      }

      return updateData
    },

    updateMessageData(messageId, updateData) {
      const message = this.messages.find(msg => msg.id === messageId)
      if (message) {
        Object.assign(message, updateData)
      }
    },

    async finishStreaming(messageId) {
      const message = this.messages.find(msg => msg.id === messageId)
      if (message) {
        message.isStreaming = false
        message.done = true
      }

      this.streaming = {
        active: false,
        messageId: null,
        content: ''
      }

      await this.saveHistoryRecord(messageId)
    },

    async saveHistoryRecord(messageId) {
      try {
        const { aiMessage, userMessage } = this.findMessagePair(messageId)

        if (!this.canSaveHistory(aiMessage, userMessage)) return

        const saveData = this.buildSaveData(userMessage, aiMessage)
        const response = await knowledgeApi.saveDatabase(saveData)

        if (response.code === 200) {
          aiMessage.historyId = response.msg
          await this.refreshHistoryList()
        } else {
          console.error('保存历史记录失败:', response.msg)
        }
      } catch (error) {
        console.error('保存历史记录异常:', error)
      }
    },

    findMessagePair(messageId) {
      const aiMessage = this.messages.find(msg => msg.id === messageId)
      const aiMessageIndex = this.messages.findIndex(msg => msg.id === messageId)
      const userMessage = aiMessageIndex > 0 ? this.messages[aiMessageIndex - 1] : null

      return { aiMessage, userMessage }
    },

    canSaveHistory(aiMessage, userMessage) {
      if (!aiMessage?.content) {
        console.log('AI消息不存在或内容为空，跳过保存历史记录')
        return false
      }

      if (!userMessage?.content || userMessage.type !== 'user') {
        console.log('用户消息不存在或内容为空，跳过保存历史记录')
        return false
      }

      if (!this.currentKnowledge) {
        console.log('当前知识库不存在，跳过保存历史记录')
        return false
      }

      return true
    },

    buildSaveData(userMessage, aiMessage) {
      return {
        knowledge_name: this.currentKnowledge.label || this.currentKnowledge.name,
        query: userMessage.content.trim(),
        result: aiMessage.content.trim(),
        type: this.chatSettings?.label || "",
        generate: CONSTANTS.GENERATE_VALUE
      }
    },

    async refreshHistoryList() {
      if (this.currentKnowledge) {
        await this.fetchHistoryList(this.currentKnowledge.label || this.currentKnowledge.name)
      }
    },

    stopChat() {
      const messageId = this.streaming.messageId

      if (this.abortController) {
        this.abortController.abort()
        this.abortController = null
      }

      // 如果有正在流式输出的消息，处理其状态
      if (messageId && this.streaming.active) {
        this.handleStreamAbort(messageId)
      } else {
        this.streaming.active = false
        this.isSending = false
      }
    },

    handleStreamAbort(messageId) {
      const message = this.messages.find(msg => msg.id === messageId)
      if (message) {
        message.isStreaming = false
        message.done = true
        // 如果内容为空，显示取消提示
        if (!message.content.trim()) {
          message.content = '回答已被取消'
        }
      }

      this.streaming = {
        active: false,
        messageId: null,
        content: ''
      }

      this.isSending = false
    },

    clearMessages() {
      this.messages = []
      this.stopChat()
    },

    startNewChat() {
      this.messages = []
      this.streaming = {
        active: false,
        messageId: null,
        content: ''
      }
      this.isSending = false
      this.stopChat()
    },

    async loadHistoryChat(chatId) {
      try {
        const response = await knowledgeApi.getHistoryChat(chatId)
        if (response.code === 200) {
          const chatDetail = response.data
          this.messages = [
            {
              id: utils.generateMessageId('user'),
              type: 'user',
              content: chatDetail.query,
              timestamp: chatDetail.create_time
            },
            {
              id: utils.generateMessageId('ai'),
              type: 'assistant',
              content: chatDetail.answer,
              timestamp: chatDetail.create_time,
              done: true
            }
          ]
        }
      } catch (error) {
        console.error('加载历史对话失败:', error)
        throw error
      }
    }
  }
})
