import { defineStore } from 'pinia'
import { login, getUserInfo, logout } from '@/api/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: uni.getStorageSync('token') || '',
    userInfo: null,
    isAuthenticated: false
  }),

  getters: {
    // 是否已登录
    isLoggedIn: (state) => !!state.token,
    // 用户名
    username: (state) => state.userInfo?.userName || '',
    // 用户昵称
    nickname: (state) => state.userInfo?.nickName || ''
  },

  actions: {
    /**
     * 登录
     * @param {Object} loginData 登录数据
     * @param {string} loginData.username 用户名
     * @param {string} loginData.password 密码
     * @param {string} loginData.code 验证码
     * @param {string} loginData.uuid 验证码UUID
     */
    async login(loginData) {
      try {
        const response = await login(
          loginData.username,
          loginData.password,
          loginData.code,
          loginData.uuid
        )

        if (response.code === 200) {
          // 保存token
          this.token = response.data.access_token
          this.isAuthenticated = true

          // 存储到本地
          uni.setStorageSync('token', this.token)

          // 获取完整的用户信息并保存到store
          await this.ensureUserInfo()

          return { success: true, data: response.data }
        } else {
          throw new Error(response.msg || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        throw error
      }
    },

    /**
     * 获取用户信息
     */
    /**
     * 确保用户信息存在，如果不存在则从接口获取
     */
    async ensureUserInfo() {
      if (!this.userInfo) {
        await this.fetchUserInfo()
      }
      return this.userInfo
    },

    /**
     * 从接口获取用户信息
     */
    async fetchUserInfo() {
      try {
        const response = await getUserInfo()
        if (response.code === 200) {
          this.userInfo = response.data.user
          return this.userInfo
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return null
      }
    },

    /**
     * 退出登录
     */
    async logout() {
      try {
        // 调用退出接口
        await logout()
      } catch (error) {
        console.error('退出登录接口调用失败:', error)
      } finally {
        // 清除本地数据
        this.token = ''
        this.userInfo = null
        this.isAuthenticated = false

        // 清除本地存储
        uni.removeStorageSync('token')
        
        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/login/index'
        })
      }
    },

    /**
     * 初始化认证状态（只恢复token）
     */
    initAuth() {
      const token = uni.getStorageSync('token')
      if (token) {
        this.token = token
        this.isAuthenticated = true
        return true
      }

      return false
    }
  }
})
