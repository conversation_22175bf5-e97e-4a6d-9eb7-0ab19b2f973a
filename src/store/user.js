import { defineStore } from 'pinia'
import { login, getUserInfo, logout } from '@/api/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: uni.getStorageSync('token') || '',
    userInfo: uni.getStorageSync('userInfo') || {},
    isAuthenticated: false
  }),

  getters: {
    // 是否已登录
    isLoggedIn: (state) => !!state.token,
    // 用户名
    username: (state) => state.userInfo.userName || '',
    // 用户昵称
    nickname: (state) => state.userInfo.nickName || ''
  },

  actions: {
    /**
     * 登录
     * @param {Object} loginData 登录数据
     * @param {string} loginData.username 用户名
     * @param {string} loginData.password 密码
     * @param {string} loginData.code 验证码
     * @param {string} loginData.uuid 验证码UUID
     */
    async login(loginData) {
      try {
        const response = await login(
          loginData.username,
          loginData.password,
          loginData.code,
          loginData.uuid
        )

        if (response.code === 200) {
          // 保存token
          this.token = response.data.access_token
          this.isAuthenticated = true

          // 存储到本地
          uni.setStorageSync('token', this.token)

          // 获取完整的用户信息（包含administrator等字段）
          const fullUserInfo = await this.fetchUserInfo()

          if (fullUserInfo) {
            this.userInfo = fullUserInfo
            uni.setStorageSync('userInfo', this.userInfo)
          } else {
            // 如果获取用户信息失败，使用登录接口返回的信息
            this.userInfo = response.data.user
            uni.setStorageSync('userInfo', this.userInfo)
          }

          return { success: true, data: response.data }
        } else {
          throw new Error(response.msg || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        throw error
      }
    },

    /**
     * 获取用户信息
     */
    async fetchUserInfo() {
      try {
        const response = await getUserInfo()
        if (response.code === 200) {
          return response.data.user
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return null
      }
    },

    /**
     * 退出登录
     */
    async logout() {
      try {
        // 调用退出接口
        await logout()
      } catch (error) {
        console.error('退出登录接口调用失败:', error)
      } finally {
        // 清除本地数据
        this.token = ''
        this.userInfo = {}
        this.isAuthenticated = false
        
        // 清除本地存储
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        
        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/login/index'
        })
      }
    },

    /**
     * 检查登录状态
     */
    checkAuthStatus() {
      const token = uni.getStorageSync('token')
      const userInfo = uni.getStorageSync('userInfo')
      
      if (token && userInfo) {
        this.token = token
        this.userInfo = userInfo
        this.isAuthenticated = true
        return true
      }
      
      return false
    }
  }
})
