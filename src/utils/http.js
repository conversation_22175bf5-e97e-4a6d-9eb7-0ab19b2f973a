import HttpRequest from 'luch-request'

import config from '@/config'
import { useUserStore } from '@/store/user'

/**
 * HTTP 请求拦截器
 */

const http = new HttpRequest({
  timeout: 120 * 1000, // 请求超时时间，2分钟
})

http.interceptors.request.use(async (request) => {
  const userStore = useUserStore()

  request.baseURL = config.baseURL

  // 为登录接口本身提供豁免，不进行认证检查
  if (request.custom?.auth === false) {
    return request
  }

  // 如果用户已登录，添加 token 到请求头
  if (userStore.token) {
    request.header.Authorization = 'Bearer ' + userStore.token
  }

  return request
})

http.interceptors.response.use((response) => {
  const apiRes = response.data
  // 如果响应数据错误有额外的逻辑，而不只是仅仅弹出错误，请将 needProcessError 设置为 ture
  if (response.config.custom.needProcessError) {
    return apiRes
  }

  if (response.config.custom.isBlob) {
    return apiRes
  }

  if (apiRes.code === 200) {
    return apiRes
  }

  if (apiRes.code === 401) {
    const userStore = useUserStore()
    userStore.logout() // 调用 store 的 action 来处理登出逻辑
    return Promise.reject(apiRes) // 返回 Promise.reject 更符合拦截器逻辑
  }

  const errMessage = apiRes.msg || '系统繁忙，请稍后重试'

  uni.showModal({
    content: errMessage,
    showCancel: false,
    title: '提示',
  })

  const err = new Error(errMessage)
  console.log('err', err)
  return Promise.reject(err)
}, (err) => {
  console.error('err', err)
  uni.showModal({
    content: '系统繁忙，请稍后重试',
    showCancel: false,
    title: '提示',
  })
  return Promise.reject(err)
})

const request = http.request.bind(http)
export default request
