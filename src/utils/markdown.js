/**
 * Markdown 渲染工具
 * 参考PC端的v-md-editor配置，支持H5和非H5环境的markdown渲染
 */

// #ifdef H5
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

// 参考PC端配置创建markdown-it实例
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  breaks: true, // 支持换行
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return `<pre class="hljs"><code class="language-${lang}">${hljs.highlight(lang, str).value}</code></pre>`
      } catch (error) {
        console.warn('代码高亮失败:', error)
      }
    }
    return `<pre class="hljs"><code>${md.utils.escapeHtml(str)}</code></pre>`
  }
})

// 添加一些自定义规则，模拟v-md-editor的行为
md.renderer.rules.table_open = function () {
  return '<table class="table">'
}

md.renderer.rules.blockquote_open = function () {
  return '<blockquote class="blockquote">'
}

/**
 * H5环境下的markdown渲染（参考PC端v-md-editor）
 * @param {string} text - 要渲染的markdown文本
 * @returns {string} 渲染后的HTML
 */
export function renderMarkdownH5(text) {
  if (!text) return ''

  try {
    return md.render(text)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return text
  }
}
// #endif

/**
 * 简单的markdown渲染（用于非H5环境）
 * @param {string} text - 要渲染的markdown文本
 * @returns {string} 处理后的文本
 */
export function renderMarkdownSimple(text) {
  if (!text) return ''
  
  return text
    // 标题
    .replace(/^### (.*$)/gim, '📌 $1')
    .replace(/^## (.*$)/gim, '📋 $1')
    .replace(/^# (.*$)/gim, '📖 $1')
    // 粗体
    .replace(/\*\*(.*?)\*\*/gim, '$1')
    .replace(/__(.*?)__/gim, '$1')
    // 斜体
    .replace(/\*(.*?)\*/gim, '$1')
    .replace(/_(.*?)_/gim, '$1')
    // 代码块
    .replace(/```[\s\S]*?```/gim, '[代码块]')
    // 行内代码
    .replace(/`([^`]*)`/gim, '[$1]')
    // 链接
    .replace(/\[([^\]]*)\]\([^)]*\)/gim, '$1')
    // 列表项
    .replace(/^\s*[-*+]\s+/gim, '• ')
    .replace(/^\s*\d+\.\s+/gim, '1. ')
}

/**
 * 统一的markdown渲染接口
 * 根据环境自动选择合适的渲染方式
 * @param {string} text - 要渲染的markdown文本
 * @returns {string} 渲染后的内容
 */
export function renderMarkdown(text) {
  if (!text) return ''
  
  // #ifdef H5
  return renderMarkdownH5(text)
  // #endif
  
  // #ifndef H5
  return renderMarkdownSimple(text)
  // #endif
}

/**
 * 提取纯文本内容（去除markdown格式）
 * @param {string} text - markdown文本
 * @returns {string} 纯文本
 */
export function extractPlainText(text) {
  if (!text) return ''
  
  return text
    // 移除代码块
    .replace(/```[\s\S]*?```/gim, '')
    // 移除行内代码
    .replace(/`([^`]*)`/gim, '$1')
    // 移除链接，保留文本
    .replace(/\[([^\]]*)\]\([^)]*\)/gim, '$1')
    // 移除图片
    .replace(/!\[([^\]]*)\]\([^)]*\)/gim, '$1')
    // 移除标题标记
    .replace(/^#{1,6}\s+/gim, '')
    // 移除粗体和斜体标记
    .replace(/\*\*(.*?)\*\*/gim, '$1')
    .replace(/__(.*?)__/gim, '$1')
    .replace(/\*(.*?)\*/gim, '$1')
    .replace(/_(.*?)_/gim, '$1')
    // 移除列表标记
    .replace(/^\s*[-*+]\s+/gim, '')
    .replace(/^\s*\d+\.\s+/gim, '')
    // 移除引用标记
    .replace(/^\s*>\s+/gim, '')
    // 清理多余的空白
    .replace(/\n\s*\n/g, '\n')
    .trim()
}

/**
 * 检查是否为H5环境
 * @returns {boolean}
 */
export function isH5Environment() {
  // #ifdef H5
  return true
  // #endif
  
  // #ifndef H5
  return false
  // #endif
}

/**
 * 获取markdown渲染的CSS样式（参考PC端github主题）
 * @returns {string} CSS样式字符串
 */
export function getMarkdownStyles() {
  return `
    .markdown-content {
      line-height: 1.6;
      color: #24292e;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
      word-wrap: break-word;
    }

    .markdown-content h1,
    .markdown-content h2,
    .markdown-content h3,
    .markdown-content h4,
    .markdown-content h5,
    .markdown-content h6 {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      line-height: 1.25;
    }

    .markdown-content h1 {
      font-size: 2em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }

    .markdown-content h2 {
      font-size: 1.5em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }

    .markdown-content h3 {
      font-size: 1.25em;
    }

    .markdown-content h4 {
      font-size: 1em;
    }

    .markdown-content h5 {
      font-size: 0.875em;
    }

    .markdown-content h6 {
      font-size: 0.85em;
      color: #6a737d;
    }

    .markdown-content p {
      margin-top: 0;
      margin-bottom: 16px;
    }

    .markdown-content pre {
      background-color: #f6f8fa;
      border-radius: 6px;
      padding: 16px;
      overflow: auto;
      margin-bottom: 16px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 85%;
      line-height: 1.45;
    }

    .markdown-content pre code {
      background: transparent;
      padding: 0;
      border-radius: 0;
      font-size: 100%;
    }

    .markdown-content code {
      background-color: rgba(27,31,35,0.05);
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-size: 85%;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    }

    .markdown-content .hljs {
      background: #f6f8fa;
      color: #24292e;
    }

    .markdown-content blockquote {
      margin: 0 0 16px 0;
      padding: 0 1em;
      color: #6a737d;
      border-left: 0.25em solid #dfe2e5;
    }

    .markdown-content blockquote.blockquote {
      background: transparent;
    }

    .markdown-content ul,
    .markdown-content ol {
      margin-top: 0;
      margin-bottom: 16px;
      padding-left: 2em;
    }

    .markdown-content li {
      margin-bottom: 0.25em;
    }

    .markdown-content li + li {
      margin-top: 0.25em;
    }

    .markdown-content table {
      border-collapse: collapse;
      border-spacing: 0;
      width: 100%;
      margin-bottom: 16px;
      display: block;
      overflow: auto;
    }

    .markdown-content table.table {
      display: table;
    }

    .markdown-content th,
    .markdown-content td {
      border: 1px solid #dfe2e5;
      padding: 6px 13px;
    }

    .markdown-content th {
      background-color: #f6f8fa;
      font-weight: 600;
    }

    .markdown-content tr:nth-child(2n) {
      background-color: #f6f8fa;
    }

    .markdown-content a {
      color: #0366d6;
      text-decoration: none;
    }

    .markdown-content a:hover {
      text-decoration: underline;
    }

    .markdown-content strong {
      font-weight: 600;
    }

    .markdown-content em {
      font-style: italic;
    }

    .markdown-content hr {
      height: 0.25em;
      padding: 0;
      margin: 24px 0;
      background-color: #e1e4e8;
      border: 0;
    }

    .markdown-content img {
      max-width: 100%;
      height: auto;
      box-sizing: content-box;
    }
  `
}
