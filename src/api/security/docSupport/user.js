import request from '@/utils/request'

let userService = {
    //材料指南层级
    qList: function(params) {
        return request({
            url: '/fileFormation/userfile/list',
            params: params,
            method: 'get'
        })
    },
    // 搜素
    selectList: function(params) {
        return request({
            url: '/fileFormation/userfile/selectlist',
            params: params,
            method: 'get'
        })
    },
    // 树
    treeUserList: function(params) {
        return request({
            url: '/fileFormation/userFileTreen',
            params: params,
            method: 'get'
        })
    },
    // 上传文件
    addFile: function(params) {
        return request({
            url: '/fileFormation/userfile',
            data: params,
            method: 'post'
        })
    },
    // 修改
    updateFile: function(params) {
        return request({
            url: '/fileFormation/userfile',
            data: params,
            method: 'put'
        })
    },
    // 编辑修改状态
    updateFileState: function(params) {
        return request({
            url: '/fileFormation/userfile/editstate',
            data: params,
            method: 'put'
        })
    },
    // 删除
    deleteFile: function(params) {
        return request({
            url: '/fileFormation/userfile/remove',
            data: params,
            method: 'post'
        })
    },
    // 分享
    shareFile: function(params) {
        return request({
            url: '/fileFormation/sharefile',
            data: params,
            method: 'post'
        })
    },
    // 被分享删除
    delShare: function(id) {
        return request({
            url: '/fileFormation/sharefile/userFileId/' + id,
            method: 'delete'
        })
    },
    // 分享者删除
    delShareList: function(id) {
        return request({
            url: '/fileFormation/sharefile/userfileidlist/' + id,
            method: 'delete'
        })
    },
    // 分享详情查询
    detailShare: function(id) {
        return request({
            url: '/fileFormation/sharefile/userfile/' + id,
            method: 'get'
        })
    }

}

export default userService