import request from '@/utils/request'

let fileService = {
    //材料指南层级
    qList: function (params) {
        return request({
            url: '/fileFormation/guidefile/list',
            params: params,
            method: 'get'
        })
    },
    // 搜素
    selectList: function (params) {
        return request({
            url: '/fileFormation/guidefile/selectlist',
            params: params,
            method: 'get'
        })
    },
    // 树
    treeList: function (params) {
        return request({
            url: '/fileFormation/fileTreen',
            params: params,
            method: 'get'
        })
    },
    // user树
    treeUserList: function(params) {
        return request({
            url: '/fileFormation/userFileTreen',
            params: params,
            method: 'get'
        })
    },
    // 上传文件
    addFile: function(params) {
        return request({
            url: '/fileFormation/guidefile',
            data: params,
            method: 'post'
        })
    },
    // 重命名
    updateFile: function(params) {
        return request({
            url: '/fileFormation/guidefile',
            data: params,
            method: 'put'
        })
    },
    // 删除
    deleteFile: function(params) {
        return request({
            url: '/fileFormation/guidefile/remove',
            data:params,
            method: 'post'
        })
    },
    // 惯用语
    // 查询列表
    iList: function(params) {
        return request({
            url: '/fileFormation/assistfile/list',
            params: params,
            method: 'get'
        })
    },
    // 新增
    addIdioms: function(params) {
        return request({
            url: '/fileFormation/assistfile',
            data: params,
            method: 'post'
        })
    },
    // 修改
    editIdioms: function(params) {
        return request({
            url: '/fileFormation/assistfile',
            data: params,
            method: 'put'
        })
    },
    // 删除
    delIdioms: function(id) {
        return request({
            url: '/fileFormation/assistfile/'+id,
            method: 'delete'
        })
    },
}

export default fileService