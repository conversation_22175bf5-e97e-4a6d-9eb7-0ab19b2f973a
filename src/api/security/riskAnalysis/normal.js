import request from '@/utils/request'

export function qList(query) { //模板列表(自己的和有权限的)
    return request({
      url: '/assisted/template/list',
      method: 'get',
      params: query
    })
}

export function listTemplateByUserId(query) { //模板列表(自己的和有权限的)
  return request({
    url: '/assisted/template/listTemplateByUserId',
    method: 'get',
    params: query
  })
}

export function qSelfList(query) { //模板列表(自己的)
  return request({
    url: '/riskFile/reporttemplate/listByuserId',
    method: 'get',
    params: query
  })
}

export function addTem(query) { //添加模板
    return request({
      url: '/assisted/template/add',
      method: 'post',
      data: query
    })
}

export function editTem(query) { //修改模版
    return request({
      url: '/assisted/template/update',
      method: 'put',
      data: query
    })
}

export function delTem(id) { //删除模版
    return request({
      url: '/assisted/template/' + id,
      method: 'delete',
    })
}

export function delTemFile(url) { //删除模版文件
  return request({
    url: '/riskFile/delectFile',
    method: 'post',
    data: {
      url
    }
  })
}

export function temDetail(id) { //获取模版详情
    return request({
      url: '/assisted/template/' + id,
      method: 'get',
    })
}

export function dList(params) { //配置数据列表
  return request({
    url: '/assisted/template/configuration',
    method: 'post',
    params:params
  })
}
export function listTmple(type) { //报告生成配置选择的模版列表(自己的和有权限的)
  return request({
    url: '/assisted/template/listTemplate/' + type,
    method: 'get',
  })
}
export function reportList(query) { //个人报告列表
  return request({
    url: '/assisted/repRecord/list',
    method: 'get',
    params: query
  })
}
export function reportAdd(query) { //报告生成
  return request({
    url: '/assisted/repRecord/add',
    method: 'post',
    data: query
  })
}
export function reportAddAi(query) { //报告生成
  return request({
    url: '/assisted/repRecord/addAi',
    method: 'post',
    data: query
  })
}

export function vList(query) { //全部报告列表
  return request({
    url: '/riskFile/reportrecord/allList',
    method: 'get',
    params: query
  })
}

export function permsTree(query) { //获取审核人
  return request({
    url: '/alarm/pushconfig/treeselect/1',
    method: 'get',
    params: query
  })
}
export function addTmple(query) { //报告生成添加数据
  return request({
    url: '/riskFile/reportrecord',
    method: 'post',
    data: query
  })
}
export function addData(query) { //添加数据
  return request({
    url: '/assisted/repData/addOrUpdateRepData',
    method: 'post',
    data: query
  })
}

export function addCopyData(id) { //添加数据
  return request({
    url: '/assisted/repData/addCopyRepData/' + id,
    method: 'post'
  })
}

export function editData(query) { //修改报告名称
  return request({
    url: '/riskFile/reportdata',
    method: 'put',
    data: query
  })
}
export function delReport(id) { //删除标签
  return request({
    url: '/assisted/repRecord/' + id,
    method: 'delete',
  })
}
export function addPerFile(params) { //加入个人文档
  return request({
    url: '/assisted/repRecord/addUserDocument',
    method: 'post',
    params:params
  })
}
export function editReportData(query) { //修改模版
  return request({
    url: '/assisted/repRecord/update',
    method: 'put',
    data: query
  })
}
export function againReportData(id) { //重新生成
  return request({
    url: '/assisted/repRecord/rebuild/' + id,
    method: 'post'
  })
}

export function delData(id) { //删除数据
  return request({
    url: '/assisted/repData/' + id,
    method: 'delete',
  })
}

export function dataDetail(id) { //获取数据详情
  return request({
    url: '/riskFile/reportdata/' + id,
    method: 'get',
  })
}


export function lblList(id) { //标签列表
  return request({
    url: '/assisted/label/labels/' + id,
    method: 'get'
  })
}

export function addLabel(query) { //添加标签
  return request({
    url: '/assisted/label/add',
    method: 'post',
    data: query
  })
}

export function editLabel(query) { //修改标签
  return request({
    url: '/assisted/label/update',
    method: 'put',
    data: query
  })
}

export function delLabel(id) { //删除标签
  return request({
    url: '/assisted/label/' + id,
    method: 'delete',
  })
}

export function lblDetail(id) { //获取标签详情
  return request({
    url: '/riskFile/datalabel/' + id,
    method: 'get',
  })
}

export function permsList(query) { //权限人员列表
  return request({
    url: '/riskFile/reportauth/list',
    method: 'get',
    params: query
  })
}

export function addPerms(query) { //权限人员列表
  return request({
    url: '/riskFile/reportauth',
    method: 'post',
    data: query
  })
}

export function delPerms(id) { //删除人员
  return request({
    url: '/riskFile/reportauth/' + id,
    method: 'delete',
  })
}

export function getProcessVariable(id) { //获取流程信息
  return request({
    url: '/riskFile/reportrecord/getProcessVariable/' + id,
    method: 'get',
  })
}

// 启动流程
export function start(data) {
  return request({
    url: '/activiti/act/process/start',
    method: 'post',
    data: data
  })
}
// 附件删除
export function deleteFile(ids){
  return request({
    url:"/filecore/recycleBin/clear/" + ids,
    method:"delete",
  })
}
// 附件上传
export function uploadFile(params){
  return request({
    url:"/filecore/upload",
    method:"post",
    data:params
  })
}


export function updateStartStop(data){
  return request({
    url: '/assisted/template/updateStartStop',
    method: 'post',
    data: data
  })
}

export function copyTemplate(id){
  return request({
    url: '/assisted/template/copyTemplate/' + id,
    method: 'get'
  })
}


export function getRepDataSqlByTemplateId(templateId){
  return request({
    url: '/assisted/repData/getRepDataSqlByTemplateId/' + templateId,
    method: 'get'
  })
}


export function downloadDocByHtml(data){
  return request({
    url: '/assisted/repRecord/downloadDocByHtml',
    method: 'post',
    data: data,
    responseType: "blob",
  })
}
