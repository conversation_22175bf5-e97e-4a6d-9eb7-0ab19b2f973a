import request from '@/utils/request'

export function crtWord(query) {
  return request({
    url: '/office/createword',
    method: 'post',
    params: query
  })
}

export function readOnlyWord(query) {
  return request({
    url: '/fileFormation/readOnlyWord',
    method: 'post',
    params: query
  })
}

export function copyOne(query) {
  return request({
    url: '/fileFormation/dynamicCreateWord2',
    method: 'post',
    params: query
  })
}
export function copyTwo(query) {
  return request({
    url: '/fileFormation/dynamicCreateWord22',
    method: 'post',
    params: query
  })
}

export function edit1(query) { //编辑--插入书签
  return request({
    url: '/fileFormation/dynamicCreateWord',
    method: 'post',
    params: query
  })
}

export function edit2(query) {
  return request({
    url: '/fileFormation/word',
    method: 'post',
    params: query
  })
}
