import request from '@/utils/request'

// 查询大纲列表
export function listOutline(query) {
  return request({
    url: '/assisted/outline/list',
    method: 'get',
    params: query
  })
}

// 查询大纲详细
export function getOutline(id) {
  return request({
    url: '/assisted/outline/' + id,
    method: 'get'
  })
}

// 新增大纲
export function addOutline(data) {
  return request({
    url: '/assisted/outline',
    method: 'post',
    data: data
  })
}

// 修改大纲
export function updateOutline(data) {
  return request({
    url: '/assisted/outline',
    method: 'put',
    data: data
  })
}

// 删除大纲
export function delOutline(id) {
  return request({
    url: '/assisted/outline/' + id,
    method: 'delete'
  })
}

export function updateStartStop(data){
  return request({
    url: '/assisted/outline/updateStartStop',
    method: 'post',
    data: data
  })
}
