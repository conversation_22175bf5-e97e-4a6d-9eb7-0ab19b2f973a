import request from '@/utils/request'

// 查询安全看板配置列表
export function getSafeNameList() {
  return request({
    url: '/system/safeBoardRule/getSafeNamelist',
    method: 'get'
  })
}
// 查询部门下拉树结构
// export function treeselect(query) {
//   return request({
//     url: '/system/workSpeed/treeselect',
//     method: 'get',
//     params: query
//   })
// }

export function getDeptLists() {
  return request({
    url: '/system/safeShow/getDeptLists',
    method: 'get'
  })
}

//查询安全看板配置列表
export function listShow(query) {
  return request({
    url: '/system/safeShow/list',
    method: 'get',
    params: query
  })
}

export function getRiskTypeCount(query) {
  return request({
    url: '/system/safeShow/getRiskTypeCount',
    method: 'get',
    params: query
  })
}

//获取安全事件对应的风险等级
export function getRiskLevel(id) {
  return request({
    url: '/system/safeBoardRule/getRiskLevelList/'+id,
    method: 'get'
  })
}


//获取数量和分值分布
export function getNumAndScoreList(query) {
  return request({
    url: '/system/safeShow/getNumAndScoreList',
    method: 'get',
    params: query
  })
}

//查询安全看板-风险追溯
export function getdataList(query) {
  return request({
    url: '/system/safeShow/getdataList',
    method: 'get',
    params: query
  })
}


//查询安全看板-风险追溯
export function selectRiskTrend(query) {
  return request({
    url: '/system/safeShow/selectRiskTrend',
    method: 'get',
    params: query
  })
}


//下载-风险追溯
export function downExcel(query) {
  return request({
    url: '/system/safeShow/downExcel',
    method: 'get',
    params: query
  })
}
