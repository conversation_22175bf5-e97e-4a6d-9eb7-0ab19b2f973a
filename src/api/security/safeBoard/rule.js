import request from '@/utils/request'

// 查询安全看板配置列表
export function listConfig(query) {
  return request({
    url: '/system/safeBoardRule/list',
    method: 'get',
    params: query
  })
}

// 查询安全看板配置详细
export function getConfig(id) {
  return request({
    url: '/system/safeBoardRule/' + id,
    method: 'get'
  })
}

// 新增安全看板配置
export function addConfig(data) {
  return request({
    url: '/system/safeBoardRule',
    method: 'post',
    data: data
  })
}

// 修改安全看板配置
export function updateConfig(data) {
  return request({
    url: '/system/safeBoardRule',
    method: 'put',
    data: data
  })
}

// 删除安全看板配置
export function delConfig(id) {
  return request({
    url: '/system/safeBoardRule/' + id,
    method: 'delete'
  })
}

// 查询风险等级配置信息
export function getRiskType(id) {
  return request({
    url: '/system/safeBoardRule/getRiskTypeData/' + id,
    method: 'get'
  })
}

// 新增安全看板风险配置
export function addRiskConfig(data) {
  return request({
    url: '/system/safeBoardRule/addRiskConfig',
    method: 'post',
    data: data
  })
}

// 修改安全看板风险配置
export function updateRiskConfig(data) {
  debugger
  return request({
    url: '/system/safeBoardRule/updateRiskConfig',
    method: 'put',
    data: data
  })
}


// 删除安全看板配置
export function delRiskConfig(id) {
  return request({
    url: '/system/safeBoardRule/delRiskConfig/' + id,
    method: 'delete'
  })
}
