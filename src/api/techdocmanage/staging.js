import request from '@/utils/request'

/**
 * <AUTHOR>
 * @时间 2022-07-07 09:40:36
 * @功能 编辑事项
 * @param  
*/
export function GetEditItem(){
    return request({
        url: '/techDocManage/docWorkBench/editItem',
        method: 'get'
    })
}

/**
 * <AUTHOR>
 * @时间 2022-07-07 09:42:28
 * @功能 成果分析
 * @param  
*/
export function GetAnalysisResult(){
    return request({
        url: '/techDocManage/docWorkBench/analysisResult',
        method: 'get'
    })
}

/**
 * <AUTHOR>
 * @时间 2022-07-07 09:44:13
 * @功能 工作分析
 * @param  
*/
export function GetWorkAnalysis(){
    return request({
        url: '/techDocManage/docWorkBench/workAnalysis',
        method: 'get'
    })
}

/**
 * <AUTHOR>
 * @时间 2022-07-07 09:45:32
 * @功能 最新发布
 * @param  
*/
export function GetLatestRelease() {
    return request({
        url: '/techDocManage/docWorkBench/latestRelease',
        method: 'get'
    })
}

/**
 * <AUTHOR>
 * @时间 2022-07-07 09:46:41
 * @功能 即将过期 业务
 * @param  
*/
export function GetAboutToExpire() {
    return request({
        url: '/techDocManage/docWorkBench/uploadBusinessExpire',
        method: 'get'
    })
}

/**
 * <AUTHOR>
 * @时间 2022-07-08 14:26:05
 * @功能 即将过期  分享给我
 * @param  
*/
export function GetShareExpired() {
    return request({
        url: '/techDocManage/docWorkBench/shareExpired',
        method: 'get'
    })
}



/**
 * <AUTHOR>
 * @时间 2022-07-07 09:47:36
 * @功能 快速访问
 * @param  
*/
export function GetRapidAccess() {
    return request({
        url: '/techDocManage/docWorkBench/rapidAccess',
        method: 'get'
    })
}

/**
 * <AUTHOR>
 * @时间 2022-07-07 09:48:33
 * @功能 最近浏览
 * @param  
*/
export function GetRecentBrowse() {
    return request({
        url: '/techDocManage/docWorkBench/recentBrowse',
        method: 'get'
    })
}

/**
 * <AUTHOR>
 * @时间 2022-07-07 09:48:33
 * @功能 最近浏览
 * @param  
*/
export function GetRecommendDoc() {
    return request({
        url: '/techDocManage/document/getRecommendDoc',
        method: 'get'
    })
}