import request from '@/utils/request'

// 查询归档业务列表
export function listDocArchiveBusiness(query) {
  return request({
    url: '/techDocManage/docArchiveBusiness/list',
    method: 'get',
    params: query
  })
}

// 查询归档业务详细
export function getDocArchiveBusiness(id) {
  return request({
    url: '/techDocManage/docArchiveBusiness/' + id,
    method: 'get'
  })
}

// 新增归档业务
export function addDocArchiveBusiness(data) {
  return request({
    url: '/techDocManage/docArchiveBusiness',
    method: 'post',
    data: data
  })
}

// 修改归档业务
export function updateDocArchiveBusiness(data) {
  return request({
    url: '/techDocManage/docArchiveBusiness',
    method: 'put',
    data: data
  })
}

// 删除归档业务
export function delDocArchiveBusiness(id) {
  return request({
    url: '/techDocManage/docArchiveBusiness/' + id,
    method: 'delete'
  })
}

// 查询归档业务详细
export function getDocArchiveBusinessListByConfigId(id) {
  return request({
    url: '/techDocManage/docArchiveBusiness/getListByConfigId/' + id,
    method: 'get'
  })
}
