import request from "@/utils/request";

let labelMaintenanceService = {
  // 查询标签列表
  labelList: function (params) {
    return request({
      url: "/techDocManage/docLabel/listAll",
      method: "get",
      params,
    });
  },
  // 标签新增
  addLabel: function (params) {
    return request({
      url: "/techDocManage/docLabel",
      data: params,
      method: "post",
    });
  },
  // 标签修改
  putLabel: function (params) {
    return request({
      url: "/techDocManage/docLabel",
      data: params,
      method: "put",
    });
  },
  // 标签删除
  deleteLabel: function (id) {
    return request({
      url: "/techDocManage/docLabel/" + id,
      method: "delete",
    });
  },
  // 个人文档-标签移除
  removeLabel: function (id, ids) {
    return request({
      url: "/techDocManage/docLabel/removeDocByLabel/" + id + "/" + ids,
      method: "delete",
    });
  },
  // 个人文档-根据标签查询个人文件
  queryPersonLabel: function (params) {
    return request({
      url: "/techDocManage/docDocumentUser/listByLabel",
      method: "get",
      params,
    });
  },
  //个人文档-给文件设置标签
  setDocLabel: function (data) {
    return request({
      url: "/techDocManage/docLabel/editLabel",
      method: "post",
      data,
    });
  },
  // 个人文档-增加没有的标签
  insertLabel: function (data) {
    return request({
      url: "/techDocManage/docLabel/insert",
      method: "post",
      data,
    });
  },
  // 个人文档-查询单文件的标签
  queryFileLabel: function (params) {
    return request({
      url: "/techDocManage/docLabel/getLabelByDocId",
      method: "get",
      params,
    });
  },
  // 知识地图-卡片列表查询
  queryKnowledgeMap: function (params) {
    return request({
      url: "/techDocManage/result/getDocLabelByLabelAndDocName",
      method: "get",
      params,
    });
  },
  // 知识地图-根据标签搜索文件
  labelQueryFile: function (params) {
    return request({
      url: "/techDocManage/result/getDocResultByLabelAndDocName",
      method: "get",
      params,
    });
  },
};

export default labelMaintenanceService;
