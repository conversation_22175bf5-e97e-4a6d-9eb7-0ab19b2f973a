import request from '@/utils/request'

// 查询文件夹预制规则列表
export function listFolderPrecut(query) {
  return request({
    url: '/techDocManage/precutFolder/list',
    method: 'get',
    params: query
  })
}

// 查询文件夹预制规则详细
export function getFolderPrecut(id) {
  return request({
    url: '/techDocManage/precutFolder/' + id,
    method: 'get'
  })
}

// 新增文件夹预制规则
export function addFolderPrecut(data) {
  return request({
    url: '/techDocManage/precutFolder',
    method: 'post',
    data: data
  })
}

// 修改文件夹预制规则
export function updateFolderPrecut(data) {
  return request({
    url: '/techDocManage/precutFolder',
    method: 'put',
    data: data
  })
}

// 删除文件夹预制规则
export function delFolderPrecut(id) {
  return request({
    url: '/techDocManage/precutFolder/' + id,
    method: 'delete'
  })
}
