import request from '@/utils/request'

// 查询DocKnowledgeAiTool列表
export function listKnowledgeAiTool(query) {
  return request({
    url: '/techDocManage/knowledgeAiTool/list',
    method: 'get',
    params: query
  })
}

// 查询DocKnowledgeAiTool所有列表
export function listAllKnowledgeAiTool(query) {
  return request({
    url: '/techDocManage/knowledgeAiTool/listAll',
    method: 'get',
    params: query
  })
}

// 查询DocKnowledgeAiTool详细
export function getKnowledgeAiTool(id) {
  return request({
    url: '/techDocManage/knowledgeAiTool/getInfo/' + id,
    method: 'get'
  })
}

// 新增DocKnowledgeAiTool
export function addKnowledgeAiTool(data) {
  return request({
    url: '/techDocManage/knowledgeAiTool/add',
    method: 'post',
    data: data
  })
}

// 修改DocKnowledgeAiTool
export function updateKnowledgeAiTool(data) {
  return request({
    url: '/techDocManage/knowledgeAiTool/edit',
    method: 'put',
    data: data
  })
}

// 删除DocKnowledgeAiTool
export function delKnowledgeAiTool(id) {
  return request({
    url: '/techDocManage/knowledgeAiTool/remove/' + id,
    method: 'delete'
  })
}
