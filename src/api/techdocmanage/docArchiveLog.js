import request from '@/utils/request'

// 查询归档日志列表
export function listDocarchivelog(query) {
  return request({
    url: '/techDocManage/docArchiveLog/list',
    method: 'get',
    params: query
  })
}

// 查询归档日志详细
export function getDocarchivelog(id) {
  return request({
    url: '/techDocManage/docArchiveLog/' + id,
    method: 'get'
  })
}

// 新增归档日志
export function addDocarchivelog(data) {
  return request({
    url: '/techDocManage/docArchiveLog',
    method: 'post',
    data: data
  })
}

// 修改归档日志
export function updateDocarchivelog(data) {
  return request({
    url: '/techDocManage/docArchiveLog',
    method: 'put',
    data: data
  })
}

// 删除归档日志
export function delDocarchivelog(id) {
  return request({
    url: '/techDocManage/docArchiveLog/' + id,
    method: 'delete'
  })
}

// 个人文档-归档
export function placeOnFile(params) {
  return request({
    url: 'techDocManage/docDocumentUser/personalDocReArchive',
    params: params,
    method: 'get'
  })
}

// 获取归档记录下的所有文件ID
export function getArchiveFileId(id) {
  return request({
    url: '/techDocManage/docArchiveLog/getArchiveFileId/' + id,
    method: 'get'
  })
}
