import request from "@/utils/request";

const submitService = {
  //获取文件统计数量
  getStatistics: function (params) {
    return request({
      url: "safeKnowledge/period/file/item/statistics",
      params,
    });
  },
  //获取目录类别
  getCatalogCategory: function (params) {
    return request({
      url: "safeKnowledge/period/file/item/root/list",
      params,
    });
  },
  //获取文件分类
  getClasses: function (params) {
    return request({
      url: "safeKnowledge/option/classes",
      params,
    });
  },
  //获取文件项 级联
  getFileItem: function (params) {
      return request({
        url: "safeKnowledge/option/items",
        params,
      });
  },
  //获取文件项期数下拉
  periodsOption: function (params) {
    return request({
      url: "safeKnowledge/option/item/periods",
      params,
      method: "get",
    });
  },
  // 根据标签下载
  downloadSourceTagFile: function (params) {
    return request({
      url: "safeKnowledge/period/file/item/downLoadItemFile",
      method: "get",
      responseType: 'blob',
      params
    });
  },
  // 单个文件下载
  downloadSourceTemplateFile: function (data) {
    return request({
      url: "fileCenter/file/downloadFile",
      data,
      method: "post",
      responseType: "blob",
    });
  },
   // 文件成套提交-zip压缩文件
   zipFile: function (data) {
    return request({
      method: "post",
      url:'fileCenter/file/compressFile/',
      data,
    });
  },
  // 文档列表
  docList: function (params) {
      return request({
        url: "safeKnowledge/period/file/item/list",
        params,
      });
  },
  //获取文件项详情
  getFileItemDetail: function (id) {
    return request({
      url: "safeKnowledge/period/file/item/"+id,
      method: "get",
    });
  },
  //暂不更新保存
  notUpdatedSave: function (data) {
    return request({
      url: "safeKnowledge/period/file/item/notUpdated",
      method: "post",
      data
    });
  },
   // 上报与保存
   uploadSubmit: function (data) {
    return request({
      url: "safeKnowledge/period/file/item",
      method: "POST",
      data,
    });
  },
  //文件列表
  docViewList: function (id,params) {
    return request({
      url: "safeKnowledge/period/file/item/files/" + id,
      method: "get",
      params,
    });
  },
  //根据版本下载文件
  downloadSourceVersionFile: function (params) {
    return request({
      url: "safeKnowledge/period/file/item/downLoadFile",
      method: "get",
      params,
      responseType: "blob",
    });
  },
  //根据版本下载实时签名文件
  downLoadApproveFile: function (params) {
    return request({
      url: "safeKnowledge/period/file/item/downLoadApproveFile",
      method: "get",
      params,
      responseType: "blob",
    });
  },
  //根据文件项id下载实时签名文件
  downLoadApproveZipFile: function (params) {
    return request({
      url: "safeKnowledge/period/file/item/downLoadApproveZipFile",
      method: "get",
      params,
      responseType: "blob",
    });
  },
  //工作台列表
  homeViewList: function (data) {
    return request({
      url: "message/agenda/list",
      data,
      method: "POST",
    });
  },
  //下载列表
  docDownloadList: function (data) {
      return request({
        url: "safeKnowledge/period/file/item/downLoadList",
        method: "POST",
        data,
      });
  },
  //下载统计
  docDownloadStatistics: function (params) {
    return request({
      url: "safeKnowledge/period/file/item/downloadStatistics",
      params,
    });
  },





  //转办
  multiTaskTransfer: function (params) {
    return request({
      url: "flow/userTask/multiTaskTransfer",
      method: "GET",
      params,
    });
  },

  fileResolve: function (data) {
    return request({
      url: "fileCenter/api/resolve/event",
      method: "POST",
      data,
    });
  },




  // 文档总览过滤
  docViewFilter: function (params) {
    return request({
      url: "/directory/findCatalogCategory",
      params,
    });
  },
  // 文档总览
  // docViewList: function (params) {
  //   return request({
  //     url: "/docTask/taskTree",
  //     params,
  //   });
  // },
  // 补录时间选择
  recordTime: function (data) {
    return request({
      url: "/docTask/trapsTask",
      method:'POST',
      data,
    });
  },
  // 是否可清除
  isClean: function (params) {
    return request({
      url: "/docTask/cleanTask",
      params,
    });
  },
  // 文件分类
  fileType: function (params) {
    return request({
      url: "/doc/analysis/docClassification",
      params,
    });
  },
  // 文件项
  fileItem: function (params) {
    return request({
      url: "/doc/analysis/docItem",
      params,
    });
  },

  // 文件统计
  fileCount: function (params) {
    return request({
      url: "/docTask/statistics",
      params,
    });
  },
  // 获取文件上传页的数据
  fileUploadPageData: function (id,params) {
    return request({
      url: "/docTask/info/" + id,
      params
    });
  },
  // 校验暂不更新是否打开
  notUpdated: function (id) {
    return request({
      url: "/docTask/checkDir/" + id,
    });
  },
  // 暂不更新提交
  notUpdatedTemporarily: function (data) {
    return request({
      url: "/docTask/notUpdatedTemporarily",
      method: "POST",
      data,
    });
  },
  // 获取任务-期数列表
  tableData: function (params, path) {
    return request({
      url: path,
      params,
    });
  },
  // 文件列表详情----任务-期数列表
  listDetail: function (params, path) {
    return request({
      url: "/taskLogDetail/" + path,
      params,
    });
  },

  // 文件上报
  fileReport: function ( params) {
    return request({
      url:'/docReport/report',
      params,
    });
  },

  // 文件上传
  fileUpload:function (data) {
    return request({
      url: "/filecore/uploadPicture",
      method: "post",
      data
    });
  },
  // 审批配置查询
  querySignConfig: function (params) {
    return request({
      url:'/activiti/advancedConfig/byTaskIdAndProInstanceId',
      params,
    });
  },
  // 电子签章
  usersSignture:function (data) {
    return request({
      url: "/system/user/usersSignture",
      method: 'post',
      data
    });
  },
  // 获取文件列表
  getFileListUpload:function (id) {
    return request({
      url: "safeKnowledge/period/file/item/approvalFiles/"+id,
    });
  },
  // 获取文件列表
  getFileList:function (id) {
    return request({
      url: "safeKnowledge/period/file/item/approvalFilesByAgenda/"+id,
    });
  },
  // getFileList:function (params) {
  //   return request({
  //     url: "fileCenter/version/openFile",
  //     responseType: 'blob',
  //     params
  //   });
  // },
  // 提交电子签章
  submitSignInfo:function (data) {
    return request({
      url: '/fileCenter/sign/signPdf',
      method: 'post',
      data: data
    })
  },
  // 提交电子签章-base64图片
  submitSignInfoApp:function (data) {
    return request({
      url: '/fileCenter/sign/signPdfApp',
      method: 'post',
      data: data
    })
  },
  // 扫码发送base64图片
  sendBaseImg:function (data) {
    return request({
      url: '/fileCenter/version/uploadSignImage',
      method: 'post',
      data: data
    })
  },
  // 扫码发送签章信息
  sendSignInfo:function (params) {
    return request({
      url: '/safeKnowledge/approveSignature/add',
      method: 'get',
      params
    })
  },
  // 轮询查询时候电子签名
  querySign:function (params) {
    return request({
      url: '/safeKnowledge/approveSignature/getInfo',
      method: 'get',
      params
    })
  },
    // 通过轮询查询的versionId查询签名图片
    versionSignImg:function (params) {
      return request({
        url: '/fileCenter/version/openFile',
        method: 'get',
        params
      })
    },
    // 删除扫码签名的图片
    deleteSignImg:function (id) {
      return request({
        url: '/safeKnowledge/approveSignature/'+id,
        method: 'delete',
      })
    },
    // 删除当前登录人扫码签名的图片
    deleteUserSignImg:function (params) {
      return request({
        url: '/safeKnowledge/approveSignature/deleteAllSignature',
        method: 'get',
        params
      })
    },
    // 审批提交
    submitApproval:function (data) {
      return request({
        url: '/safeKnowledge/period/file/item/submitApprovalInfo',
        method: 'post',
        data: data
      })
    },
    // 审批提交app
    submitApprovalApp:function (data) {
      return request({
        url: '/safeKnowledge/period/file/item/submitApprovalInfoApp',
        method: 'post',
        data: data
      })
    },
    // 任务管理列表
    taskList: function (params) {
      return request({
        url: "flow/flowInfo/taskList",
        method: 'get',
        params
      });
    },
};

export default submitService;
