import request from "@/utils/request";

let knowledgeMapService = {
  // 知识地图卡片列表
  getKnowledgeMapDocList: function (params) {
    return request({
      url: "/techDocManage/document/getKnowledgeMapDocList",
      method: "get",
      params,
    });
  },
  // 知识地图卡片列表检索
  searchKnowledgeMapDocList: function (params) {
    return request({
      url: "/techDocManage/document/searchKnowledgeMapDocList",
      method: "get",
      params,
    });
  },
  // 文件类型列表
  getTypeList: function (params) {
    return request({
      url: "/techDocManage/document/getTypeList",
      method: "get",
      params,
    });
  },
  // 关键字列表
  getKeywordList: function (params) {
    return request({
      url: "/techDocManage/document/getKeywordList",
      method: "get",
      params,
    });
  },

  // 标签列表
  getLabelList: function (params) {
    return request({
      url: "/techDocManage/document/getLabelList",
      method: "get",
      params,
    });
  },
  // 检索框过滤
  searchKnowledgeMapList: function (params) {
    return request({
      url: "/techDocManage/document/searchKnowledgeMapList",
      method: "get",
      params,
    });
  },
  // 文档挖掘列表
  getKnowledgeMineList: function (params) {
    return request({
      url: "/techDocManage/document/getKnowledgeMineList",
      method: "get",
      params,
    });
  },
  // 文档挖掘列表详情
  getKnowledgeMineDocList: function (params) {
    return request({
      url: "/techDocManage/document/getKnowledgeMineDocList",
      method: "get",
      params,
    });
  },
};

export default knowledgeMapService;
