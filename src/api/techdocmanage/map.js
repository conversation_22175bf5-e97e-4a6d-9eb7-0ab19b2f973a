import MapboxLanguage from "@mapbox/mapbox-gl-language";
import html2canvas from "html2canvas";
import mapboxgl from "mapbox-gl"; // 引包方式二


// import { CompassControl } from "mapbox-map-tool";
// 定义打点marker
var markers
// 清除打点数组
var currentMarkers = []
// 定义路线生成属性
var isPlay = false
var counter = 0
var steps = 0
var newRouteGeoJson
var map
let aLength = 0;


var realRouteGeoJson = {
    'type': 'FeatureCollection',
    'features': [{
        'type': 'Feature',
        'geometry': {
            'type': 'LineString',
            'coordinates': []
        }
    }]
}

var animatePointGeoJson = {
    'type': 'FeatureCollection',
    'features': [{
        'type': 'Feature',
        'properties': {},
        'geometry': {
            'type': 'Point',
            'coordinates': []
        }
    }]
}
// 地图初始化
function initmap(basicMapbox) {
    // debugger
    mapboxgl.accessToken =
        "pk.eyJ1IjoiaGFuY2YiLCJhIjoiY2twbWl2czM4MjE1YzJ4bXdhc3FpNjBqbCJ9.c4Hdrv74dFKS8ZC7PS_iCw";
    // 英文标注转换为中文
    mapboxgl.setRTLTextPlugin(
        "https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-rtl-text/v0.1.0/mapbox-gl-rtl-text.js"
    );
    map = new mapboxgl.Map({
        container: basicMapbox,
        style: "mapbox://styles/mapbox/streets-v11",
        center: [116.95165597846272, 23.749095406704654],
        zoom: 5,
    });
    map.on("load", () => {
        createFly([116.95165597846272, 23.749095406704654]);
        // createFly([116.95165597846272, 23.749095406704654]);
        // 添加瓦片图
        createlay();
        // Navigation();  
    });
    // 比例尺
    // Scale();
    // 语言
    setLang();
    // 全屏
    fullscreen();
    // 定位
    fixedposition();
}
// 地图轨迹生成
async function addLine(routeGeoJson, ArealRouteGeoJson) {
    // svg格式右箭头
    var svgXML = `<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"> 
                  <path d="M529.6128 512L239.9232 222.4128 384.7168 77.5168 819.2 ************ 946.4832 239.9232 801.5872z" p-id="9085" fill="#ffffff"></path> 
              </svg>
              `;
    var svgBase64 =
        "data:image/svg+xml;base64," +
        window.btoa(unescape(encodeURIComponent(svgXML)));
    console.log("0初始化");
    var arrowIcon = new Image(20, 20);
    arrowIcon.src = svgBase64;
    arrowIcon.onload = function () {
        map.addImage("arrowIcon", arrowIcon);
        console.log(routeGeoJson, 'routeGeoJson')
        map.loadImage(
            "data:image/png;base64,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",
            function (error, carIcon) {
                if (carIcon) {
                    console.log("执行");
                    map.addImage("carIcon", carIcon);
                    setRouteData(routeGeoJson, ArealRouteGeoJson);
                }
            }
        );
    };
    // await stopClick();
}

// 获取轨迹数据
async function setRouteData(routeGeoJson, ArealRouteGeoJson) {

    console.log("1获取轨迹数据");
    animatePointGeoJson.features[0].geometry.coordinates = ArealRouteGeoJson.features[0].geometry.coordinates[0]
    aLength = routeGeoJson.features[0].geometry.coordinates.length;
    newRouteGeoJson = await resetRoute(ArealRouteGeoJson.features[0], 5000, 'kilometers')
    steps = newRouteGeoJson.geometry.coordinates.length
    addRoutelayer(routeGeoJson) // 添加轨迹线图层
    addRealRouteSource() // 添加实时轨迹线图层
    addArrowlayer(routeGeoJson) // 添加箭头图层
    addAnimatePointSource() // 添加动态点图层
};
 // 全屏
function fullscreen() {
    map.addControl(new mapboxgl.FullscreenControl());
}
 // 定位
function fixedposition() {
    map.addControl(
        new mapboxgl.GeolocateControl({
            positionOptions: {
                enableHighAccuracy: true,
            },
            trackUserLocation: true,
        })
    );
}

// 添加轨迹线图层
function addRoutelayer(routeGeoJson) {

    console.log("2添加轨迹线图层");
    map.addLayer({
        'id': 'routeLayer',
        'type': 'line',
        'source': {
            'type': 'geojson',
            'lineMetrics': true,
            'data': routeGeoJson
        },
        'paint': {
            'line-width': 10,
            'line-opacity': 1,
            'line-color': '#009EFF',
        }
    });
}

// 添加实时轨迹线
function addRealRouteSource() {


    map.addLayer({
        'id': 'realRouteLayer',
        'type': 'line',
        'source': {
            'type': 'geojson',
            'lineMetrics': true,
            'data': realRouteGeoJson
        },
        'paint': {
            'line-width': 10,
            'line-opacity': 1,
            'line-color': '#FF9900',
        }
    });
}

// 添加箭头图层
function addArrowlayer(routeGeoJson) {
    console.log("4添加箭头图层");
    map.addLayer({
        'id': 'arrowLayer',
        'type': 'symbol',
        'source': {
            'type': 'geojson',
            'data': routeGeoJson //轨迹geojson格式数据
        },
        'layout': {
            'symbol-placement': 'line',
            'symbol-spacing': 50, // 图标间隔，默认为250
            'icon-image': 'arrowIcon', //箭头图标
            'icon-size': 0.5
        }
    });
}

// 添加动态点图层
function addAnimatePointSource() {
    console.log("5添加动态点图层");
    map.addLayer({
        'id': 'animatePointLayer',
        'type': 'symbol',
        'source': {
            'type': 'geojson',
            'data': animatePointGeoJson
        },
        'layout': {
            'icon-image': 'carIcon', //require("../assets/imgs/an_on.png"),
            'icon-size': 0.5,
            'icon-rotate': ['get', 'bearing'],
            'icon-rotation-alignment': 'map',
            'icon-allow-overlap': true,
            'icon-ignore-placement': true
        }
    });

    animate()
}
// 路线动画
function animate() {
    console.log("6执行动画");
    if (counter >= steps) {
        return
    }
    var startPnt, endPnt
    if (counter == 0) {
        realRouteGeoJson.features[0].geometry.coordinates = []
        startPnt = newRouteGeoJson.geometry.coordinates[counter]
        endPnt = newRouteGeoJson.geometry.coordinates[counter + 1]
    } else if (counter !== 0) {
        startPnt = newRouteGeoJson.geometry.coordinates[counter - 1]
        endPnt = newRouteGeoJson.geometry.coordinates[counter]
    }

    animatePointGeoJson.features[0].properties.bearing = turf.bearing(
        turf.point(startPnt),
        turf.point(endPnt)
    ) - 90;
    animatePointGeoJson.features[0].geometry.coordinates = newRouteGeoJson.geometry.coordinates[counter];

    realRouteGeoJson.features[0].geometry.coordinates.push(animatePointGeoJson.features[0].geometry.coordinates)

    map.getSource('animatePointLayer').setData(animatePointGeoJson);
    map.getSource('realRouteLayer').setData(realRouteGeoJson);
    if (isPlay) {
        requestAnimationFrame(animate);
    }
    counter = counter + 1;
}
// 复原动画位置
async function resetRoute(route, nstep, units) {
    console.log("7重置路径");
    var newroute = {
        'type': 'Feature',
        'geometry': {
            'type': 'LineString',
            'coordinates': []
        }
    }
    var lineDistance = turf.lineDistance(route);
    var nDistance = lineDistance / nstep;
    for (let i = 0; i < aLength - 1; i++) {
        var from = turf.point(route.geometry.coordinates[i]);
        var to = turf.point(route.geometry.coordinates[i + 1]);
        let lDistance = turf.distance(from, to, {
            units: units
        });
        if (i == 0) {
            newroute.geometry.coordinates.push(route.geometry.coordinates[0])
        }
        if (lDistance > nDistance) {
            let rings = await lineMore(from, to, lDistance, nDistance, units)
            newroute.geometry.coordinates = newroute.geometry.coordinates.concat(rings)
        } else {
            newroute.geometry.coordinates.push(route.geometry.coordinates[i + 1])
        }
    }
    return newroute
}

function lineMore(from, to, distance, splitLength, units) {
    console.log("8多线");
    var step = parseInt(distance / splitLength)
    var leftLength = distance - step * splitLength
    var rings = []
    var route = turf.lineString([from.geometry.coordinates, to.geometry.coordinates])
    for (let i = 1; i <= step; i++) {
        let nlength = i * splitLength
        let pnt = turf.along(route, nlength, {
            units: units
        });
        rings.push(pnt.geometry.coordinates)
    }
    if (leftLength > 0) {
        rings.push(to.geometry.coordinates)
    }
    return rings
}
// 开始
function startClick() {
    if (!isPlay) {
        isPlay = true
        animate()
    }
}
// 暂停
function pauseClick() {
    isPlay = false
    animate()
}


// 复原
function stopClick() {
    isPlay = false
    counter = 0
    animate()
}

var markerID_addLayer, markerID_addSource, markerURL
var URL_image = [],
    ID_addLayer = [],
    ID_addSource = []
// marker打点功能
function addDOT(geojson, fun) {
    // debugger
    currentMarkers = []
    URL_image = []
    ID_addLayer = []
    ID_addSource = []
    console.log(geojson);
    for (const marker of geojson) {
        map.loadImage(
            // 图标路径
            marker.url,
            (error, image) => {
                if (error) throw error;
                // 图片地址
                markerURL = 'image' + marker.url
                URL_image.unshift(markerURL)
                // 图层id
                markerID_addLayer = 'points' + marker.id
                ID_addLayer.unshift(markerID_addLayer)
                // 图层源id
                markerID_addSource = 'point' + marker.id
                ID_addSource.unshift(markerID_addSource)

                // Add the image to the map style.
                map.addImage(markerURL, image);
                // console.log(image);
                // console.log(markerURL);
                // Add a data source containing one point feature.
                map.addSource(markerID_addSource, {
                    'type': 'geojson',
                    'data': {
                        'type': 'FeatureCollection',
                        'features': [{
                            'type': 'Feature',
                            'geometry': {
                                'type': 'Point',
                                'coordinates': marker.coordinates
                            }
                        }]
                    }
                });
                // console.log(marker.coordinates);
                // Add a layer to use the image to represent the data.
                map.addLayer({
                    'id': markerID_addLayer,
                    'type': 'symbol',
                    'source': markerID_addSource, // reference the data source
                    'layout': {
                        'icon-image': markerURL, // reference the image
                        'icon-size': 1
                    }
                });
            }
        );

        // 创建marker图层div
        const el = document.createElement("div");
        el.style.width = `50px`;
        el.style.height = `100px`;
        el.style.background = `red`;
        // 设置监听
        el.addEventListener("click", () => {
            var ids = marker.ids
            var id = marker.id
            var video_url = marker.videoUrl
            return fun(video_url, ids, id)
        });
        /*
         *@作者:  Zhangst
         *@時間: 2022-03-11 14:13:45
         *@描述: 鼠标经过打点提示框
         */
        let DeviceName = marker.name
        //创建一个弹出窗口
        var popup = new mapboxgl.Popup({
            closeButton: false,
            closeOnClick: false
        });
        el.addEventListener("mousemove", () => {
            // debugger
            console.log("鼠标经过");
            popup.setLngLat(marker.coordinates)
                .setHTML("<strong>" + DeviceName + "</strong>")
                .addTo(map);
        })
        el.addEventListener("mouseleave", () => {
            popup.remove()
        })
        markers = new mapboxgl.Marker(el)
            .setLngLat(marker.coordinates)
            .addTo(map);
        // 添加到markers数组遍历删除
        currentMarkers.push(markers);
        // console.log("markers" + markers);
    }
}

function addOneDOT(geojson, fun) {
        for (const marker of geojson) {
            map.loadImage(
                // 'https://docs.mapbox.com/mapbox-gl-js/assets/cat.png',
                marker.url,
                (error, image) => {
                    if (error) throw error;
                    // 图片地址
                    markerURL = 'image' + marker.url
                    URL_image.unshift(markerURL)
                    // 图层id
                    markerID_addLayer = 'points' + marker.id
                    ID_addLayer.unshift(markerID_addLayer)
                    // 图层源id
                    markerID_addSource = 'point' + marker.id
                    ID_addSource.unshift(markerID_addSource)

                    // Add the image to the map style.
                    map.addImage(markerURL, image);
                    // console.log(image);
                    // console.log(markerURL);
                    // Add a data source containing one point feature.
                    map.addSource(markerID_addSource, {
                        'type': 'geojson',
                        'data': {
                            'type': 'FeatureCollection',
                            'features': [{
                                'type': 'Feature',
                                'geometry': {
                                    'type': 'Point',
                                    'coordinates': marker.coordinates
                                }
                            }]
                        }
                    });
                    // console.log(marker.coordinates);
                    // Add a layer to use the image to represent the data.
                    map.addLayer({
                        'id': markerID_addLayer,
                        'type': 'symbol',
                        'source': markerID_addSource, // reference the data source
                        'layout': {
                            'icon-image': markerURL, // reference the image
                            'icon-size': 0.25
                        }
                    });
                }
            );
            // console.log(marker,'111111111111');
            // 创建marker图层div
            const el = document.createElement("div");
            el.style.width = `50px`;
            el.style.height = `100px`;
            // el.style.background = `blue`;
            // el.id = "marker";
            // 设置监听
            // debugger
            let DeviceName = marker.name
            var popup = new mapboxgl.Popup({
                closeButton: false,
                closeOnClick: false
            });
            el.addEventListener("mousemove", () => {
                // debugger
                // console.log("鼠标经过");
                popup.setLngLat(marker.coordinates)
                    .setHTML("<strong>" + DeviceName + "</strong>")
                    .addTo(map);
            })
            el.addEventListener("mouseleave", () => {
                popup.remove()
            })
            el.addEventListener("click", () => {
                var ids = marker.ids
                var id = marker.id
                var video_url = marker.videoUrl
                // console.log(ids, id, video_url);
                return fun(video_url, ids, id)
            });
            markers = new mapboxgl.Marker(el)
                .setLngLat(marker.coordinates)
                .addTo(map);
            // 添加到markers数组遍历删除
            currentMarkers.push(markers);
            // console.log(currentMarkers)
        }
}

// 地图热力图
function hotDot(hotdata) {
    // console.log("热力打点");
    const size = 200;

    // This implements `StyleImageInterface`
    // to draw a pulsing dot icon on the map.
    const pulsingDot = {
        width: size,
        height: size,
        data: new Uint8Array(size * size * 4),

        // When the layer is added to the map,
        // get the rendering context for the map canvas.
        onAdd: function () {
            const canvas = document.createElement('canvas');
            // console.log(canvas);
            canvas.width = this.width;
            canvas.height = this.height;
            this.context = canvas.getContext('2d');
        },

        // Call once before every frame where the icon will be used.
        render: function () {
            const duration = 1000;
            const t = (performance.now() % duration) / duration;

            const radius = (size / 2) * 0.3;
            const outerRadius = (size / 2) * 0.7 * t + radius;
            const context = this.context;

            // Draw the outer circle.
            context.clearRect(0, 0, this.width, this.height);
            context.beginPath();
            context.arc(
                this.width / 2,
                this.height / 2,
                outerRadius,
                0,
                Math.PI * 2
            );
            context.fillStyle = `rgba(255, 200, 200, ${1 - t})`;
            context.fill();

            // Draw the inner circle.
            context.beginPath();
            context.arc(
                this.width / 2,
                this.height / 2,
                radius,
                0,
                Math.PI * 2
            );
            context.fillStyle = 'rgba(255, 100, 100, 1)';
            context.strokeStyle = 'white';
            context.lineWidth = 2 + 4 * (1 - t);
            context.fill();
            context.stroke();

            // Update this image's data with data from the canvas.
            this.data = context.getImageData(
                0,
                0,
                this.width,
                this.height
            ).data;

            // Continuously repaint the map, resulting
            // in the smooth animation of the dot.
            map.triggerRepaint();

            // Return `true` to let the map know that the image was updated.
            return true;
        }
    };
    map.addImage('pulsing-dot', pulsingDot, {
        pixelRatio: 2
    });
    map.addSource('dot-point', {
        'type': 'geojson',
        'data': {
            'type': 'FeatureCollection',
            'features': hotdata
        }
    });
    map.addLayer({
        'id': 'layer-with-pulsing-dot',
        'type': 'symbol',
        'source': 'dot-point',
        'layout': {
            'icon-image': 'pulsing-dot'
        }
    });

}
// 截图和打印地图
function Screenshot(name, fun) {
    let img = ""
    html2canvas(document.querySelector("#map"), {
        width: 500, // TODO 截屏按照1920*1080分辨率下的预览窗口宽高
        height: 600,
        x: 250,
        y: 100,
        useCORS: true,
        allowTaint: true,
    }).then((canvas) => {
        var context = canvas.getContext("2d");
        context.mozImageSmoothingEnabled = false;
        context.webkitImageSmoothingEnabled = false;
        context.msImageSmoothingEnabled = false;
        context.imageSmoothingEnabled = false;
        // 转成图片，生成图片地址
        const a = document.createElement("a");
        // 地图名字
        let fileName = name;
        a.href = canvas.toDataURL("image/png");
        // a.download = `${Date.now()}`;
        a.download = fileName;
        a.click();
        // 获取生成的图片的url
        img = canvas.toDataURL("png");
        return fun(img)
    });
}

// 清除打点marker
function removeDOT(geojson) {
    // debugger
    // console.log(URL_image, "////////////////");
    // console.log(ID_addLayer, "////////////////");
    // console.log(ID_addSource, "////////////////");
    // console.log(currentMarkers, "////////////////");
    if (currentMarkers !== null) {
        for (let i = currentMarkers.length - 1; i >= 0; i--) {
            currentMarkers[i].remove();
        }
    }
    if (URL_image !== null) {
        for (let i = 0; i < URL_image.length; i++) {
            if (map.hasImage(URL_image[i])) map.removeImage(URL_image[i]);
        }
    }
    if (ID_addLayer !== null) {
        for (let i = 0; i < ID_addLayer.length; i++) {
            if (map.getLayer(ID_addLayer[i])) map.removeLayer(ID_addLayer[i]);
        }
    }
    if (ID_addSource !== null) {
        for (let i = 0; i < ID_addSource.length; i++) {
            if (map.getSource(ID_addSource[i])) map.removeSource(ID_addSource[i]);
        }
    }
    currentMarkers = []
    URL_image = []
    ID_addLayer = []
    ID_addSource = []
}
// 删除地图轨迹
function delLine() {
    // console.log("删除");
    // 删除轨迹线图层
    if (map.getLayer("routeLayer")) {
        map.removeLayer("routeLayer");
        map.removeSource("routeLayer");
    }
    // 删除实时轨迹线
    if (map.getLayer("realRouteLayer")) {
        map.removeLayer("realRouteLayer");
        map.removeSource("realRouteLayer");
    }
    // 删除箭头图层
    if (map.getLayer("arrowLayer")) {
        map.removeLayer("arrowLayer");
        map.removeSource("arrowLayer");
    }
    // 删除动态点图层
    if (map.getLayer("animatePointLayer")) {
        map.removeLayer("animatePointLayer");
        map.removeSource("animatePointLayer");
    }
    // 删除热力点
    if (map.getLayer("layer-with-pulsing-dot")) {
        map.removeLayer("layer-with-pulsing-dot");
        map.removeSource("dot-point");
    }
    // 删除汽车图片
    if (map.hasImage("carIcon")) map.removeImage("carIcon");
}
// 移至某个点
function createFly(lnglat, zoom) {
    map.flyTo({
        center: lnglat,
        zoom: zoom || 15.87470733275043,
        speed: 1,
        curve: 2,
    });
}

// 设置语言
function setLang() {
    var language = new MapboxLanguage({
        defaultLanguage: "zh-Hans",
    });
    map.addControl(language);
}
// 地图比例尺
function Scale() {
    // 比例尺
    var scale = new mapboxgl.ScaleControl({
        maxWidth: 80,
        unit: "imperial",
    });
    map.addControl(scale);
    scale.setUnit("metric");
}
// 地图导航Navigation 
function Navigation(params) {
    map.addControl(new mapboxgl.NavigationControl());
}

// pixels the map pans when the up or down arrow is clicked
const deltaDistance = 100;

// degrees the map rotates when the left or right arrow is clicked
const deltaDegrees = 25;

function easing(t) {
    return t * (2 - t);
}

function up() {
    map.panBy([0, -deltaDistance], {
        easing: easing
    });
}

function down() {
    map.panBy([0, deltaDistance], {
        easing: easing
    });
}

function left() {
    // map.easeTo({
    //     bearing: map.getBearing() - deltaDegrees,
    //     easing: easing
    // });
    map.panBy([-deltaDistance, 0], {
        easing: easing
    });
}

function right() {
    map.panBy([deltaDistance, 0], {
        easing: easing
    });
}

function RemoveControl() {
    map.removeControl(control)
}
// 添加切片图层
function createlay() {
    map.addLayer({
        //添加别墅的geojson图层进行三维的显示
        id: "qiepianLayer", //图层名称
        type: "raster", //显示类型为栅格
        source: {
            type: "raster", //数据源为类型为raster
            tiles: [
                // 'http://117.33.234.27:9095/arcgis/rest/services/zhyq/M15/MapServer/export?dpi=96&transparent=true&format=png8&layers=&bbox={bbox-epsg-3857}&f=image&bboxSR=102100&imageSR=102100'
                "http://120.78.205.211:6080/arcgis/rest/services/zhyq/MZ194/MapServer/export?dpi=96&transparent=true&format=png8&layers=&bbox={bbox-epsg-3857}&f=image&bboxSR=102100&imageSR=102100",
            ],
            tileSize: 256,
            //切片请求的范围，不在这个范围的话  就不进行切片请求
            // "bounds": [106.61304, -85, 120, 85]
        },
    });
}
var poi = []
// 获取经纬度 getLnt
function getLngLat() {
    map.on("click", function (e) {
        if (e.defaultPrevented) return;
        console.log("点击", e.lngLat);
        poi.push(e.lngLat);
        // console.log(poi);

        for (let i = 0; i < poi.length; i++) {
            console.log("[" + poi[i].lng + "," + poi[i].lat + "]");
        }
        // console.log(that.poi);
    });
}
export default {
    addLine,
    addDOT,
    hotDot,
    Screenshot,
    getLngLat,
    startClick,
    pauseClick,
    stopClick,
    initmap,
    removeDOT,
    delLine,
    addOneDOT,
    createFly,
    Scale,
    up,
    down,
    left,
    right,
    RemoveControl,
    Navigation
}