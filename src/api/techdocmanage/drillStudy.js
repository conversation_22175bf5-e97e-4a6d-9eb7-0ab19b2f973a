import request from '@/utils/request'

// 查询培训学习列表
export function listDrillStudy(query) {
  return request({
    url: '/techDocManage/drillStudy/list',
    method: 'get',
    params: query
  })
}

// 查询培训学习详细
export function getDrillStudy(id) {
  return request({
    url: '/techDocManage/drillStudy/' + id,
    method: 'get'
  })
}

// 新增培训学习
export function addDrillStudy(data) {
  return request({
    url: '/techDocManage/drillStudy',
    method: 'post',
    data: data
  })
}

// 修改培训学习
export function updateDrillStudy(data) {
  return request({
    url: '/techDocManage/drillStudy',
    method: 'put',
    data: data
  })
}

// 删除培训学习
export function delDrillStudy(id) {
  return request({
    url: '/techDocManage/drillStudy/' + id,
    method: 'delete'
  })
}

// 查询个人培训学习列表
export function myOnlieStudylist(query) {
  return request({
    url: '/techDocManage/drillStudy/myOnlieStudylist',
    method: 'get',
    params: query
  })
}

// 热门课件
export function popularLearnlist() {
  return request({
    url: '/techDocManage/drillStudy/popularLearn',
    method: 'get',
  })
}

// 数据统计
export function myStudyStatistics() {
  return request({
    url: '/techDocManage/drillStudy/myStudyStatistics',
    method: 'get',
  })
}

// 笔记记录
export function myNotesList() {
  return request({
    url: '/techDocManage/drillStudy/myNotesList',
    method: 'get',
  })
}

// 参与评论
export function myDiscussList() {
  return request({
    url: '/techDocManage/drillStudy/myDiscussList',
    method: 'get',
  })
}

// 学习分析
export function analysisList(params) {
  return request({
    url: '/techDocManage/drillStudy/analysisList/'+params,
    method: 'get',
  })
}