import request from '@/utils/request'

// 查询转换pdf失败列表
export function listDocPdfConvert(query) {
  return request({
    url: '/filecore/office/list',
    method: 'get',
    params: query
  })
}

// 重新转换PDF
export function ConvertAgain(query) {
  return request({
    url: '/filecore/office/convertAgain',
    method: 'get',
    params: query
  })
}
// 向量转换
export function ConvertVector(query) {
  return request({
    url: 'techDocManage/result/getUnconvertedVectorFileList',
    method: 'get',
    params: query
  })
}
