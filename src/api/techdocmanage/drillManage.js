import request from '@/utils/request'

// 查询培训资料管理列表
export function listDrillManage(query) {
  return request({
    url: '/techDocManage/drillManage/list',
    method: 'get',
    params: query
  })
}

// 在线学习列表
export function listDrillOnlineStudy(query) {
  return request({
    url: '/techDocManage/drillManage/studyList',
    method: 'get',
    params: query
  })
}

// 查询培训资料管理详细
export function getDrillManage(id) {
  return request({
    url: '/techDocManage/drillManage/' + id,
    method: 'get'
  })
}


// 新增培训资料管理
export function addDrillManage(data) {
  return request({
    url: '/techDocManage/drillManage',
    method: 'post',
    data: data
  })
}

// 修改培训资料管理
export function updateDrillManage(data) {
  return request({
    url: '/techDocManage/drillManage',
    method: 'put',
    data: data
  })
}

// 删除培训资料管理
export function delDrillManage(id) {
  return request({
    url: '/techDocManage/drillManage/' + id,
    method: 'delete'
  })
}

// 发布培训资料管理
export function publishDrillManage(status,ids) {
  return request({
    url: '/techDocManage/drillManage/publish/' + status+'/'+ids,
    method: 'get'
  })
}


// 附件上传
export function uploadFile(params){
  return request({
    url:"/filecore/upload",
    method:"post",
    data:params
  })
}