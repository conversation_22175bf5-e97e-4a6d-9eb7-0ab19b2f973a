import request from '@/utils/request'

// 查询文件上传任务列表
export function listDocumentUploadTask(query) {
  return request({
    url: '/filecore/documentUploadTask/list',
    method: 'get',
    params: query
  })
}

// 查询文件上传任务所有列表
export function listAllDocumentUploadTask(query) {
  return request({
    url: '/filecore/documentUploadTask/listAll',
    method: 'get',
    params: query
  })
}

// 查询文件上传任务详细
export function getDocumentUploadTask(id) {
  return request({
    url: '/filecore/documentUploadTask/getInfo/' + id,
    method: 'get'
  })
}

// 新增文件上传任务
export function addDocumentUploadTask(data) {
  return request({
    url: '/filecore/documentUploadTask/add',
    method: 'post',
    data: data
  })
}

// 修改文件上传任务
export function updateDocumentUploadTask(data) {
  return request({
    url: '/filecore/documentUploadTask/edit',
    method: 'put',
    data: data
  })
}

// 删除文件上传任务
export function delDocumentUploadTask(id) {
  return request({
    url: '/filecore/documentUploadTask/removeDoc/' + id,
    method: 'delete'
  })
}
// 删除文件下载任务
export function delDocumentDownloadTask(id) {
  return request({
    url: '/filecore/documentUploadTask/remove/' + id,
    method: 'delete'
  })
}


// 任务重做重新上传
export function reUpLoadByZip(data) {
  return request({
    url: '/filecore/documentUploadTask/reUpLoadByZip',
    method: 'post',
    data: data
  })
}
   //文件夹分享
export function addShareDoc(query) {
    return request({
      url: "filecore/documentUploadTask/addShareDoc",
      params: query,
      method: "get",
    });
}