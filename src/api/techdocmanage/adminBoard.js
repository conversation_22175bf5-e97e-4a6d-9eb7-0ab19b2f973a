import request from '@/utils/request'

// 热门文档列表
export function HotDocuments(data) {
  return request({
    url: '/techDocManage/docManager/hotDocuments',
    method: 'post',
    data: data
  })
}

// 热门文档横柱状图
export function HotDocumentsHistogram(data) {
  return request({
    url: '/techDocManage/docManager/hotDocumentsHistogram',
    method: 'post',
    data: data
  })
}

// 归档类型分析
export function FileTypeChart(data) {
  return request({
    url: '/techDocManage/docManager/fileTypeChart',
    method: 'post',
    data: data
  })
}

// 查询磁盘大小
export function DiskSize(query) {
  return request({
    url: '/techDocManage/docManager/diskSize',
    method: 'get',
	params: query
  })
}

// 查询文件的容量、大小
export function DocumentClassify(query) {
  return request({
    url: '/techDocManage/docManager/documentClassify',
    method: 'get',
	params: query
  })
}


// 热门文档面积折线图
export function HotDocumentsAreaChart(data) {
  return request({
    url: '/techDocManage/docManager/hotDocumentsAreaChart',
    method: 'post',
    data: data
  })
}

// 热门文档折线图
export function HotDocumentsLineChart(data) {
  return request({
    url: '/techDocManage/docManager/hotDocumentsLineChart',
    method: 'post',
    data: data
  })
}

