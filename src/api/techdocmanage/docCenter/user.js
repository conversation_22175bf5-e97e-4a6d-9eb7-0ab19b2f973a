import request from "@/utils/request";

let userService = {
  // 文档目录树结构-左侧树结构
  treeUserList: function (params) {
    return request({
      url: "techDocManage/directory/fileMountListTree",
      params: params,
      method: "get",
    });
  },
  //公共文档列表数据
  qListPublic: function (params) {
    return request({
      url: "techDocManage/result/listInfo",
      params: params,
      method: "get",
    });
  },
  //成果文档-列表数据
  qList: function (params) {
    return request({
      url: "techDocManage/result/list",
      params: params,
      method: "get",
    });
  },
  // 文件预览
  previewFile: function (params) {
    return request({
      url: "filecore/openFile",
      params: params,
      method: "get",
    });
  },
  // 成果文档-文件详情
  fileDetail: function (id) {
    return request({
      url: "techDocManage/result/" + id,
      method: "get",
    });
  },
  // 成果文档-文件操作日志
  fileLog: function (params) {
    return request({
      url: "filecore/docLog/list",
      params: params,
      method: "get",
    });
  },
  // 成果文档-文件的历史版本文件
  fileHistory: function (params) {
    return request({
      url: "filecore/docVersion/list",
      params: params,
      method: "get",
    });
  },
  // 成果文档-智能提取
  intelligentExtractionList: function (params) {
    return request({
      url: "techDocManage/content/getInfoByDocId",
      params: params,
      method: "get",
    });
  },
  // 成果文档-历史版本下载
  fileHistoryDownload: function (params) {
    return request({
      url: "filecore/downloadVersionFilePdf",
      params: params,
      method: "get",
    });
  },
  // 搜素
  selectList: function (params) {
    return request({
      url: "/fileFormation/userfile/selectlist",
      params: params,
      method: "get",
    });
  },
  // 文档下载
  downloadFile: function (params) {
    return request({
      // url: "/filecore/download",
      url: "/filecore/downloadSourceFile",
      params: params,
      method: "get",
      responseType: "blob",
    });
  },
  // 原文件下载
  downloadSourceFile: function (params) {
    return request({
      url: "/filecore/downloadSourceFile",
      params: params,
      method: "get",
      responseType: "blob",
    });
  },
  // 历史文件pdf下载
  downloadVersionFilePdf: function (params) {
    return request({
      url: "/filecore/downloadVersionFilePdf",
      params: params,
      method: "get",
      responseType: "blob",
    });
  },
  // 历史文件原文件下载
  downloadVersionSourceFile: function (params) {
    return request({
      url: "/filecore/downloadVersionSourceFile",
      params: params,
      method: "get",
      responseType: "blob",
    });
  },
  // 成果文档-文件收藏
  collectFile: function (params) {
    return request({
      url: "techDocManage/collect/addCollect/" + params,
      method: "post",
    });
  },
  // 成果文档-文档分享
  shareFile: function (params) {
    return request({
      url: "techDocManage/docShare",
      data: params,
      method: "post",
    });
  },
  // 部门树结构
  deptTreeList: function (params) {
    return request({
      url: "/system/tree/deptTree",
      params: params,
      method: "get",
    });
  },
  // 成果文档-单文件树形结构
  singleFileTree: function (ids) {
    return request({
      url: "techDocManage/docauth/getInfoByFileIds/" + ids,
      method: "get",
    });
  },
  // 成果文档-部门树结构-新接口
  deptTreeListNew: function (params) {
    return request({
      url: "/system/tree/deptTree/document",
      params: params,
      method: "get",
    });
  },
  // 岗位树结构
  optionSelect: function (params) {
    return request({
      url: "/system/tree/postTree",
      params: params,
      method: "get",
    });
  },
  // 成果文档-岗位树结构-新接口
  optionSelectNew: function (params) {
    return request({
      url: "/system/tree/postTree/document",
      params: params,
      method: "get",
    });
  },
  // 成果文档-文档移动
  moveFile: function (params) {
    return request({
      url: "techDocManage/result/moveFile",
      params: params,
      method: "get",
    });
  },
  // 上传文件-公共接口
  uploadFile: function (params) {
    return request({
      url: "filecore/uploads",
      data: params,
      method: "post",
    });
  },
  
  // 打包上传
  uploadByZip: function (params) {
    return request({
      url: "/filecore/documentUploadTask/uploadByZip",
      data: params,
      method: "post",
    });
  },
  
  // 上传大纲
  uploadSynopsisFile: function (params) {
    return request({
      url: "assisted/repRecord/uploadSynopsisFile",
      data: params,
      method: "post",
    });
  },

  // 文件上传-表单提交
  addFile: function (params) {
    return request({
      url: "techDocManage/result",
      data: params,
      method: "post",
    });
  },
  // 成果文档-业务目录新增
  addBusinessDirectory: function (params) {
    return request({
      url: "techDocManage/result/BusinessDirectory",
      data: params,
      method: "post",
    });
  },
  // 成果文档-业务目录修改 //文件发布
  updateBusinessDirectory: function (params) {
    return request({
      url: "techDocManage/result",
      data: params,
      method: "put",
    });
  },
  // 成果文档- //打包下载
  packageDownloadDirectory: function (params) {
    return request({
      url: "filecore/documentUploadTask/downloadSourceFile",
      params: params,
      method: "get",
    });
  },
  // 成果文档-业务目录修改 //文件夹发布
  updatePublishFolder: function (params) {
    return request({
      url: "techDocManage/result/publishFolder",
      data: params,
      method: "post",
    });
  },
  // 成果文档-文档删除
  deleteFile: function (ids) {
    return request({
      url: "filecore/document/" + ids,
      method: "delete",
    });
  },
  // 成果文档-文件夹删除
  deleteDirectory: function (ids) {
    return request({
      url: "/techDocManage/result/" + ids,
      method: "delete",
    });
  },
  // 成果文档-业务回收站列表
  recycleList: function (params) {
    return request({
      url: "techDocManage/result/getBusinessRecycleList",
      params: params,
      method: "get",
    });
  },
  // 成果文档-业务回收站文件还原
  recoverFile: function (id) {
    return request({
      url: "filecore/recycleBin/reduction/" + id,
      method: "get",
    });
  },
  // 成果文档-业务回收站清空
  clearFile: function (ids) {
    return request({
      url: "techDocManage/result/businessRecycleClear/" + ids,
      method: "DELETE",
    });
  },
  // 文档查询-文件列表
  searchFile: function (params) {
    return request({
      url: "techDocManage/result/listAll",
      params: params,
      method: "get",
    });
  },
  // 文档查询-点击文件夹
  searchFileByDirectory: function (params) {
    return request({
      url: "techDocManage/result/listAllByDirectory",
      params: params,
      method: "get",
    });
  },
  // 个人文档-新增文件夹
  addFiledirectory: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/addFolder",
      data: params,
      method: "post",
    });
  },
  // 个人文档-修改文件夹
  updateFiledirectory: function (params) {
    return request({
      url: "techDocManage/docDocumentUser",
      data: params,
      method: "put",
    });
  },
  // 个人文档-文件夹树结构
  personalTreeList: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/folderTree",
      params: params,
      method: "get",
    });
  },
  // 个人文档-文件列表
  personalFileList: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/list",
      params: params,
      method: "get",
    });
  },
  // 个人文档-文件上传
  personalFileUpload: function (params) {
    return request({
      url: "techDocManage/docDocumentUser",
      data: params,
      method: "post",
    });
  },

  // 个人文档-文件上传
  personalFileUploadByBrainPower: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/brainPower",
      data: params,
      method: "post",
    });
  },

  // 手动编写上传文件
  personalFileUploadByHand: function (params) {
    return request({
      url: "assisted/repRecord/handWrite",
      data: params,
      method: "post",
    });
  },

  // 个人文档-文件删除
  personalFileDelete: function (ids) {
    return request({
      url: "filecore/document/" + ids,
      method: "DELETE",
    });
  },
  // 个人文档-文件夹删除
  personalFolderDelete: function (ids) {
    return request({
      url: "/techDocManage/docDocumentUser/" + ids,
      method: "DELETE",
    });
  },
  // 个人文档-文件移交
  personalFileMoves: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/transfer",
      params: params,
      method: "get",
    });
  },
  // 个人文档-回收站列表
  personalRecycleList: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/userRecycleList",
      params: params,
      method: "get",
    });
  },
  // 个人文档-回收站文件还原
  personalRecycleReduction: function (ids) {
    return request({
      url: "/filecore/recycleBin/reduction/" + ids,
      method: "get",
    });
  },
  // 个人文档-回收站清除
  personalRecycleClear: function (ids) {
    return request({
      url: "techDocManage/docDocumentUser/userRecycleClear/" + ids,
      method: "DELETE",
    });
  },
  // 个人文档-我的收藏
  personalCollection: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/myCollection",
      params: params,
      method: "get",
    });
  },
  // 个人文档-最近浏览-收藏
  PersonalDocumentsRecentBrowsingFavorites: function (params) {
    return request({
      url: "techDocManage/collect/browsingRecords/",
      data: params,
      method: "post",
    });
  },
  // 个人文档-最近浏览-移除收藏
  PersonalDocumentsRecentBrowsingRemoveFavorites: function (ids) {
    return request({
      url: "techDocManage/collect/removeDocId/" + ids,
      method: "DELETE",
    });
  },
  // 个人文档-我的分享
  personalShare: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/Myshare",
      params: params,
      method: "get",
    });
  },
  // 个人文档-文档分享
  personalShareFile: function (params) {
    return request({
      url: "techDocManage/docShare/insert",
      data: params,
      method: "post",
    });
  },
  // 个人文档-分享详情
  PersonalDocumentsShareDetails: function (params) {
    return request({
      url: "techDocManage/docShare/list",
      params: params,
      method: "get",
    });
  },
  // 个人文档-分享给我
  personalShareToMe: function (params) {
    return request({
      url: "techDocManage/docShare/shareWithMy",
      params: params,
      method: "get",
    });
  },
  // 个人文档-最近浏览
  personalRecent: function (params) {
    return request({
      url: "filecore/docLog/listOpenInfo",
      params: params,
      method: "get",
    });
  },
  // 个人文档-归档
  personalArchive: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/placeOnFile",
      params: params,
      method: "get",
    });
  },
  // 个人文档-归档列表
  personalArchiveList: function (params) {
    return request({
      url: "techDocManage/docArchiveConfig/list",
      params: params,
      method: "get",
    });
  },
  // 个人文档-归档成果表
  personalArchiveResult: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/placeOnFileList",
      params: params,
      method: "get",
    });
  },
  // 个人文档-删除我的分享
  personalShareDelete: function (ids) {
    return request({
      url: "techDocManage/docShare/" + ids,
      method: "DELETE",
    });
  },
  // 个人文档-文件所分享的人
  personalShareUser: function (params) {
    return request({
      url: "techDocManage/docShare/FileId/",
      params: params,
      method: "get",
    });
  },
  // 个人文档-收藏添加到个人文件夹
  personalCollectionAdd: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/copyMyCollection",
      data: params,
      method: "post",
    });
  },
  // 个人文档-删除收藏
  personalCollectionDelete: function (ids) {
    return request({
      url: "techDocManage/collect/removeFileId/" + ids,
      method: "DELETE",
    });
  },
  // 个人文档-文档详情
  PersonalDocumentsDetails: function (id) {
    return request({
      url: "techDocManage/docDocumentUser/" + id,
      method: "get",
    });
  },
  // 个人文档-文件移动
  personalFileMove: function (params) {
    return request({
      url: "techDocManage/docDocumentUser/moveUserFile",
      params: params,
      method: "get",
    });
  },
  // 待办列表查询
  toDoListQuery: function (params) {
    return request({
      url: "procedure/process/backlog",
      params: params,
      method: "get",
    });
  },
  // 待办任务详情
  toDoTasksDetails: function (id) {
    return request({
      url: "procedure/process/processInfo/" + id,
      method: "get",
    });
  },
  // 待办任务通过
  toDoTasksPassed: function (params) {
    return request({
      url: "procedure/process/agree",
      data: params,
      method: "post",
    });
  },
  // 待办任务驳回
  toDoTasksReject: function (params) {
    return request({
      url: "procedure/process/reject/",
      data: params,
      method: "post",
    });
  },
  // 已办列表查询
  doneListQuery: function (params) {
    return request({
      url: "procedure/process/havedone",
      params: params,
      method: "get",
    });
  },
  // 已办任务详情
  donetaskDetails: function (id) {
    return request({
      url: "procedure/process/processInfo/" + id,
      method: "get",
    });
  },
  // 查询问档详情信息
  queryFileDetail: function (id) {
    return request({
      url: "filecore/document/" + id,
      method: "get",
    });
  },

  getEntityByDocId: function (params) {
    return request({
      url: "assisted/content/getEntityByDocId",
      params: params,
      method: "get",
    });
  },

  updateRepLabelContent: function (params) {
    return request({
      url: "assisted/content/edit",
      data: params,
      method: "put",
    });
  },

  handWriteNewVersion: function (params) {
    return request({
      url: "assisted/repRecord/handWriteNewVersion",
      data: params,
      method: "post",
    });
  },
  getImgUrl: function (id) {
    return request({
      url: "/filecore/docVersion/url/" + id,
      method: "get",
    });
  },

  directoryDiff: function (data) {
    return request({
      url: "/filecore/docVersion/directoryDiff",
      method: "post",
      data: data,
    });
  },
  singleDirectory: function (params) {
    return request({
      url: "/filecore/docVersion/getSynopsis",
      method: "get",
      params,
    });
  },
  directoryDiffVerify: function (data) {
    return request({
      url: "/filecore/docVersion/directoryDiffVerify",
      method: "get",
      params: data,
    });
  },

  getFileList: function (params) {
    return request({
      url: "/techDocManage/result/getDocumentResultList",
      params,
    });
  },
  getFileTags: function (params) {
    return request({
      url: "/techDocManage/content/getInfoByDocId",
      params,
    });
  },

  keepDocument: function (data) {
    return request({
      url: "/filecore/keepDocument",
      method: "get",
      params: data,
    });
  },
  // 智能核稿
  aiReviewDraft: function (params) {
    return request({
      url: "/filecore/addByDocIdAndResultId",
      params,
    });
  },
  // 搜索文件
  queryFileList: function (params) {
    return request({
      url: "/techDocManage/result/getOverdueList",
      params,
    });
  },
  // 通过id上传文件
  uploadFilesById: function (params) {
    return request({
      url: "/filecore/uploadByDocId",
      data: params,
      method: "post",
    });
  },
  


  // 大纲导出
  directoryDiffExport: function (params) {
    return request({
      url: "/filecore/docVersion/directoryDiffExport",
      method: "get",
      params,
      responseType: "blob",
    });
  },

  // 通过id上传文件
  checkFileUnique: function (data) {
    console.log('res44444444', data);
    return request({
      url: "/techDocManage/result/checkFileUnique",
      data: data,
      method: "post",
    });
  },
  // 修改知识文档树排序接口
  updateDirectoryTree: function (data) {
    return request({
      url: "/techDocManage/result/updateDirectoryTree",
      method: "post",
	  data: data,
    });
  },
};


export default userService;
