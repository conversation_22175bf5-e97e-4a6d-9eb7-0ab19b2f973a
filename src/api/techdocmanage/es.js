import request from '@/utils/request'

export function search(keywrod){
    return request({
        url: 'elasticsearch/es/highSearch/'+keywrod,
        method: 'get',
        // params: keywrod
    })
}

//统计查询到的记录
export function count(params){
    return request({
        url: 'elasticsearch/es/nlpCount',
        method: 'post',
        data: params
    })
}

//多条件查询
export function advancedSearch(params){
    return request({
        url: 'elasticsearch/es/moreSearch',
        method: 'post',
        data:params
    })
}
//new多条件查询
export function newAdvancedSearch(data){
    return request({
        url: 'elasticsearch/es/nlpSearch',
        method: 'post',
        data
    })
}
// 分页查询
export function PagSearch(params) {
    return request({
        url: 'elasticsearch/es/pageSearch',
        method: 'get',
        params
    })
    
}

// pdf页面及坐标
export function getlocation(params) {
    return request({
        url: 'elasticsearch/es/location',
        method: 'get',
        params
    })
    
}

//打开文件
export function openDoc(id){
    return request({
        url: '/fs/openFile?id='+id,
        method: 'get',
    })
}

//目录名称
export function  treeUserList(params){
    return request({
        url: 'techDocManage/directory/fileMountListTree',
        method: 'get',
        params:params
    })
}

//获取文件权限
export function getfileroot(params){
    return request({
        url: 'techDocManage/docauth/getInfoByFileIds/'+params,
        method: 'get',
    })
}

// 权限申请
export function RootApply(params){
 return request({
    url: 'techDocManage/result/applyAuth/?id='+params,
    method: 'get',
 })
}
