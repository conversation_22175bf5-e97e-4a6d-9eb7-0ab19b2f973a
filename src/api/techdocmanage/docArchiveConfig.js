import request from '@/utils/request'

// 查询归档配置列表
export function listDocArchiveConfig(query) {
  return request({
    url: '/techDocManage/docArchiveConfig/list',
    method: 'get',
    params: query
  })
}

// 查询归档配置详细
export function getDocArchiveConfig(id) {
  return request({
    url: '/techDocManage/docArchiveConfig/' + id,
    method: 'get'
  })
}

// 新增归档配置
export function addDocArchiveConfig(data) {
  return request({
    url: '/techDocManage/docArchiveConfig',
    method: 'post',
    data: data
  })
}

// 修改归档配置
export function updateDocArchiveConfig(data) {
  return request({
    url: '/techDocManage/docArchiveConfig',
    method: 'put',
    data: data
  })
}

// 删除归档配置
export function delDocArchiveConfig(id) {
  return request({
    url: '/techDocManage/docArchiveConfig/' + id,
    method: 'delete'
  })
}

// 启动归档配置
export function enableDocArchiveConfig(id) {
  return request({
    url: '/techDocManage/docArchiveConfig/enable/' + id,
    method: 'get'
  })
}

// 获取自动归档编号
export function getArchiveConfigId() {
  return request({
    url: '/techDocManage/docArchiveConfig/getArchiveConfigId',
    method: 'get'
  })
}

// 获取目录树
export function fileMountListTree(query) {
  return request({
    url: '/techDocManage/directory/fileMountListTree',
    method: 'get',
    params: query
  })
}




