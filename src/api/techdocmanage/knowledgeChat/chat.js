import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listChat(query) {
  return request({
    url: '/techDocManage/knowledge/getKnowledgeChatByKnowledgeName',
    method: 'get',
    params: query
  })
}
// 查询【请填写功能名称】列表
export function getKnowledgeChatNum(query) {
  return request({
    url: '/techDocManage/knowledge/getKnowledgeChatNum',
    method: 'get',
    params: query
  })
}
// 查询【请填写功能名称】详细
export function getChat(id) {
  return request({
    url: '/techDocManage/knowledge/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addChat(data) {
  return request({
    url: '/techDocManage/knowledge/addChat',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateChat(data) {
  return request({
    url: '/techDocManage/knowledge/editChat',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delChat(id) {
  return request({
    url: '/techDocManage/knowledge/chat/' + id,
    method: 'delete'
  })
}
