import request from '@/utils/request'

// 查询DocKnowledgeHelp列表
export function listHelp(query) {
  return request({
    url: '/techDocManage/help/list',
    method: 'get',
    params: query
  })
}

// 查询DocKnowledgeHelp详细
export function getHelp(id) {
  return request({
    url: '/techDocManage/help/' + id,
    method: 'get'
  })
}

// 新增DocKnowledgeHelp
export function addHelp(data) {
  return request({
    url: '/techDocManage/help',
    method: 'post',
    data: data
  })
}

// 修改DocKnowledgeHelp
export function updateHelp(data) {
  return request({
    url: '/techDocManage/help',
    method: 'put',
    data: data
  })
}

// 删除DocKnowledgeHelp
export function delHelp(id) {
  return request({
    url: '/techDocManage/help/' + id,
    method: 'delete'
  })
}
// 常用语排序
export function getHelpRank(params) {
  return request({
    url: '/techDocManage/help/updateRank',
    method: 'get',
    params
  })
}