import request from "@/utils/request";

// 查询目录列表
export function listDocDirectory(query) {
  return request({
    url: "/techDocManage/directory/list",
    method: "get",
    params: query,
  });
}

// 查询目录详细
export function getDocDirectory(id) {
  return request({
    url: "/techDocManage/directory/" + id,
    method: "get",
  });
}

// 新增目录
export function addDocDirectory(data) {
  return request({
    url: "/techDocManage/directory",
    method: "post",
    data: data,
  });
}

// 修改目录
export function updateDocDirectory(data) {
  return request({
    url: "/techDocManage/directory",
    method: "put",
    data: data,
  });
}

// 删除目录
export function delDocDirectory(id) {
  return request({
    url: "/techDocManage/directory/" + id,
    method: "delete",
  });
}

// 获取文档类型下的子目录
export function fileMountList(level) {
  return request({
    url: "/techDocManage/directory/fileMountList/" + level,
    method: "get",
  });
}

// 获取目录树
export function fileMountListTree(query) {
  return request({
    url: "/techDocManage/directory/fileMountListTree",
    method: "get",
    params: query,
  });
}
