import request from '@/utils/request';

const approveService = {
  // 获取文件列表
  getFileList: function (params) {
    return request({
      url: '/flow/businessApprove/approvalFilesByAgenda/',
      params,
    });
  },
  // 轮询查询时候电子签名
  querySign: function (params) {
    return request({
      url: '/flow/approveSignature/getInfo',
      method: 'get',
      params,
    });
  },
  // 删除扫码签名的图片
  deleteSignImg: function (params) {
    return request({
      url: 'flow/approveSignature/remove',
      method: 'get',
      params
    });
  },
  // 删除当前登录人扫码签名的图片
  deleteUserSignImg: function (params) {
    return request({
      url: '/flow/approveSignature/deleteAllSignature',
      method: 'get',
      params,
    });
  },
  // 审批提交
  submitApproval: function (data) {
    return request({
      url: '/flow/businessApprove/submitApprovalInfo',
      method: 'post',
      data: data,
    });
  },
  // 审批提交App
  submitApprovalApp: function (data) {
    return request({
      url: '/flow/businessApprove/submitApprovalInfoApp',
      method: 'post',
      data: data,
    });
  },
  // 审批提交
  test: function (params) {
    return request({
      url: '/flow/businessApprove/startApproval',
      params
    });
  },
};

export default approveService;
