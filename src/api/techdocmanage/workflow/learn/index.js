import request from '@/utils/request'

// 文件学习-详情
export function fileDetail(params) {
    return request({
        url: "/techDocManage/commentStudy/details",
        method: "post",
        data: params
    })
}

// 开始学习
export function startLearn(params) {
    return request({
        url:"/techDocManage/commentStudy/beginStart",
        method:"post",
        data: params
    })
}

// 计时学习
export function polling(params) {
    return request({
        url:"/techDocManage/commentStudy/polling",
        method:"post",
        data: params
    })
}
// docId 查询详情信息
export function getDocIdDetail(id) {
    return request({
      url: '/techDocManage/document/' + id,
      method: 'get'
    })
  }
//   文件详情
  export function getfileDetail(params) {
    return request({
      url: '/techDocManage/statuteRevise/getInfoByFilename',
      method: 'get',
      params
    })
  }
// 完成学习
export function completeStart(params) {
    return request({
        url:"/techDocManage/commentStudy/completeStart",
        method:"post",
        data: params
    })
}

// 评论列表
export function getCommentList(params) {
    return request({
        url:"/techDocManage/commentDiscuss/list",
        method:"get",
        params:params
    })
}

// 添加评论
export function addComment(params) {
    return request({
        url:"/techDocManage/commentDiscuss",
        method:"post",
        data: params
    })
}

// 添加笔记
export function addNotes(params) {
    return request({
        url:"/techDocManage/commentStudy",
        method:"put",
        data: params
    })
}

// 开始学习
export function startLearnOnline(params) {
    return request({
        url:"/techDocManage/commentStudy/beginStartOnline",
        method:"post",
        data: params
    })
}

export function completeStartOnline(params) {
    return request({
        url:"/techDocManage/commentStudy/completeStartOnline",
        method:"post",
        data: params
    })
}

// 修订
export function getRevision(params) {
    return request({
        url:"/filecore/addByDocId",
        method:"get",
        params: params
    })
}
