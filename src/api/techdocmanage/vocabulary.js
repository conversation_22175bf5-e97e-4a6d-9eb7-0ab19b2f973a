import request from '@/utils/request'

// 查询下拉树结构
export function treeselect() {
  return request({
    url: '/techDocManage/vocabulary/treeselect',
    method: 'get'
  })
}
export function saveBzcCategory(data) {
  return request({
    url: '/techDocManage/vocabulary/addBzcCategory',
    method: 'post',
    data: data
  })
}

export function deleteBzcCategory(id) {
  return request({
    url: '/techDocManage/vocabulary/deleteBzcCategory/' + id,
    method: 'get'
  })
}

export function updateBzcCategory(data) {
  return request({
    url: '/techDocManage/vocabulary/updateBzcCategory',
    method: 'post',
    data: data
  })
}

export function updateVocabulary(data) {
  return request({
    url: '/techDocManage/vocabulary/updateVocabulary',
    method: 'post',
    data: data
  })
}

export function addVocabulary(data) {
  return request({
    url: '/techDocManage/vocabulary/addVocabulary',
    method: 'post',
    data: data
  })
}

export function listVocabulary(query) {
  return request({
    url: '/techDocManage/vocabulary/listVocabulary',
    method: 'get',
    params: query
  })
}

export function getVocabulary(id) {
  return request({
    url: '/techDocManage/vocabulary/getVocabulary/' + id,
    method: 'get'
  })
}

export function delVocabulary(id) {
  return request({
    url: '/techDocManage/vocabulary/delVocabulary/'+ id,
    method: 'get'
  })
}

export function getKeyWords(query) {
  return request({
    url: '/techDocManage/vocabulary/getKeyWords',
    method: 'get',
    params: query
  })
}

export function listAll(query) {
  return request({
    url: '/techDocManage/vocabulary/listAll',
    method: 'get',
    params: query
  })
}

export function updateBatchVocabulary(data) {
  return request({
    url: '/techDocManage/vocabulary/updateBatchVocabulary',
    method: 'post',
    data: data
  })
}

  export function searchKbAllKeywords(data) {
    return request({
      url: '/techDocManage/knowledge_base/search_kb_all_keywords',
      method: 'post',
      data: data
    })
  }
