/*
 * @Author: <PERSON>@coalmineinfo.cn
 * @Date: 2022-08-18 11:13:16
 * @LastEditors: <PERSON> w<PERSON>@coalmineinfo.cn
 * @LastEditTime: 2022-09-20 14:30:37
 * @FilePath: \coalmine-ui\src\api\dfsProduction\base.js
 * @Description: 大佛寺生产协同中心公共接口及数据
 */
import request from '@/utils/axios'

export const baseUrl = '/production';

/**
 * @description: 智能生产业务协同通用获取数据
 * @param url 请求地址
 * @param method 请求方式 默认get
 * @param params 请求参数 默认无
 * @return {*}
 */
export const getData = ({ url, method = 'get', params = undefined }) => {
    let key = method === 'get' ? 'params' : 'data';
    return request({
        url: baseUrl + url,
        method,
        [key]: params
    })
};