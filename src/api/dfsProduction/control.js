/*
 * @Author: <PERSON>@coalmineinfo.cn
 * @Date: 2022-08-18 10:20:46
 * @LastEditors: <PERSON> w<PERSON>@coalmineinfo.cn
 * @LastEditTime: 2022-11-11 16:57:45
 * @FilePath: \coalmine-ui\src\api\dfsProduction\control.js
 * @Description: 大佛寺生产协同中心智能操控接口
 */
import request from '@/utils/axios'
import { baseUrl } from './common'
// 环境监测信息
export const getWorkEnv = (params) => request({
    url: baseUrl + '/mining/workEnvironment',
    method: 'get',
    params
});


// 综采生产
export const getMainProd = (data) => request({
    url: baseUrl + '/mining/mineProduction/' + data,
    method: 'get'
});

// 人员轨迹详情
export const getTrack = (params, middleUrl) => request({
    url: baseUrl + middleUrl + '/userTrajectory',
    method: 'get',
    params,
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
});