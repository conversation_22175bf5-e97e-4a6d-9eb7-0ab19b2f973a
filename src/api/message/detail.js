import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listDetail(query) {
  return request({
    url: '/message/messageDetail/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getDetail(id) {
  return request({
    url: '/message/messageDetail/' + id,
    method: 'get'
  })
}

// 删除【请填写功能名称】
export function delDetail(id) {
  return request({
    url: '/message/messageDetail/' + id,
    method: 'delete'
  })
}
// 数量统计
export function getTotal(query) {
  return request({
    url: '/message/messageDetail/getTotal',
    method: 'get',
    params: query
  })
}

// 修改已读状态
export function readUpdate(ids) {
  return request({
    url: '/message/messageDetail/readUpdate/'+ids,
    method: 'get'
  })
}
// 置顶
export function topUpdate(ids) {
  return request({
    url: '/message/messageDetail/topUpdate/'+ids,
    method: 'get'
  })
}
// 取消置顶
export function notopUpdate(ids) {
  return request({
    url: '/message/messageDetail/notopUpdate/'+ids,
    method: 'get'
  })
}
