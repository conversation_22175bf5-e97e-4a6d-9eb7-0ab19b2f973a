import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listTemplate(query) {
  return request({
    url: '/message/messageTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getTemplate(id) {
  return request({
    url: '/message/messageTemplate/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addTemplate(data) {
  return request({
    url: '/message/messageTemplate',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateTemplate(data) {
  return request({
    url: '/message/messageTemplate',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delTemplate(id) {
  return request({
    url: '/message/messageTemplate/' + id,
    method: 'delete'
  })
}
