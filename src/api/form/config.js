import request from '@/utils/request'

// 查询表单定义列表
export function listInfo(query) {
  return request({
    url: '/form/info/list',
    method: 'get',
    params: query
  })
}

// 查询表单定义详细
export function getInfo(formId) {
  return request({
    url: '/form/info/' + formId,
    method: 'get'
  })
}

// 新增表单定义
export function addInfo(data) {
  return request({
    url: '/form/info',
    method: 'post',
    data: data
  })
}

// 修改表单定义
export function updateInfo(data) {
  return request({
    url: '/form/info',
    method: 'put',
    data: data
  })
}

// 修改表单定义
export function updateJson(data) {
  return request({
    url: '/form/info/updateJson',
    method: 'put',
    data: data
  })
}



// 删除表单定义
export function delInfo(formId) {
  return request({
    url: '/form/info/' + formId,
    method: 'delete'
  })
}

// 发布表单定义
export function publishInfo(data) {
  return request({
    url: '/form/info/publish',
    method: 'put',
    data: data
  })
}

// 获取所有主表
export function masterTable() {
  return request({
    url: '/form/info/selectMasterForms' ,
    method: 'get'
  })
}


