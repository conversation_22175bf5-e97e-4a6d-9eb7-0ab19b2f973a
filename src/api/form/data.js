import request from '@/utils/request'

// 查询单数据列表
export function listFormData(query) {
  return request({
    url: '/form/formData/list',
    method: 'get',
    params: query
  })
}

// 查询单数据详细
export function getFormData(id) {
  return request({
    url: '/form/formData/' + id,
    method: 'get'
  })
}

// 查询单数据详细
export function getFormDataByFormId(data) {
  return request({
    url: '/form/formData/getFormDataByFormId',
    method: 'post',
    data: data
  })
}
// 新增单数据
export function addFormData(data) {
  return request({
    url: '/form/formData',
    method: 'post',
    data: data
  })
}

// 修改单数据
export function updateFormData(data) {
  return request({
    url: '/form/formData',
    method: 'put',
    data: data
  })
}

// 删除单数据
export function delFormData(id) {
  return request({
    url: '/form/formData/' + id,
    method: 'delete'
  })
}

// 获取子表信息
export function getFormDefByDataId(id) {
  return request({
    url: '/form/formData/getFormDefByDataId/' + id ,
    method: 'get'
  })
}

export function getLogList(query) {
  return request({
    url: '/form/log/list',
    method: 'get',
    params: query
  })
}

