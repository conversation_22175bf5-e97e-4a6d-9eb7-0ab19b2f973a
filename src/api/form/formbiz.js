import request from '@/utils/request'

// 查询业务管理列表
export function listForm(query) {
  return request({
    url: '/form/formbiz/list',
    method: 'get',
    params: query
  })
}

// 查询业务管理详细
export function getForm(id) {
  return request({
    url: '/form/formbiz/' + id,
    method: 'get'
  })
}

// 新增业务管理
export function addForm(data) {
  return request({
    url: '/form/formbiz',
    method: 'post',
    data: data
  })
}

// 修改业务管理
export function updateForm(data) {
  return request({
    url: '/form/formbiz',
    method: 'put',
    data: data
  })
}

// 删除业务管理
export function delForm(id) {
  return request({
    url: '/form/formbiz/' + id,
    method: 'delete'
  })
}