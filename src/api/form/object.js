import request from '@/utils/request'

// 查询业务对象列表
export function listObject(query) {
  return request({
    url: '/form/object/list',
    method: 'get',
    params: query
  })
}

// 查询业务对象详细
export function getObject(id) {
  return request({
    url: '/form/object/' + id,
    method: 'get'
  })
}

// 新增业务对象
export function addObject(data) {
  return request({
    url: '/form/object',
    method: 'post',
    data: data
  })
}

// 修改业务对象
export function updateObject(data) {
  return request({
    url: '/form/object',
    method: 'put',
    data: data
  })
}

// 删除业务对象
export function delObject(id) {
  return request({
    url: '/form/object/' + id,
    method: 'delete'
  })
}

// 查询系统list
export function sysTreeselect() {
  return request({
    url: '/system/system/list',
    method: 'get'
  })
}

// 修改业务对象
export function updateObjectStatusConfig(data) {
  return request({
    url: '/form/object/updateStatusConfig',
    method: 'put',
    data: data
  })
}

