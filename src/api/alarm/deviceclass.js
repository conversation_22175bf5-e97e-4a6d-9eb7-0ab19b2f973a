import request from '@/utils/request'

// 查询设备分类列表
export function listDeviceclass(query) {
  return request({
    url: '/alarm/deviceclass/list',
    method: 'get',
    params: query
  })
}

// 查询设备分类详细
export function getDeviceclass(id) {
  return request({
    url: '/alarm/deviceclass/' + id,
    method: 'get'
  })
}

// 新增设备分类
export function addDeviceclass(data) {
  return request({
    url: '/alarm/deviceclass',
    method: 'post',
    data: data
  })
}

// 修改设备分类
export function updateDeviceclass(data) {
  return request({
    url: '/alarm/deviceclass',
    method: 'put',
    data: data
  })
}

// 删除设备分类
export function delDeviceclass(id) {
  return request({
    url: '/alarm/deviceclass/' + id,
    method: 'delete'
  })
}

// 获取设备分类下拉树
export function getDeviceclassTree(){
  return request({
    url: '/alarm/deviceclass/treeselect',
    method: 'get'
  })
}

// 获取设备分类及其设备下拉树
export function getDevicevoTree(query){
  return request({
    url: '/alarm/deviceclass/devicevo/treeselect',
    method: 'get',
    params: query
  })
}