import request from '@/utils/request'

// 查询督办列表
export function listSupervisehandle(query) {
  return request({
    url: '/alarm/supervisehandle/list',
    method: 'get',
    params: query
  })
}

// 查询督办详细
export function getSupervisehandle(id) {
  return request({
    url: '/alarm/supervisehandle/' + id,
    method: 'get'
  })
}

// 新增督办
export function addSupervisehandle(data) {
  return request({
    url: '/alarm/supervisehandle',
    method: 'post',
    data: data
  })
}

// 修改督办
export function updateSupervisehandle(data) {
  return request({
    url: '/alarm/supervisehandle',
    method: 'put',
    data: data
  })
}

// 删除督办
export function delSupervisehandle(id) {
  return request({
    url: '/alarm/supervisehandle/' + id,
    method: 'delete'
  })
}
