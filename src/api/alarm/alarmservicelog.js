import request from '@/utils/request'

// 查询alarmservicelog列表
export function listAlarmservicelog(query) {
  return request({
    url: '/alarm/alarmservicelog/list',
    method: 'get',
    params: query
  })
}

// 查询alarmservicelog详细
export function getAlarmservicelog(id) {
  return request({
    url: '/alarm/alarmservicelog/' + id,
    method: 'get'
  })
}

// 新增alarmservicelog
export function addAlarmservicelog(data) {
  return request({
    url: '/alarm/alarmservicelog/',
    method: 'post',
    data: data
  })
}

// 修改alarmservicelog
export function updateAlarmservicelog(data) {
  return request({
    url: '/alarm/alarmservicelog/',
    method: 'put',
    data: data
  })
}

// 删除alarmservicelog
export function delAlarmservicelog(id) {
  return request({
    url: '/alarm/alarmservicelog/' + id,
    method: 'delete'
  })
}

//清空alarmservicelog
export function cleanAlarmservicelog(){
  return request({
    url:'/alarm/alarmservicelog/clean/',
    method:'delete'
  })
}

//根据事件id删除清空alarmservicelog
export function deleteAlarmservicelogByEventid(id){
  return request({
    url:'/alarm/alarmservicelog/clean/'+id,
    method:'delete'
  })
}


