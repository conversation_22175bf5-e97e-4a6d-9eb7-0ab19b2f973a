import request from '@/utils/request'

let deviceScreen= {
  NODETYPE: {
    TOP: 0, //0   组织机构 1 级别，即矿务局
    MINE: 1, // 1   机构2级  矿
    SYSTYPE: 2, // 2   系统大类
    SYS: 3, // 3   系统
    EQ: 4, // 4   设备
  },
  /**
   * 得到树状结构上的错误信息树
   * @returns 
   */
   deviceTreeErrs: function () {
    return request({
      url: '/ServerCommand/deviceTreeErrs',
      method: 'get',
    })
  },

  /**
   * 得到树结构的节点
   * @param {*} parentId 
   * @returns 
   */
   deviceTree: function (parentId) {
    return request({
      url: '/ServerCommand/deviceTree?parent_id=' + parentId,
      method: 'get',
    })
  },

  /**
   * 递归获取树节点
   * @param {*} parentId 
   * @returns 
   */
   deviceTreeData: function () {
    return request({
      url: '/ServerCommand/deviceTreeData',
      method: 'get',
    })
  },

  deviceTreeDataDept: function () {
    return request({
      url: '/ServerCommand/deviceTreeDataDept',
      method: 'get',
    })
  },
  
  // 
  RecursionTree: function (dataarray, parentId) {
    let rs = [];
    for (let i = 0; i < dataarray.length; i++) {
      let d = dataarray[i]
      if (d.parent_id == parentId)
        rs.push(d);
    }
    return rs;
  },

  /**
   * 得到按照分系统显示的全部节点
   * @returns 
   */
  deviceTreeSys: function () {
    let p = new Promise((resolve, reject) => {
      this.deviceTreeData().then((rs) => {
        let tree = []
        let data = rs.data;
        tree = this.RecursionTree(data, '');
        tree.forEach(element => {
          let r = this.RecursionTree(data, element.id)
          if (r.length > 0) {
            element.children = r;
            r.forEach(v => {
              let r1 = this.RecursionTree(data, v.id)
              if (r1.length > 0) {
                v.children = r1;
              }
            })
          }
        });
        resolve(tree);
      })
    });
    return p;
  },
  
  deviceTreeDept: function () {
    let p = new Promise((resolve, reject) => {
      this.deviceTreeDataDept().then((rs) => {
        let tree = []
        let data = rs.data;
        //==1
        tree = this.RecursionTree(data, '');
        //==2
        tree.forEach(element => {
          let r = this.RecursionTree(data, element.id)
          if (r.length > 0) {
            element.children = r;
            //==3
            r.forEach(v => {
              let r1 = this.RecursionTree(data, v.id)
              if (r1.length > 0) {
                v.children = r1;

                //==4
                // r1.forEach(v1 => {
                //   let r2 = this.RecursionTree(data, v1.id)
                //   if (r2.length > 0) {
                //     v1.children = r2;
                //   }
                // })

              }
            })
          }
        });
        resolve(tree);
      })
    });
    return p;
  },

  /**
   *  点击节点，得到设备和工作时间段信息
   * @param {*} id 
   * @param {*} level 
   * @returns 
   */
 getStatusCount: function (id, level) {
    return request({
      url: '/ServerCommand/getStatusCount?id=' + id + "&level=" + level,
      method: 'get',
    })
  },

  /**
   * 得到仪表盘显示的参数最新值
   * @param {*} equipment_id 
   * @returns 
   */
  getDeviceLastValue: function (equipment_id) {
    return request({
      url: '/ServerCommand/getDeviceLastValue?equipment_id=' + equipment_id,
      method: 'get',
    })
  },

  /**
   * 修改设备状态
   * @param {*} equipment_id 
   * @param {*} status 
   * @returns 
   */
   chgDeviceStatus: function (equipment_id, status) {
    return request({
      url: '/ServerCommand/chgDeviceStatus',
      method: 'post',
      data: {
        id: equipment_id,
        status: status
      }
    })
  },

  /**
   * 根据系统id批量更改设备状态
   * @param {*} sys_id 
   * @param {*} status 
   * @returns 
   */
  chgDeviceStatusBySysId: function (sys_id, status) {
    return request({
      url: '/ServerCommand/chgDeviceStatusBySysId',
      method: 'post',
      data: {
        id: sys_id,
        status: status
      }
    })
  },

  /**
   * 增加临时检修记录
   * @param {*} form 
   * @returns 
   */
   casualFix: function (form) {
    return request({
      url: '/ServerCommand/casualFix',
      method: 'post',
      data: form
    })
  },

  //-----一下为设备运行监控-----------
  /**
   * 得到指定时间段内设备额 过煤量 运行时长的平均值，卡片显示用
   * @param {*} id 
   * @param {*} level 
   * @param {*} begintime 
   * @param {*} endtime 
   * @returns 
   */
   getDeviceCount: function (id, level, begintime, endtime, sumtype) {
    return request({
      url: '/ServerCommand/getDeviceCount?'
        + "id=" + id
        + "&level=" + level
        + "&begintime=" + begintime
        + "&endtime=" + endtime
        + "&sumtype=" + sumtype
      ,
      method: 'get'

    })
  },

  /**
   * 得到指定时间段内过煤量，按照汇总方式汇总
   * @param {*} id 
   * @param {*} level 
   * @param {*} begintime 
   * @param {*} endtime 
   * @param {*} sumtype 
   * @returns 
   */
  getCoalCount: function (id, level, begintime, endtime, sumtype) {
    return request({
        url: '/ServerCommand/getCoalCount?'
          + "id=" + id
          + "&level=" + level
          + "&begintime=" + begintime
          + "&endtime=" + endtime
          + "&sumtype=" + sumtype
        ,
        method: 'get'

      })
  },

  // 查询临检记录
  searchList: function (id) {
    return request({
      url: '/ServerCommand/searchList?id=' + id,
      method: 'post',
    })
  },

  /**
   * 得到报警信息
   * @returns 
   */
  getDeviceAlarms: function () {
    return request({
      url: '/ServerCommand/getDeviceAlarms',
      method: 'get'

    })
  },

  //参数列表
  parameterList: function(id) {
    return request({
        url: 'alarm/parameter/'+id,
        method: 'get'
    })
  },
   // 最新参数列表
   parameterListREC: function(id) {
    return request({
        url: 'alarm/parameter/getnewInfo/'+id,
        method: 'get'
    })
  },
}
export default deviceScreen