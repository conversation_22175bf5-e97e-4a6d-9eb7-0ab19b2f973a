import request from '@/utils/request'

// 查询报警事件列表
export function listAlarmevent(query) {
  return request({
    url: '/alarm/alarmevent/list',
    method: 'get',
    params: query
  })
}
// 查询业务类型树
export function selectEventTypeTree() {
  return request({
    url: '/alarm/alarmevent/selectEventTypeTree',
    method: 'get'
  })
}


// 查询报警事件详细
export function getAlarmevent(id) {
  return request({
    url: '/alarm/alarmevent/' + id,
    method: 'get'
  })
}

// 新增报警事件
export function saveAlarmevent(data) {
  return request({
    url: '/alarm/alarmevent/save',
    method: 'post',
    data: data
  })
}

// 新增报警事件
export function addAlarmevent(data) {
  return request({
    url: '/alarm/alarmevent',
    method: 'post',
    data: data
  })
}

// 修改报警事件
export function updateAlarmevent(data) {
  return request({
    url: '/alarm/alarmevent',
    method: 'put',
    data: data
  })
}

// 删除报警事件
export function delAlarmevent(id) {
  return request({
    url: '/alarm/alarmevent/' + id,
    method: 'delete'
  })
}

// 根据事件ID查询设备ID
export function getDeviceIdByEcentId(eventId) {
  return request({
    url: '/alarm/alarmevent/deviceId/' + eventId,
    method: 'get'
  })
}
