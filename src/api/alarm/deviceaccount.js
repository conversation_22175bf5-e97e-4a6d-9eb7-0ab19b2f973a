import request from '@/utils/request'

// 查询设备信息列表
export function listDevice(query) {
  return request({
    url: '/alarm/deviceaccount/list',
    method: 'get',
    params: query
  })
}

// 查询设备信息详细
export function getDevice(id) {
  return request({
    url: '/alarm/deviceaccount/' + id,
    method: 'get'
  })
}

// 新增设备信息
export function addDevice(data) {
  return request({
    url: '/alarm/deviceaccount',
    method: 'post',
    data: data
  })
}

// 修改设备信息
export function updateDevice(data) {
  return request({
    url: '/alarm/deviceaccount',
    method: 'put',
    data: data
  })
}

// 删除设备信息
export function delDevice(id) {
  return request({
    url: '/alarm/deviceaccount/' + id,
    method: 'delete'
  })
}

// 查询设备信息列表
export function listDeviceByIds(ids) {
  return request({
    url: '/alarm/deviceaccount/listByIds/'+ ids,
    method: 'get',
  })
}

// 查询设备信息列表
export function listMineDept(deptType, deptId) {
  if(!deptId){
    deptId = null;
  }
  return request({
    url: '/alarm/minedept/'+ deptType +'/'+ deptId,
    method: 'get',
  })
}

// 查询所属系统树
export function listSubSystemTree() {
  return request({
    url: '/alarm/subsystem/get',
    method: 'get',
  })
}
// 查询所属系统
export function listSubSystem() {
  return request({
    url: '/alarm/subsystem/list',
    method: 'get',
  })
}

