import request from '@/utils/request'

// 查询参数列表
export function queryPushList(query) {
  return request({
    url: '/alarm/push/list',
    method: 'get',
    params: query
  })
}

// 查询listkey
export function queryListKey(query) {
  return request({
    url: '/alarm/push/listKey',
    method: 'get',
    params: query
  })
}

// 查询参数详细
export function getById(id) {
  return request({
    url: '/alarm/pushTemplate/getById?id=' + id,
    method: 'get'
  })
}

// 根据参数键名查询参数值
export function getConfigKey(configKey) {
  return request({
    url: '/system/config/configKey/' + configKey,
    method: 'get'
  })
}

// 新增报警模板
export function addCompany(data) {
  return request({
    url: '/alarm/push/addCompany',
    method: 'post',
    data: data
  })
}

// 更新报警模板
export function updatePush(data) {
  return request({
    url: '/alarm/push/update',
    method: 'post',
    data: data
  })
}

// 删除报警模板
export function delPush(id) {
  return request({
    url: '/alarm/push/' + id,
    method: 'delete'
  })
}

// 刷新参数缓存
export function refreshCache() {
  return request({
    url: '/system/config/refreshCache',
    method: 'delete'
  })
}
