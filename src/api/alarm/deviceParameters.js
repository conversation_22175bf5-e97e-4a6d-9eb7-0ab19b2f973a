import request from '@/utils/request'
let deviceParameters = {
  // 树
  treeList: function (params) {
    return request({
        url: 'alarm/deviceclass/treeselectbymonitorlevel',
        params: params,
        method: 'get'
    })
  },
  // 设备列表
  eqpList: function (params) {
    return request({
        url: 'alarm/deviceaccount/listbymonitorlevel',
        params: params,
        method: 'get'
    })
  },
  // 参数列表
  parameterList: function(id) {
    return request({
        url: 'alarm/parameter/'+id,
        method: 'get'
    })
  },
  // 推荐参数列表
  parameterListREC: function(id) {
    return request({
        url: 'alarm/parameter/getnewInfo/'+id,
        method: 'get'
    })
  },
  // 新增配置
  addParameter: function(params) {
      return request({
          url: 'alarm/parameter',
          data: params,
          method: 'post'
      })
  },
  // 修改接口
  editParameter: function(params) {
      return request({
          url: 'alarm/parameter',
          data: params,
          method: 'put'
      })
  },
  // 删除接口
  delParameter: function(id) {
    return request({
        url: 'alarm/parameter/' + id,
        method: 'delete'
    })
  },
  guid: function() {
    return request({
      url: '/ServerCommand/GUID',
      method: 'get'
    })
  }
  
}
export default deviceParameters