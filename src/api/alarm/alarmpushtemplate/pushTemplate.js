import request from '@/utils/request'

// 查询参数列表
export function queryList(query) {
  return request({
    url: '/alarm/pushTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询参数详细
export function getById(id) {
  return request({
    url: '/alarm/pushTemplate/getById?id=' + id,
    method: 'get'
  })
}

// 根据参数键名查询参数值
export function getConfigKey(configKey) {
  return request({
    url: '/system/config/configKey/' + configKey,
    method: 'get'
  })
}

// 新增报警模板
export function addTemplate(data) {
  return request({
    url: '/alarm/pushTemplate/add',
    method: 'post',
    data: data
  })
}

// 更新报警模板
export function updateTemplate(data) {
  return request({
    url: '/alarm/pushTemplate/update',
    method: 'post',
    data: data
  })
}

// 删除报警模板
export function delTemplate(id) {
  return request({
    url: '/alarm/pushTemplate/delById?id=' + id,
    method: 'post'
  })
}

// 刷新参数缓存
export function refreshCache() {
  return request({
    url: '/system/config/refreshCache',
    method: 'delete'
  })
}

// 获取
export function getLeaders(data) {
  return request({
    url: '/alarm/pushTemplate/getLeaders',
    method: 'post',
    data: data
  })
}
// 获取
export function saveLeaders(data) {
  return request({
    url: '/alarm/pushTemplate/saveLeaders',
    method: 'post',
    data: data
  })
}
