import request from '@/utils/request'

// 查询报警事件列表
export function listAlarmjobevent(query) {
  return request({
    url: '/alarm/alarmjobevent/list',
    method: 'get',
    params: query
  })
}

// 查询未配置报警事件列表
export function listNotConfigAlarmjobevent() {
  return request({
    url: '/alarm/alarmjobevent/list/notconfig',
    method: 'get',
  })
}

// 查询报警事件详细
export function getAlarmjobevent(id) {
  return request({
    url: '/alarm/alarmjobevent/' + id,
    method: 'get'
  })
}

// 新增报警事件
export function addAlarmjobevent(data) {
  return request({
    url: '/alarm/alarmjobevent',
    method: 'post',
    data: data
  })
}

// 修改报警事件
export function updateAlarmjobevent(data) {
  return request({
    url: '/alarm/alarmjobevent',
    method: 'put',
    data: data
  })
}

// 删除报警事件
export function delAlarmjobevent(id) {
  return request({
    url: '/alarm/alarmjobevent/' + id,
    method: 'delete'
  })
}

//清除缓存
export function clearCache(){
  return request({
    url: '/alarm/alarmjobevent/clean/',
    method: 'post'
  })
}

//定时任务执行一次
export function executeOnce(id){
  const data = {
    id
  }
  return request({
    url: '/alarm/alarmjobevent/run',
    method: 'put',
    data: data
  })
}

//获取专业分类树
export function getTree(){
  return request({
    url: 'alarm/alarmjobevent/treeselect',
    method: 'get'

  })
}

// 任务状态修改
export function changeStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: 'alarm/alarmjobevent/changeStatus',
    method: 'put',
    data: data
  })
}
