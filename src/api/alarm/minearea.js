import request from '@/utils/request'

// 查询矿井区域维护列表
export function listMinearea(query) {
  return request({
    url: '/alarm/minearea/list',
    method: 'get',
    params: query
  })
}

// 查询全部区域维护列表
export function alllistMinearea() {
  return request({
    url: '/alarm/minearea/allList',
    method: 'get'
  })
}

// 使用组织ID查询矿井区域维护列表
export function listMineareaByDeptId(query,id) {
  return request({
    url: '/alarm/minearea/listByDeptId/' + id,
    method: 'get',
    params: query
  })
}

// 查询矿井区域维护详细
export function getMinearea(id) {
  return request({
    url: '/alarm/minearea/' + id,
    method: 'get'
  })
}

// 新增矿井区域维护
export function addMinearea(data) {
  return request({
    url: '/alarm/minearea',
    method: 'post',
    data: data
  })
}

// 修改矿井区域维护
export function updateMinearea(data) {
  return request({
    url: '/alarm/minearea',
    method: 'put',
    data: data
  })
}

// 删除矿井区域维护
export function delMinearea(id) {
  return request({
    url: '/alarm/minearea/' + id,
    method: 'delete'
  })
}
