import request from '@/utils/request'

// 查询推送报警级别列表
export function listPushperson(query) {
  return request({
    url: '/alarm/pushperson/list',
    method: 'get',
    params: query
  })
}

// 查询推送报警级别详细
export function getPushperson(id) {
  return request({
    url: '/alarm/pushperson/' + id,
    method: 'get'
  })
}

// 新增推送报警级别
export function addPushperson(data) {
  return request({
    url: '/alarm/pushperson',
    method: 'post',
    data: data
  })
}

// 修改推送报警级别
export function updatePushperson(data) {
  return request({
    url: '/alarm/pushperson',
    method: 'put',
    data: data
  })
}

// 删除推送报警级别
export function delPushperson(id) {
  return request({
    url: '/alarm/pushperson/' + id,
    method: 'delete'
  })
}

// 查询推送报警级别详细
export function listPushpersonByPushConfigId(id) {
  return request({
    url: '/alarm/pushperson/getListByPushConfigId/' + id,
    method: 'get'
  })
}


