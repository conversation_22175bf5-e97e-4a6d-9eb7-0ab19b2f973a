import request from '@/utils/request'

// 查询报警类型级别列表
export function listAlarmtypelevel(query) {
  return request({
    url: '/alarm/alarmtypelevel/list',
    method: 'get',
    params: query
  })
}

// 查询报警类型级别详细
export function getAlarmtypelevel(id) {
  return request({
    url: '/alarm/alarmtypelevel/' + id,
    method: 'get'
  })
}

// 查询报警类型级别详细
export function getAlarmtypelevelInfo(id) {
  return request({
    url: '/alarm/alarmtypelevel/alarmType/' + id,
    method: 'get'
  })
}

// 新增报警类型级别
export function addAlarmtypelevel(data) {
  return request({
    url: '/alarm/alarmtypelevel',
    method: 'post',
    data: data
  })
}

// 修改报警类型级别
export function updateAlarmtypelevel(data) {
  return request({
    url: '/alarm/alarmtypelevel',
    method: 'put',
    data: data
  })
}

// 删除报警类型级别
export function delAlarmtypelevel(id) {
  return request({
    url: '/alarm/alarmtypelevel/' + id,
    method: 'delete'
  })
}
