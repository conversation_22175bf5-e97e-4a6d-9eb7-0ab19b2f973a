import request from '@/utils/request'

// 查询三违登记列表
export function listThreeviolation(query) {
  return request({
    url: '/alarm/threeviolation/list',
    method: 'get',
    params: query
  })
}

// 查询三违登记详细
export function getThreeviolation(id) {
  return request({
    url: '/alarm/threeviolation/' + id,
    method: 'get'
  })
}

// 新增三违登记
export function addThreeviolation(data) {
  return request({
    url: '/alarm/threeviolation',
    method: 'post',
    data: data
  })
}

// 修改三违登记
export function updateThreeviolation(data) {
  return request({
    url: '/alarm/threeviolation',
    method: 'put',
    data: data
  })
}

// 删除三违登记
export function delThreeviolation(id) {
  return request({
    url: '/alarm/threeviolation/' + id,
    method: 'delete'
  })
}

// 查询三违帮教记录详细
export function getHelprecord(id) {
  return request({
    url: '/alarm/helprecord/' + id,
    method: 'get'
  })
}
