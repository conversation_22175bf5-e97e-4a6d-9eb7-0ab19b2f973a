import request from '@/utils/request'

// 查询考核列表
export function listAlarmexamine(query) {
  return request({
    url: '/alarm/alarmexamine/list',
    method: 'get',
    params: query
  })
}

// 查询考核详细
export function getAlarmexamine(id) {
  return request({
    url: '/alarm/alarmexamine/' + id,
    method: 'get'
  })
}

// 新增考核
export function addAlarmexamine(data) {
  return request({
    url: '/alarm/alarmexamine',
    method: 'post',
    data: data
  })
}

// 修改考核
export function updateAlarmexamine(data) {
  return request({
    url: '/alarm/alarmexamine',
    method: 'put',
    data: data
  })
}

// 删除考核
export function delAlarmexamine(id) {
  return request({
    url: '/alarm/alarmexamine/' + id,
    method: 'delete'
  })
}
