import request from '@/utils/request'

// 查询安全风险登记列表
export function listSafetyrisk(query) {
  return request({
    url: '/alarm/safetyrisk/list',
    method: 'get',
    params: query
  })
}

// 查询安全风险登记详细
export function getSafetyrisk(id) {
  return request({
    url: '/alarm/safetyrisk/' + id,
    method: 'get'
  })
}

// 新增安全风险登记
export function addSafetyrisk(data) {
  return request({
    url: '/alarm/safetyrisk',
    method: 'post',
    data: data
  })
}

// 修改安全风险登记
export function updateSafetyrisk(data) {
  return request({
    url: '/alarm/safetyrisk',
    method: 'put',
    data: data
  })
}

// 删除安全风险登记
export function delSafetyrisk(id) {
  return request({
    url: '/alarm/safetyrisk/' + id,
    method: 'delete'
  })
}


// 查询风险检查记录详细
export function getRiskcheck(id) {
  return request({
    url: '/alarm/riskcheck/' + id,
    method: 'get'
  })
}


export function getSpecialTypeList(data) {
  return request({
    url: '/alarm/safetyrisk/specialType',
    method: 'get',
  })
}

export function getRiskTypeList(data) {
  return request({
    url: '/alarm/safetyrisk/riskType',
    method: 'get',
  })
}