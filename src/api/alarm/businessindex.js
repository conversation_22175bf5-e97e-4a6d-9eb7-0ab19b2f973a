import request from '@/utils/request'

// 查询业务指标列表
export function listBusinessindex(query) {
  return request({
    url: '/alarm/businessindex/list',
    method: 'get',
    params: query
  })
}

// 查询业务指标详细
export function getBusinessindex(id) {
  return request({
    url: '/alarm/businessindex/' + id,
    method: 'get'
  })
}

// 新增业务指标
export function addBusinessindex(data) {
  return request({
    url: '/alarm/businessindex',
    method: 'post',
    data: data
  })
}

// 修改业务指标
export function updateBusinessindex(data) {
  return request({
    url: '/alarm/businessindex',
    method: 'put',
    data: data
  })
}

// 删除业务指标
export function delBusinessindex(id) {
  return request({
    url: '/alarm/businessindex/' + id,
    method: 'delete'
  })
}

// 根据ID集合业务指标
export function listBusinessIndexByIds(ids) {
  return request({
    url: '/alarm/businessindex/list/byBusinessIds',
    method: 'post',
    data: ids
  })
}

// 根据事件ID查询业务指标
export function listBusinessIndexByEventId(eventId) {
  return request({
    url: '/alarm/businessindex/list/byEventId/' + eventId,
    method: 'get'
  })
}

//测试链接
export function connectionTest(data){
  return request({
    url: 'alarm/businessindex/connectiontest',
    method: 'post',
    data: data
  })
}