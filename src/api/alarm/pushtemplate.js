import request from '@/utils/request'

// 查询推送报警模板列表
export function listPushtemplate(query) {
  return request({
    url: '/alarm/pushtemplate/list',
    method: 'get',
    params: query
  })
}

// 查询推送报警模板详细
export function listPushtemplateByAlarmtypeId(id) {
  return request({
    url: '/alarm/pushtemplate/listByAlarmtypeId/' + id,
    method: 'get'
  })
}

// 查询推送报警模板详细
export function getPushtemplate(id) {
  return request({
    url: '/alarm/pushtemplate/' + id,
    method: 'get'
  })
}

// 新增推送报警模板
export function addPushtemplate(data) {
  return request({
    url: '/alarm/pushtemplate',
    method: 'post',
    data: data
  })
}

// 修改推送报警模板
export function updatePushtemplate(data) {
  return request({
    url: '/alarm/pushtemplate',
    method: 'put',
    data: data
  })
}

// 删除推送报警模板
export function delPushtemplate(id) {
  return request({
    url: '/alarm/pushtemplate/' + id,
    method: 'delete'
  })
}
