import request from '@/utils/request'

// 查询推送配置列表
export function listPushgovernment(query) {
  return request({
    url: '/alarm/pushgovernment/list',
    method: 'get',
    params: query
  })
}

// 查询推送配置详细
export function getPushgovernment(id) {
  return request({
    url: '/alarm/pushgovernment/' + id,
    method: 'get'
  })
}

// 新增推送配置
export function addPushgovernment(data) {
  return request({
    url: '/alarm/pushgovernment',
    method: 'post',
    data: data
  })
}

// 修改推送配置
export function updatePushgovernment(data) {
  return request({
    url: '/alarm/pushgovernment',
    method: 'put',
    data: data
  })
}

// 删除推送配置
export function delPushgovernment(id) {
  return request({
    url: '/alarm/pushgovernment/' + id,
    method: 'delete'
  })
}

// 获取设备分类下拉树
export function getDeptUserTree(type){
  return request({
    url: '/alarm/pushconfig/treeselect/' + type,
    method: 'get'
  })
}

// 查询推送报警模板详细
export function listPushgovernmentByUserId(query,id) {
  return request({
    url: '/alarm/pushgovernment/getListByUserId/' + id,
    method: 'get',
    params: query
  })
}

