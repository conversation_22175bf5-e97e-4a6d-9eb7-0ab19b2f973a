import request from '@/utils/request'

// 查询隐患登记列表
export function listHiddendanger(query) {
  return request({
    url: '/alarm/hiddendanger/list',
    method: 'get',
    params: query
  })
}

// 查询隐患登记详细
export function getHiddendanger(id) {
  return request({
    url: '/alarm/hiddendanger/' + id,
    method: 'get'
  })
}

// 新增隐患登记
export function addHiddendanger(data) {
  return request({
    url: '/alarm/hiddendanger',
    method: 'post',
    data: data
  })
}

// 修改隐患登记
export function updateHiddendanger(data) {
  return request({
    url: '/alarm/hiddendanger',
    method: 'put',
    data: data
  })
}

// 删除隐患登记
export function delHiddendanger(id) {
  return request({
    url: '/alarm/hiddendanger/' + id,
    method: 'delete'
  })
}

// 查询隐患点检查记录详细
export function getHiddencheck(id) {
  return request({
    url: '/alarm/hiddencheck/' + id,
    method: 'get'
  })
}

//查询看板图数据
export function getBoardCharts(params) {
  return request({
    url: '/alarm/SafetyStatistics/TrendQuery',
    method: 'get',
    params: params
  })
}

// 查询看板列表数据
export function getBoardList(params) {
  return request({
    url: '/alarm/SafetyStatistics/Count',
    method: 'get',
    params: params
  })
}

export function getSpecialTypeList(data) {
  return request({
    url: '/alarm/hiddendanger/specialType',
    method: 'get',
  })
}