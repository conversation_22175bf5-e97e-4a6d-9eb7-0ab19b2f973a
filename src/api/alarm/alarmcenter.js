import request from '@/utils/request'

/**
 * 查询矿井机构树统计
 * @param {*} isAlarm 1:统计已报警， 2：统计报警取消
 * @returns
 */
export function listAlarmTreeCount(query) {
  return request({
    url: '/alarm/count/treeselect',
    method: 'get',
    params: query
  })
}
/**
 * 历史报警
 */
export function getHistoryList(query) {
  return request({
    url: '/alarm/alarmDetail/list',
    method: 'get',
    params: query
  })
}


/**
 * 历史报警
 */
export function getChildHistoryList(query) {
  return request({
    url: '/alarm/alarmChildDetail/list',
    method: 'get',
    params: query
  })
}


// 修改报警事件
export function updateAlarmDetail(data) {
  return request({
    url: '/alarm/alarmDetail/alarmHandle',
    method: 'post',
    data: data
  })
}

// 修改负责人
export function updateDirector(data) {
  return request({
    url: '/alarm/alarmDetail/updateDirector',
    method: 'post',
    data: data
  })
}

// 修改报警事件
export function updateAlarmChildDetail(data) {
  return request({
    url: '/alarm/alarmChildDetail/alarmHandle',
    method: 'post',
    data: data
  })
}




// 查询报警等级日志
export function getLevelLog(data) {
  return request({
    url: '/alarm/alarmDetail/getLevelLog',
    method: 'post',
    data: data
  })
}
// 查询处置日志
export function getConfirmLog(data) {
  return request({
    url: '/alarm/alarmDetail/getConfirmLog',
    method: 'post',
    data: data
  })
}
// 查询报警指标日志
export function getValueLog(data) {
  return request({
    url: '/alarm/alarmDetail/getValueLog',
    method: 'post',
    data: data
  })
}

// 查询报警事件关联指标下拉选项
export function getIndexSelectList(data) {
  return request({
    url: '/alarm/alarmDetail/getIndexSelectList',
    method: 'post',
    data: data
  })
}

// 发起群聊
export function creatChartGroup(data) {
  return request({
    url: '/alarm/alarmDetail/creatChartGroup',
    method: 'post',
    data: data
  })
}

// 近七天报警时长统计
export function getChartData(data) {
  return request({
    url: '/alarm/alarmChildDetail/getChartData',
    method: 'post',
    data: data
  })
}

/**
 * 获取echarts配置信息
 */
export function getLevelOption(data) {
  const option = {
    grid: {
      top: '3%',
      left: '3%',
      right: '3%',
      bottom: '13%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params){
        var text = '';
        if (params[0].data[1] == 1) {
          text = '蓝色报警'
        } else if (params[0].data[1] == 2) {
          text = '黄色报警'
        } else if (params[0].data[1] == 3) {
          text = '橙色报警'
        } else if (params[0].data[1] == 4) {
          text = '红色报警'
        }
        return params[0].data[0]
        + '</br>'
        + '<span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;left:5px;background-color:red"></span>'
        + text;
      }
    },
    xAxis: {
      type: 'category',
      splitLine: {
        show: true
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 5,
      interval: 1,
      axisLabel: {
        show: true,
        formatter: function (value) {
          var texts = [];
          if (value == 1) {
            texts.push('蓝色报警');
          } else if (value == 2) {
            texts.push('黄色报警');
          } else if (value == 3) {
            texts.push('橙色报警');
          } else if (value == 4) {
            texts.push('红色报警');
          }
          return texts;
        }
      }
    },
    series: [
      {
        data: data,
        type: 'line'
      }
    ]
  };
  return option;
}
/**
 * 获取echarts配置信息
 */
export function getActualOption(data) {
  const option = {
    grid: {
      top: '3%',
      left: '3%',
      right: '3%',
      bottom: '13%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params){
        var text = params[0].data[1];
        return params[0].data[0]
        + '</br>'
        + '<span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;left:5px;background-color:red"></span>'
        + text;
      }
    },
    xAxis: {
      type: 'category',
      splitLine: {
        show: true
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        show: true
      }
    },
    series: [
      {
        data: data,
        type: 'line'
      }
    ]
  };
  return option;
}

/**
 * 获取echarts配置信息
 */
export function getChartOption(data) {
  const option = {
   xAxis: {
     type: 'category',
     data: data.days
   },
   yAxis: {
     type: 'value'
   },
   series: [
     {
       data: data.datas,
       type: 'bar',
       barWidth:50,
       label: {
         show: true,
           position: 'top',
           valueAnimation: true
       },
       itemStyle:{
         normal:{
           color:(params)=>{
             var colorList = ['#c23531','#2f4554', '#61a0a8', '#d48265', '#91c7ae','#749f83', '#ca8622'];
             return colorList[params.dataIndex]
           }
         }
       }
     }
   ]
  };
  return option;
}
/**
 * 初始化表头
 */
export function initColumns(query) {
  const columns = [
    {
      label:'报警事件名称',
      prop:'alarmName'
    },
    {
      label:'位置信息',
      prop:'positionName'
    },
    {
      label:'当前级别',
      prop:'currentLevelName'
    },
    // {
    //   label:'最高级别',
    //   prop:'maxLevelName'
    // },
    {
      label:'信息来源',
      prop:'sysName'
    },
    {
      label:'业务类型',
      prop:'eventTypeName'
    },
    {
      label:'专业',
      prop:'specialTypeName'
    },
    {
      label:'报警开始时间',
      prop:'startTime'
    },
    {
      label:'报警结束时间',
      prop:'endTime'
    },
    {
      label:'报警时长',
      prop:'duration'
    },
    {
      label:'负责人',
      prop:'manager'
    },
    {
      label:'报警状态',
      prop:'status'
    }];
  return columns;
}
/**
 * 初始化表头
 */
export function initChildColumns(query) {
  const columns = [
    {
      label:'系统名称',
      prop:'sysName'
    },
    {
      label:'子系统名称',
      prop:'subSysName'
    },
    {
      label:'设备名称',
      prop:'devName'
    },
    {
      label:'指标名称',
      prop:'prop'
    },
    {
      label:'当前级别',
      prop:'currentLevelName'
    },
    {
      label:'报警开始时间',
      prop:'startTime'
    },
    {
      label:'报警结束时间',
      prop:'endTime'
    },
    {
      label:'报警时长',
      prop:'durationName'
    },
    {
      label:'报警状态',
      prop:'status'
    },
    ];
  return columns;
}
