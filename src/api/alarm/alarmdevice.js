import request from '@/utils/request'

/**
 * 查询系统设备树
 * @returns
 */
export function getSystemTree(query) {
  return request({
    url: '/alarm/alarmDevice/getSystemTree',
    method: 'get',
    params: query
  })
}

/**
 * 查询子系统
 * @returns
 */
export function getSystemChild(systemId) {
    return request({
      url: '/alarm/alarmDevice/getSystemChild/' + systemId,
      method: 'get'
    })
}


/**
 * 查询设备
 * @returns
 */
export function getDeviceBySystemId(systemId) {
    return request({
      url: '/alarm/alarmDevice/getDeviceBySystemId/' + systemId,
      method: 'get'
    })
}

/**
 * 查询设备属性
 * @returns
 */
export function getPropertyByDeviceId(deviceId) {
    return request({
      url: '/alarm/alarmDevice/getPropertyByDeviceId/' + deviceId,
      method: 'get'
    })
}

// 查询用户列表
export function getUserList(query) {
  return request({
    url: '/alarm/alarmDevice/getUserList',
    method: 'get',
    params: query
  })
}
// 查询已选用户列表
export function getSelectUser(query) {
  return request({
    url: '/alarm/alarmDevice/getSelectUser',
    method: 'get',
    params: query
  })
}

// 添加消息接收人
export function saveConfig(data) {
  return request({
    url: '/alarm/alarmDevice/saveConfig',
    method: 'post',
    data: data
  })
}
// 查询报警主管
export function getLeader(data) {
  return request({
    url: '/alarm/alarmDevice/getLeader',
    method: 'post',
    data: data
  })
}
// 保存报警主管
export function saveLeader(data) {
  return request({
    url: '/alarm/alarmDevice/saveLeader',
    method: 'post',
    data: data
  })
}
