  import request from '@/utils/request'

// 查询风险辨识记录列表
export function listRiskidentify(query) {
  return request({
    url: '/alarm/riskidentify/list',
    method: 'get',
    params: query
  })
}

// 查询风险辨识记录详细
export function getRiskidentify(id) {
  return request({
    url: '/alarm/riskidentify/' + id,
    method: 'get'
  })
}

// 新增风险辨识记录
export function addRiskidentify(data) {
  return request({
    url: '/alarm/riskidentify',
    method: 'post',
    data: data
  })
}

// 修改风险辨识记录
export function updateRiskidentify(data) {
  return request({
    url: '/alarm/riskidentify',
    method: 'put',
    data: data
  })
}

// 删除风险辨识记录
export function delRiskidentify(id) {
  return request({
    url: '/alarm/riskidentify/' + id,
    method: 'delete'
  })
}


// 查询风险辨识人员详细
export function getIdentifymember(id) {
  return request({
    url: '/alarm/identifymember/' + id,
    method: 'get'
  })
}
