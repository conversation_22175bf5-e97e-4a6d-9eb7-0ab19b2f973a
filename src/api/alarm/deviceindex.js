import request from '@/utils/request'

// 查询设备指标列表
// export function listDeviceindex(query) {
//   return request({
//     url: '/alarm/deviceindex/list',
//     method: 'get',
//     params: query
//   })
// }

// 查询设备指标详细
export function getDeviceindex(id) {
  return request({
    url: '/alarm/deviceindex/' + id,
    method: 'get'
  })
}

// 新增设备指标
export function addDeviceindex(data) {
  return request({
    url: '/alarm/deviceindex',
    method: 'post',
    data: data
  })
}

// 修改设备指标
export function updateDeviceindex(data) {
  return request({
    url: '/alarm/deviceindex',
    method: 'put',
    data: data
  })
}

// 删除设备指标
export function delDeviceindex(id) {
  return request({
    url: '/alarm/deviceindex/' + id,
    method: 'delete'
  })
}

// 设备分类id查询设备指标列表
export function listDeviceindexByDeviceClassId(query) {
  return request({
    url: '/alarm/deviceindex/list/',
    method: 'get',
    params: query
  })
}

//获取设备下拉树，添加设备
export function getTree(){
  return request({
    url: 'alarm/deviceindex/treeselect',
    method: 'get'

  })
}

//测试链接
export function connectionTest(data){
  return request({
    url: 'alarm/deviceindex/connectiontest',
    method: 'post',
    data: data
  })
}

//通过设备id数组查询设备指标
export function listDeviceindexByDeviceIds(ids){
  return request({
    url: 'alarm/deviceindex/selectByIds/'+ids,
    method: 'get',
  });
}

// 根据事件ID查询指标
export function selectIndexByEventId(id){
  return request({
    url: 'alarm/deviceindex/selectByEventId/'+id,
    method: 'get'
  });
}
