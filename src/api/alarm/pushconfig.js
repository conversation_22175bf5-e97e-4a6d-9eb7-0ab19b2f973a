import request from '@/utils/request'

// 查询推送配置列表
export function listPushconfig(query,id) {
  return request({
    url: '/alarm/pushconfig/list',
    method: 'get',
    params: query
  })
}

// 查询推送配置详细
export function getPushconfig(id) {
  return request({
    url: '/alarm/pushconfig/' + id,
    method: 'get'
  })
}

// 新增推送配置
export function addPushconfig(data) {
  return request({
    url: '/alarm/pushconfig',
    method: 'post',
    data: data
  })
}

// 修改推送配置
export function updatePushconfig(data) {
  return request({
    url: '/alarm/pushconfig',
    method: 'put',
    data: data
  })
}

// 删除推送配置
export function delPushconfig(id) {
  return request({
    url: '/alarm/pushconfig/' + id,
    method: 'delete'
  })
}

// 获取设备分类下拉树
export function getDeptUserTree(type){
  return request({
    url: '/alarm/pushconfig/treeselect/' + type,
    method: 'get'
  })
}

// 查询推送报警模板详细
export function listPushconfigByUserId(query,id) {
  return request({
    url: '/alarm/pushconfig/getListByUserId/' + id,
    method: 'get',
    params: query
  })
}

