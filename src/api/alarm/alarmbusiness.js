import request from '@/utils/request'

// 查询业务台账列表
export function listAlarmbusiness(query) {
  return request({
    url: '/alarm/alarmbusiness/list',
    method: 'get',
    params: query
  })
}

// 查询业务台账详细
export function getAlarmbusiness(id) {
  return request({
    url: '/alarm/alarmbusiness/' + id,
    method: 'get'
  })
}

// 新增业务台账
export function addAlarmbusiness(data) {
  return request({
    url: '/alarm/alarmbusiness',
    method: 'post',
    data: data
  })
}

// 修改业务台账
export function updateAlarmbusiness(data) {
  return request({
    url: '/alarm/alarmbusiness',
    method: 'put',
    data: data
  })
}

// 删除业务台账
export function delAlarmbusiness(id) {
  return request({
    url: '/alarm/alarmbusiness/' + id,
    method: 'delete'
  })
}

// 获取业务台账树
export function getBusinessTree() {
  return request({
    url: '/alarm/alarmbusiness/treeselect',
    method: 'get'
  })
}

// 获取业务ID
export function getBusinessId(recordId) {
  return request({
    url: '/alarm/alarmbusiness/getBusinessId/'+ recordId,
    method: 'get'
  })
}



