import request from '@/utils/request'

// 查询上报政府列表
export function listAlarmreport(query) {
  return request({
    url: '/alarm/alarmreport/list',
    method: 'get',
    params: query
  })
}

// 查询上报政府详细
export function getAlarmreport(id) {
  return request({
    url: '/alarm/alarmreport/' + id,
    method: 'get'
  })
}

// 新增上报政府
export function addAlarmreport(data) {
  return request({
    url: '/alarm/alarmreport',
    method: 'post',
    data: data
  })
}

// 修改上报政府
export function updateAlarmreport(data) {
  return request({
    url: '/alarm/alarmreport',
    method: 'put',
    data: data
  })
}

// 删除上报政府
export function delAlarmreport(id) {
  return request({
    url: '/alarm/alarmreport/' + id,
    method: 'delete'
  })
}
