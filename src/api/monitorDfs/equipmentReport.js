import request from '@/utils/request'

// 系统名称列表
export function reportSystems(params) {
  return request({
    url: '/monitor/device/report/systems',
    method: 'get',
    params
  })
}
// 设备运行报表
export function deviceReport(params) {
  return request({
    url: '/monitor/device/report',
    method: 'get',
    params
  })
}
// 设备运行报表详情
export function reportDetail(params) {
  return request({
    url: '/monitor/device/report/detail',
    method: 'get',
    params
  })
}