import request from '@/utils/request'

let parameterAnalysis = {


  getPropertyByTree:function(treeId,mid){
    return request({
      url: '/monitor/paramAnalysis/getPropertyByTreeId/' + treeId +'/'+mid,
      method: 'get'
    })
  },

  querySpotInfo:function (data) {
    return request({
      url: '/monitor/paramAnalysis/querySpotInfo',
      method: 'post',
      data:data
    })
  },

  // 新增条件配置
  addCondition: function (data) {
    return request({
      url: '/monitor/analysis',
      method: 'post',
      data: data
    })
  },

// 修改条件配置
  updateCondition: function (data) {
    debugger
    return request({
      url: '/monitor/analysis',
      method: 'put',
      data: data
    })
  },

  // 获取条件配置下拉
  getConditionSelect:function(){
    return request({
      url: '/monitor/analysis/getConditionSelect',
      method: 'get'
    })
  },

  // 获取条件配置详情
  getConditionById:function(id){
    return request({
      url: '/monitor/analysis/' + id,
      method: 'get'
    })
  },


  // 获取统计图数据
  getEChartData:function(data){
    return request({
      url: '/monitor/paramAnalysis/getEChartData' ,
      method: 'post',
      data:data
    })
  },

  // 删除煤种
  delConditional: function delConditional(id) {
    return request({
      url: '/monitor/analysis/' + id,
      method: 'delete'
    })
  }


}


export default parameterAnalysis
