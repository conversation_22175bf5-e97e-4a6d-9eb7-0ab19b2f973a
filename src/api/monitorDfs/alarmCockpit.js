import request from '@/utils/request'

// 设备概况统计
export function getEquipmentOverview() {
  return request({
    url: '/monitor/alarmCockpit/getEquipmentOverview',
    method: 'get'
  })
}


// 查询预警情况数据
export function getEarlyWarning() {
  return request({
    url: '/monitor/alarmCockpit/getEarlyWarning',
    method: 'get'
  })
}


// 查询设备完好率
export function getIntactRate() {
  return request({
    url: '/monitor/alarmCockpit/getIntactRate',
    method: 'get'
  })
}


// 查询用电量
export function getElectricity() {
  return request({
    url: '/monitor/alarmCockpit/getElectricity',
    method: 'get'
  })
}


// 查询负载
export function getload() {
  return request({
    url: '/monitor/alarmCockpit/getload',
    method: 'get'
  })
}


// 查询预览分类
export function getEarlyAnalysis() {
  return request({
    url: '/monitor/alarmCockpit/getEarlyAnalysis',
    method: 'get'
  })
}


// 查询设备分布
export function getDeviceScatterData(query) {
  return request({
    url: '/monitor/alarmCockpit/getDeviceScatter',
    method: 'get',
    params: query
  })
}


// 查询预警记录
export function getWarningRecord(query) {
  return request({
    url: '/monitor/alarmCockpit/getWarningRecord',
    method: 'get',
    params: query
  })
}

// 查询重大设备状态
export function getMajorEquipmentStatus(query) {
  return request({
    url: '/monitor/alarmCockpit/getMajorEquipmentStatus',
    method: 'get',
    params: query
  })
}


// 查询设备运行状态分析
export function getDevRunAnalysis(query) {
  return request({
    url: '/monitor/alarmCockpit/getDevRunAnalysis',
    method: 'get',
    params: query

  })
}
