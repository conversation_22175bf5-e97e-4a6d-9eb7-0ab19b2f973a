import request from '@/utils/request'

let equipmentMonitor = {
  // 查询设备树
  treeTslSystemSelect: function () {
    return request({
      url: '/system/equipment/treeTslSystemSelect',
      method: 'get'
    })
  },


  treeTslSystemSelectConfig: function (query) {
    return request({
      url: '/system/equipment/treeTslSystemSelectConfig',
      method: 'get',
      params: query
    })
  },

  treeEquipmentSelectConfig: function (query) {
    return request({
      url: '/system/equipment/treeEquipmentSelectConfig',
      method: 'get',
      params: query
    })
  },


  tmpOperList: function (query) {
    return request({
      url: '/monitor/tmpOper/list',
      method: 'get',
      params: query
    })
  },


  treeEquipmentSelect: function () {
    return request({
      url: '/system/equipment/treeEquipmentSelect',
      method: 'get'
    })
  },

  getDevList: function () {
    return request({
      url: '/system/equipment/getDevList',
      method: 'get'
    })
  },

  //获取设备信息
  getEquipment: function (id) {
    return request({
      url: '/system/equipment/' + id,
      method: 'get'
    })
  },

  //获取子系统设备状态
  getSystemDevState: function (query) {
    return request({
      url: '/monitor/equipmentMonitor/getSystemDevState',
      method: 'get',
      params: query
    })
  },

  // 通过设备全名称获取设备ID和名称
  getDevInfoByDevLName: function (query) {
    return request({
      url: '/system/equipment/getDevInfoByDevLName',
      method: 'get',
      params: query
    })
  },

  getConfiguringNodes: function (type) {
    return request({
      url: '/monitor/equipmentMonitor/getConfiguringNodes/' +type,
      method: 'get'
    })
  },

  // 新增条件配置
  addNodeConfig: function (data) {
    return request({
      url: '/monitor/equipmentMonitor/addNodeConfig',
      method: 'post',
      data: data
    })
  },
}

export default equipmentMonitor



