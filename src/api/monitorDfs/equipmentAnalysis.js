import request from '@/utils/request'

let equipmentAnalysis = {

  //查询设备统计分析统计图
  selectRiskTrend: function (deviceId) {
    return request({
      url: '/monitor/monitorDevice/selectRiskTrend/' + deviceId,
      method: 'get'
    })
  },

  listAlarms: function (query) {
    return request({
      url: '/monitor/equipmentOperationAnalysis/pageList',
      method: 'get',
      params: query
    })
  },

  /**设备运行统计 */
  selectEquipmentOperationAnalysis: function (query) {
    return request({
      url: '/monitor/equipmentOperationAnalysis/getEquipmentOperationAnalysisResult',
      method: 'get',
      params: query
    })
  },

  /**设备统计分析 */
  selectEquipmentStatisticsAnalysis: function (query) {
    return request({
      url: '/monitor/equipmentOperationAnalysis/getEquipmentStatisticsAnalysisResult',
      method: 'get',
      params: query
    })
  },

  /**指标 */
  selectIndexList: function (deviceId) {
    return request({
      url: '/monitor/equipmentOperationAnalysis/getEquipmentIndexList/' + deviceId,
      method: 'get'
    })
  },


  getOverview: function (subsystemId) {
    return request({
      url: '/monitor/equipmentMonitor/getOverview/' + subsystemId,
      method: 'get'
    })
  },

  getAlarmList: function (query) {
    return request({
      url: '/monitor/equipmentMonitor/getAlarmList',
      method: 'get',
      params: query
    })
  },

  geteEquipmentInfo: function (deviceId) {
    return request({
      url: '/system/equipment/getInfoByDeviceId/' + deviceId,
      method: 'get'
    })
  },


}


export default equipmentAnalysis
