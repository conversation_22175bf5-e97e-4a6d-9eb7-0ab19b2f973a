import request from '@/utils/request'

// 查询列表
export function listListener(query) {
  return request({
    url: '/activiti/listener/list',
    method: 'get',
    params: query
  })
}

// 根据id查询详细
export function getListener(id) {
  return request({
    url: '/activiti/listener/' + id,
    method: 'get'
  })
}

// 新增
export function addListener(data) {
  return request({
    url: '/activiti/listener',
    method: 'post',
    data: data
  })
}

// 修改
export function updateListener(data) {
  return request({
    url: '/activiti/listener',
    method: 'put',
    data: data
  })
}

// 删除
export function delListener(id) {
  return request({
    url: '/activiti/listener/' + id,
    method: 'delete'
  })
}