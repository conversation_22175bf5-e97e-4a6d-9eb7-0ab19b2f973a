import request from '@/utils/request'

// 查询列表
export function listGroup(query) {
  return request({
    url: '/activiti/group/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getGroup(id) {
  return request({
    url: '/activiti/group/' + id,
    method: 'get'
  })
}

// 新增
export function addGroup(data) {
  return request({
    url: '/activiti/group',
    method: 'post',
    data: data
  })
}

// 修改
export function updateGroup(data) {
  return request({
    url: '/activiti/group',
    method: 'put',
    data: data
  })
}

// 删除
export function delGroup(id) {
  return request({
    url: '/activiti/group/' + id,
    method: 'delete'
  })
}
// 获取所有流程角色
export function getRole() {
  return request({
    url: '/activiti/act/role/list',
    method: 'get'
  })
}
// 获取所有用户列表
export function getUser() {
  return request({
    url: '/activiti/group/queryUserList',
    method: 'get'
  })
}