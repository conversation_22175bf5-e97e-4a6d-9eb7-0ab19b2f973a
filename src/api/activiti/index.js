import request from '@/utils/request'
import router from '../../router'

// 获取我的待办、已办、消息统计
export function getStatistics() {
  return request({
    url: '/activiti/act/task/getIndexCount',
    method: 'get'
  })
}
//我的待办总数
export function count() {
  return request({
    url: '/activiti/act/task/my/doing/count',
    method: 'get'
  })
}
// 获取我的待办分类统计
export function getTodoTaskSizes() {
  return request({
    url: '/activiti/act/task/getOtherTodoTaskSizes',
    method: 'get'
  })
}

// 获取我的待办分类列表查询
export function getMydoing(query) {
  return request({
    url: '/activiti/act/task/my/doing',
    method: 'get',
    params:query
  })
}
//获取我的待办详情页面
export function getMydoingDetail(data) {
  return request({
    url: '/activiti/act/task/approve',
    method: 'post',
    data:data
  })
}
//代办详情页面审批通过
export function approveOnAgree(data) {
  return request({
    url: '/activiti/act/task/submit',
    method: 'post',
    data:data
  })
}
//代办详情页面审批驳回
export function approveOnTurnDown(data) {
  return request({
    url: '/activiti/act/task/reject',
    method: 'post',
    data:data
  })
}
//获取用户列表
export function getUserList(query) {
  return request({
    url: '/activiti/group/queryUserList',
    method: 'get',
    params:query
  })
}
//代办转办
export function transfer(id) {
  return request({
    url: `/activiti/act/task/${id}/relay`,
    method: 'post',
  })
}

// 获取我的已办分类统计
export function getDoneTaskSizes() {
  return request({
    url: '/activiti/act/task/getOtherDoneTaskSizes',
    method: 'get'
  })
}
// 获取我的已办分类列表查询
export function getMycomplete(query) {
  return request({
    url: '/activiti/act/task/my/complete',
    method: 'get',
    params:query
  })
}
//获取我的已办详情页面
export function getMycompleteDetail(data) {
  return request({
    url: '/activiti/act/task/my/done/detail',
    method: 'post',
    data:data
  })
}

// 获取我的提交分类列表查询
export function getMysumbit(query) {
  return request({
    url: '/activiti/act/task/my/submit',
    method: 'get',
    params:query
  })
}
// 获取我的历史提交分类列表查询
export function getMyhisumbit(query) {
  return request({
    url: '/activiti/act/task/my/hiSubmitTask',
    method: 'get',
    params:query
  })
}
//获取我的提交详情页面
export function getMysubmitDetail(data) {
  return request({
    url: '/activiti/act/task/myRunSubmitDetail',
    method: 'post',
    data:data
  })
}
//获取我的历史提交详情页面
export function getMyhisubmitDetail(data) {
  return request({
    url: '/activiti/act/task/myCompleteSubmitDetail',
    method: 'post',
    data:data
  })
}

// 获取我的消息分类列表查询
export function getMynews(query) {
  return request({
    url: '/activiti/act/myMessage/search',
    method: 'get',
    params:query
  })
}
//获取我的消息详情页面
export function getMynewsDetail(id) {
  return request({
    url: `/activiti/act/myMessage/reading/${id}`,
    method: 'get',
  })
}

//判断该条数据是待办还是已办
export function getMyCompleteByTaskId(data) {
  return request({
    url: '/activiti/act/task/getMyCompleteByTaskId',
    method: 'post',
    data:data
  })
}

//处理门户跳转待办、已办详情
export function extracted(to) {
  var isType = false
  var routeUrl = to.query.targetUrl
  if (routeUrl.indexOf('@') != -1) {
    isType = true
  }
  var quickDevFormPath = ''
  if (!routeUrl.startsWith('/')) {
    let data = routeUrl.split('$')
    routeUrl = data[0]
    quickDevFormPath = data[1]
  }
  let data = {
    taskId: to.query.taskId,
  };
  getMyCompleteByTaskId(data).then((res) => {
    let query = {
      quickDevFormPath: quickDevFormPath,
      taskId: to.query.taskId,
      instId: to.query.instId,
      isType: isType //区分显示快开详情页和代码详情页
    }
    if(typeof(res.data) == "undefined"){
      query["deTaskId"] = to.query.id;
      query["processId"] = to.query.processId;
      query["onlyOne"] = "doing";
    }else{
      query["taskDefKey"] = res.data.taskDefKey;
      query["approveBy"] = res.data.approveBy;
      query["flowId"] = res.data.flowId;
      query["onlyOne"] = "complete";
    }
    router.push({
      path: routeUrl,
      query: query
    })
  });
}
