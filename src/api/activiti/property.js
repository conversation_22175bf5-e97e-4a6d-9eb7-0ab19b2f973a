import request from '@/utils/request'

// 查询列表
export function listProperty(query) {
  return request({
    url: '/activiti/property/list',
    method: 'get',
    params: query
  })
}

// id查询详细
export function getProperty(id) {
  return request({
    url: '/activiti/property/' + id,
    method: 'get'
  })
}

// 新增
export function addProperty(data) {
  return request({
    url: '/activiti/property',
    method: 'post',
    data: data
  })
}

// 修改
export function updateProperty(data) {
  return request({
    url: '/activiti/property',
    method: 'put',
    data: data
  })
}

// 删除
export function delProperty(id) {
  return request({
    url: '/activiti/property/' + id,
    method: 'delete'
  })
}