import request from "@/utils/request";

// 查询【业务类型】列表
export function listBusiness(query) {
  return request({
    url: "/flow/business/list",
    method: "get",
    params: query,
  });
}

// 查询【业务类型-流程实例】列表
export function listActModel(query) {
  return request({
    url: "/flow/business/getProcdefList",
    method: "get",
    params: query,
  });
}

// 查询【业务类型-表】列表
export function listBusinessTable(query) {
  return request({
    url: "/flow/business/getTableList",
    method: "get",
    params: query,
  });
}

// 查询【业务类型】详细
export function getBusiness(id) {
  return request({
    url: "/flow/business/" + id,
    method: "get",
  });
}

// 新增【业务类型】
export function addBusiness(data) {
  return request({
    url: "/flow/business",
    method: "post",
    data: data,
  });
}

// 修改【业务类型】
export function updateBusiness(data) {
  return request({
    url: "/flow/business/update",
    method: "post",
    data: data,
  });
}

// 删除【业务类型】
export function delBusiness(id) {
  return request({
    url: "/flow/business/delete/" + id,
    method: "post",
  });
}

// 查询【业务类型】详细
export function actDeptTreeselect(id) {
  return request({
    url: "/flow/business/actDeptTreeselect/" + id,
    method: "get",
  });
}

// 查询子表类型列表
export function listChildTableType(query) {
  return request({
    url: "/operationalplan/template/selectBusinessType",
    method: "get",
    params: query,
  });
}
