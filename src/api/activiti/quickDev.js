import request from '@/utils/request'
// 获取流程变量-快开平台
export function getProcessVal(query) {
    return request({
      url: '/activiti/table/getProcessVal',
      method: 'get',
      params: query
    })
  }
  // 启动流程
  export function start(data) {
    return request({
      url: '/activiti/act/process/start',
      method: 'post',
      data: data
    })
  }
  //查看审批历史
  export function taskHistory(id) {
    return request({
      url: '/activiti/act/task/'+id+'/detail',
      method: 'get',
    })
  }
  //更换审批人
  export function taskRun(processId,updateId,id) {
    return request({
      url: '/activiti/act/task/run/'+processId+'/'+updateId+'/'+id,
      method: 'get',
    })
  }
  //选择抄送人
  export function getUserList(query) {
    return request({
      url: '/activiti/group/queryUserList',
      method: 'get',
      params: query
    })
  }
  //根据实例id获取审批人
  export function getNextUserByInstId(instId) {
    return request({
      url: '/activiti/table/getNextUserByInstId?instId='+instId,
      method: 'get',
      params: query
    })
  }