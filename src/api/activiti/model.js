import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listModel(query) {
  return request({
    url: '/activiti/model/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getModel(id) {
  return request({
    url: '/activiti/model/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addModel(data) {
  return request({
    url: '/activiti/model',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateModel(data) {
  return request({
    url: '/activiti/model',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delModel(id) {
  return request({
    url: '/activiti/model/removeById/' + id,
    method: 'delete'
  })
}
// 获取角色组和角色关系
export function getGroup() {
  return request({
    url: '/activiti/act/group/list',
    method: 'get'
  })
}
// 获取所有流程角色列表
export function getRole() {
  return request({
    url: `/activiti/act/role/list`,
    method: 'get'
  })
}
//根据组Id查询流程角色列表
export function getRoleListByGroupId(id) {
  return request({
    url: `/activiti/group/getRoleListByGroupId/${id}`,
    method: 'get'
  })
}
// 获取所有用户列表
export function getUser(query) {
  return request({
    url: `/activiti/group/queryUserList`,
    method: 'get',
    params: query
  })
}
// 流程发布
export function publish(id) {
  return request({
    url: `/activiti/model/publish/${id}`,
    method: 'get'
  })
}
// 导入
export function importFile(data) {
  return request({
    url: '/activiti/act/modeler/import',
    method: 'post',
    data: data
  })
}
