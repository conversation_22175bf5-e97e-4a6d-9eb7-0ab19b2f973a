import request from '@/utils/request'


// 查询列表
export function listElement(query) {
  return request({
    url: '/activiti/element/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getElement(id) {
  return request({
    url: '/activiti/element/' + id,
    method: 'get'
  })
}

// 新增
export function addElement(data) {
  return request({
    url: '/activiti/element',
    method: 'post',
    data: data
  })
}

// 修改
export function updateElement(data) {
  return request({
    url: '/activiti/element',
    method: 'put',
    data: data
  })
}

// 删除
export function delElement(id) {
  return request({
    url: '/activiti/element/' + id,
    method: 'delete'
  })
}
//查询设置
export function getByElementId(id) {
  return request({
    url: '/activiti/element/queryPropertyIdsByElementId/' + id,
    method: 'get'
  })
}
//保存设置
export function saveElementId(data) {
  return request({
    url: '/activiti/element/saveElementProperty',
    method: 'post',
    data:data,
  })
}