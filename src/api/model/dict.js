import request from '@/utils/request'

// 查询列表
export function init() {
  return request({
    url: '/model/dict/init',
    method: 'post',
  })
}

// 更新模型
export function updateModel() {
  return request({
    url: '/model/dict/updateModel',
    method: 'post',
  })
}

// 查询列表
export function listDict(query) {
  return request({
    url: '/model/dict/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getDict(id) {
  return request({
    url: '/model/dict/' + id,
    method: 'get'
  })
}

// 新增
export function addDict(data) {
  return request({
    url: '/model/dict',
    method: 'post',
    data: data
  })
}

// 修改
export function updateDict(data) {
  return request({
    url: '/model/dict',
    method: 'put',
    data: data
  })
}

// 删除
export function delDict(id) {
  return request({
    url: '/model/dict/' + id,
    method: 'delete'
  })
}
