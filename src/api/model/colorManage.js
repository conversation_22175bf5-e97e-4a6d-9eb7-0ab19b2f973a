import request from '@/utils/request'

// 查询系统列表列表
export function listSystem() {
  return request({
    url: '/things/services/color/systems',
    method: 'get'
  })
}


export function iniProps(query) {
  return request({
    url: '/things/services/color/iniProps',
    method: 'get',
    params: query
  })
}


// 查询列表
export function listColorManage(query) {
  return request({
    url: '/things/services/color/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getManage(id) {
  return request({
    url: '/things/services/color/' + id,
    method: 'get'
  })
}

// 新增
export function addManage(data) {
  return request({
    url: '/things/services/color',
    method: 'post',
    data: data
  })
}

// 修改
export function updateManage(data) {
  return request({
    url: '/things/services/color',
    method: 'put',
    data: data
  })
}

// 删除
export function delManage(id) {
  return request({
    url: '/things/services/color/' + id,
    method: 'delete'
  })
}
