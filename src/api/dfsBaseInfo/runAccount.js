import request from '@/utils/request'

// 查询设备运行台账列表
export function listRunAccount(query) {
  return request({
    url: '/system/runAccount/list',
    method: 'get',
    params: query
  })
}

// 查询设备运行台账详细
export function getRunAccount(id) {
  return request({
    url: '/system/runAccount/' + id,
    method: 'get'
  })
}

// 新增设备运行台账
export function addRunAccount(data) {
  return request({
    url: '/system/runAccount',
    method: 'post',
    data: data
  })
}

// 修改设备运行台账
export function updateRunAccount(data) {
  return request({
    url: '/system/runAccount',
    method: 'put',
    data: data
  })
}

// 删除设备运行台账
export function delRunAccount(id) {
  return request({
    url: '/system/runAccount/' + id,
    method: 'delete'
  })
}
