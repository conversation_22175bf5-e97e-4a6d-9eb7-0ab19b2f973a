import request from '@/utils/request'

// 查询设备台账列表
export function listEquipment(query) {
  return request({
    url: '/system/equipment/list',
    method: 'get',
    params: query
  })
}

// 查询设备台账详细
export function getEquipment(id) {
  return request({
    url: '/system/equipment/' + id,
    method: 'get'
  })
}

// 新增设备台账
export function addEquipment(data) {
  return request({
    url: '/system/equipment',
    method: 'post',
    data: data
  })
}

// 修改设备台账
export function updateEquipment(data) {
  return request({
    url: '/system/equipment',
    method: 'put',
    data: data
  })
}

// 修改系统分类
export function updateSystem(data) {
  return request({
    url: '/system/equipment/system',
    method: 'put',
    data: data
  })
}

// 删除设备台账
export function delEquipment(id) {
  return request({
    url: '/system/equipment/' + id,
    method: 'delete'
  })
}


// 查询设备树
export function treeTslSystemSelect() {
  return request({
    url: '/system/equipment/treeTslSystemSelect',
    method: 'get'
  })
}
// 查询设备树
export function treeEquipmentSelect() {
  return request({
    url: '/system/equipment/treeEquipmentSelect',
    method: 'get'
  })
}

// 查询机构设备树
export function mineSelectUser() {
  return request({
    url: '/system/user/mineSelectUser',
    method: 'get'
  })
}

// 查询机构设备树
export function mineSelect() {
  return request({
    url: '/system/user/mineSelect',
    method: 'get'
  })
}


// 查询属性列表
export function getPropertyList(query) {
  return request({
    url: '/system/property/getPropertyList',
    method: 'get',
    params: query
  })
}


// 查询命令列表
export function getCommandList(query) {
  return request({
    url: '/system/command/getCommandList',
    method: 'get',
    params: query

  })
}

// 查询设备类型下拉
export function getDevTypeOption() {
  return request({
    url: '/monitor/option/getDevTypeOption',
    method: 'get'
  })
}


// 查询系统下设备信息
export function getDevInfoBySystemId(systemId) {
  return request({
    url: '/system/equipment/getDevInfoBySystemId/' + systemId,
    method: 'get'
  })
}

// 查询区队人员下拉
export function getUserByTeamList(deptId) {
  return request({
    url: '/monitor/option/getUserByTeamList/' + deptId,
    method: 'get'
  })
}

// 查询矿井基本信息详细
export function getMineNameByUserId() {
  return request({
    url: '/monitor/option/getMineNameByUserId',
    method: 'get'
  })
}

// 同步
export function syncDevice() {
  return request({
    url: '/system/equipment/syncDevice',
    method: 'post'
  })
}
