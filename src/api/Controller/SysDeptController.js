export default class SysDeptController {
  static list (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysDept/list', 'post', params, axiosOption, httpOption);
  }

  static view (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysDept/view', 'get', params, axiosOption, httpOption);
  }

  static export (sender, params, fileName) {
    return sender.download('flow/upms/sysDept/export', params, fileName);
  }

  static add (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysDept/add', 'post', params, axiosOption, httpOption);
  }

  static update (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysDept/update', 'post', params, axiosOption, httpOption);
  }

  static delete (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysDept/delete', 'post', params, axiosOption, httpOption);
  }

  static listNotInSysDeptPost (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysDept/listNotInSysDeptPost', 'post', params, axiosOption, httpOption);
  }

  static listSysDeptPost (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysDept/listSysDeptPost', 'post', params, axiosOption, httpOption);
  }

  static addSysDeptPost (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysDept/addSysDeptPost', 'post', params, axiosOption, httpOption);
  }

  static updateSysDeptPost (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysDept/updateSysDeptPost', 'post', params, axiosOption, httpOption);
  }

  static deleteSysDeptPost (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysDept/deleteSysDeptPost', 'post', params, axiosOption, httpOption);
  }

  static viewSysDeptPost (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysDept/viewSysDeptPost', 'get', params, axiosOption, httpOption);
  }
}
