export default class SysUserController {
  static list (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysUser/list', 'post', params, axiosOption, httpOption);
  }

  static view (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysUser/view', 'get', params, axiosOption, httpOption);
  }

  static export (sender, params, fileName) {
    return sender.download('flow/upms/sysUser/export', params, fileName);
  }

  static add (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysUser/add', 'post', params, axiosOption, httpOption);
  }

  static update (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysUser/update', 'post', params, axiosOption, httpOption);
  }

  static delete (sender, params, axiosOption, httpOption) {
    return sender.doUrl('flow/upms/sysUser/delete', 'post', params, axiosOption, httpOption);
  }
}
