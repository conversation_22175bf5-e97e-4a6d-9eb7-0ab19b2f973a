import request from '@/utils/request'

// 查询工作面列表
export function listWorkface(query) {
  return request({
    url: '/operationalplan/workface/list',
    method: 'get',
    params: query
  })
}

// 查询工作面详细
export function getWorkface(id) {
  return request({
    url: '/operationalplan/workface/' + id,
    method: 'get'
  })
}

// 新增工作面
export function addWorkface(data) {
  return request({
    url: '/operationalplan/workface',
    method: 'post',
    data: data
  })
}

// 修改工作面
export function updateWorkface(data) {
  return request({
    url: '/operationalplan/workface',
    method: 'put',
    data: data
  })
}

// 删除工作面
export function delWorkface(id) {
  return request({
    url: '/operationalplan/workface/' + id,
    method: 'delete'
  })
}

// 查询生产动态日报
export function getDailyList(wfId) {
  return request({
    url: '/operationalplan/workface/daily/' + wfId,
    method: 'get'
  })
}

// 查询采煤队下拉
export function getCoalTeamOption() {
  return request({
    url: '/operationalplan/option/getCoalTeamOption',
    method: 'get'
  })
}

// 查询采区下拉
export function getPanelOption() {
  return request({
    url: '/operationalplan/option/getPanelOption',
    method: 'get'
  })
}

// 查询接续工作面下拉（状态=备采=1）
export function getContinueWorkFaceOption(id) {
  return request({
    url: '/operationalplan/option/getContinueWorkFaceOption/'+ id,
    method: 'get'
  })
}
