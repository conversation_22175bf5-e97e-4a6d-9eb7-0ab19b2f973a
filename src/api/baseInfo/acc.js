/*
 * @Author: <PERSON>@coalmineinfo.cn
 * @Date: 2022-08-26 19:23:58
 * @LastEditors: <PERSON>@coalmineinfo.cn
 * @LastEditTime: 2022-08-29 13:39:59
 * @FilePath: \coalmine-ui\src\api\baseInfo\acc.js
 * @Description: 业务台账接口
 */
import request from '@/utils/request';
import { baseUrl } from './common';
const commonUrl = baseUrl + '/businessBook';

// 新增台账
export const addBusinessBook = (data) => request({
    url: commonUrl + '/add',
    method: 'post',
    data
});

// 获取台账详情;
export const getDetail = (params) => request({
    url: commonUrl + '/getById',
    method: 'get',
    params
});

// 更新台账信息
export const updateBusinessBook = (data) => request({
    url: commonUrl + '/update',
    method: 'post',
    data
});

// 管理部门
export const getDeptList = () => request({
    url: baseUrl+'/dept/list',
    method: 'get'
});

// 违章人
export const getUserList = () => request({
    url: baseUrl + '/user/list',
    method: 'get'
});