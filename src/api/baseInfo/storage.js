import request from '@/utils/request'

// 查询煤仓列表
export function listStorage(query) {
  return request({
    url: '/operationalplan/storage/list',
    method: 'get',
    params: query
  })
}

// 查询煤仓详细
export function getStorage(id) {
  return request({
    url: '/operationalplan/storage/' + id,
    method: 'get'
  })
}

// 新增煤仓
export function addStorage(data) {
  return request({
    url: '/operationalplan/storage',
    method: 'post',
    data: data
  })
}

// 修改煤仓
export function updateStorage(data) {
  return request({
    url: '/operationalplan/storage',
    method: 'put',
    data: data
  })
}

// 删除煤仓
export function delStorage(id) {
  return request({
    url: '/operationalplan/storage/' + id,
    method: 'delete'
  })
}

// 查询煤种下拉
export function getCoalNameList() {
  return request({
    url: '/operationalplan/option/getCoalNameList',
    method: 'get'
  })
}


// 修改状态
export function updateStartStop(id,state) {
  return request({
    url: '/operationalplan/storage/updateStartStop/'+ id +'/' + state,
    method: 'get'
  })
}


