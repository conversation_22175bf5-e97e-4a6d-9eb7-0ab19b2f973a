import request from '@/utils/request'

// 查询设备台账列表
export function listEquipment(query) {
  return request({
    url: '/operationalplan/equipment/list',
    method: 'get',
    params: query
  })
}

// 查询设备台账详细
export function getEquipment(id) {
  return request({
    url: '/operationalplan/equipment/' + id,
    method: 'get'
  })
}

// 新增设备台账
export function addEquipment(data) {
  return request({
    url: '/operationalplan/equipment',
    method: 'post',
    data: data
  })
}

// 修改设备台账
export function updateEquipment(data) {
  return request({
    url: '/operationalplan/equipment',
    method: 'put',
    data: data
  })
}

// 删除设备台账
export function delEquipment(id) {
  return request({
    url: '/operationalplan/equipment/' + id,
    method: 'delete'
  })
}


// 查询设备树
export function treeEquipmentSelect() {
  return request({
    url: '/operationalplan/equipment/treeEquipmentSelect',
    method: 'get'
  })
}

// 查询机构设备树
export function mineSelectUser() {
  return request({
    url: '/system/user/mineSelectUser',
    method: 'get'
  })
}

// 查询机构设备树
export function mineSelect() {
  return request({
    url: '/system/user/mineSelect',
    method: 'get'
  })
}
