import request from '@/utils/request'

// 查询矿井基本信息列表
export function listMine(query) {
  return request({
    url: '/operationalplan/mine/list',
    method: 'get',
    params: query
  })
}

// 查询矿井名称列表
export function getMineNameList() {
  return request({
    url: '/operationalplan/option/getMineNameList',
    method: 'get'
  })
}

// 查询矿井基本信息详细
export function getMine(id) {
  return request({
    url: '/operationalplan/mine/' + id,
    method: 'get'
  })
}

// 新增矿井基本信息
export function addMine(data) {
  return request({
    url: '/operationalplan/mine',
    method: 'post',
    data: data
  })
}

// 修改矿井基本信息
export function updateMine(data) {
  return request({
    url: '/operationalplan/mine',
    method: 'put',
    data: data
  })
}

// 删除矿井基本信息
export function delMine(id) {
  return request({
    url: '/operationalplan/mine/' + id,
    method: 'delete'
  })
}

// 查询矿井基本信息详细
export function getMineNameByUserId() {
  return request({
    url: '/operationalplan/mine/getMineNameByUserId',
    method: 'get'
  })
}


// 修改矿井状态
export function updateStartStop(id,state) {
  return request({
    url: '/operationalplan/mine/updateStartStop/'+ id +'/' + state,
    method: 'get'
  })
}
