/*
 * @Author: <PERSON>@coalmineinfo.cn
 * @Date: 2022-08-27 17:46:23
 * @LastEditors: <PERSON> w<PERSON>@coalmineinfo.cn
 * @LastEditTime: 2022-08-29 18:05:37
 * @FilePath: \coalmine-ui\src\api\baseInfo\comprehensiveMng.js
 * @Description: 综合指标管理 API
 */

import request from '@/utils/request';
import { baseUrl } from './common';

const commonUrl = baseUrl + '/businessIndicator';

// 新增
export const addBusinessIndicator = (params) => request({
    url: commonUrl + '/add',
    method: 'post',
    params
});

// 查询业务指标基础信息
export const getBasicIndicators = (params) => request({
    url: commonUrl + '/listBase',
    method: 'get',
    params
});
// 添加业务基础指标数据
export const addBasicIndicator = (data) => request({
    url: commonUrl + '/select',
    method: 'post',
    data
});
// 获取树状结构数据
export const getTree = (params) => request({
    url: baseUrl + '/business/getTree',
    method: 'get',
    params
});

// 移除
export const removeIndicator = (data) => request({
    url: commonUrl + '/remove',
    method: 'post',
    data
});