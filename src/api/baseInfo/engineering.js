import request from '@/utils/request'

// 查询工程管理列表
export function listEngineering(query) {
  return request({
    url: '/operationalplan/engineering/list',
    method: 'get',
    params: query
  })
}

// 查询工程管理详细
export function getEngineering(id) {
  return request({
    url: '/operationalplan/engineering/' + id,
    method: 'get'
  })
}

// 新增工程管理
export function addEngineering(data) {
  return request({
    url: '/operationalplan/engineering',
    method: 'post',
    data: data
  })
}

// 修改工程管理
export function updateEngineering(data) {
  return request({
    url: '/operationalplan/engineering',
    method: 'put',
    data: data
  })
}

// 删除工程管理
export function delEngineering(id) {
  return request({
    url: '/operationalplan/engineering/' + id,
    method: 'delete'
  })
}


// 查询工程文档
export function getEngineeringFile(engineId) {
  return request({
    url: '/operationalplan/engineering/file/' + engineId,
    method: 'get'
  })
}

// 修改状态
export function updateStartStop(id,state) {
  return request({
    url: '/operationalplan/engineering/updateStartStop/'+ id +'/' + state,
    method: 'get'
  })
}

// 查询督办部门下拉
export function getDeptListByMine() {
  return request({
    url: '/operationalplan/option/getDeptListByMine',
    method: 'get'
  })
}

// 查询施工单位下拉
export function getConstructionTeamOption() {
  return request({
    url: '/operationalplan/option/getConstructionTeamOption',
    method: 'get'
  })
}

// 查询部门人员下拉
export function getUserByTeamList(deptId) {
  return request({
    url: '/operationalplan/option/getUserByTeamList/' + deptId,
    method: 'get'
  })
}
