/*
 * @Author: <PERSON>@coalmineinfo.cn
 * @Date: 2022-08-25 17:18:17
 * @LastEditors: <PERSON>@coalmineinfo.cn
 * @LastEditTime: 2022-08-26 19:23:27
 * @FilePath: \coalmine-ui\src\api\baseInfo\composite.js
 * @Description: 综合管理接口
 */

import request from '@/utils/request';
import { baseUrl } from './common';

const commonUrl = baseUrl + '/business';
// 新增业务信息
export const addBusiness = (data) => request({
    url: commonUrl + '/add',
    method: 'post',
    data
});

// 获取业务详情
export const getDetail = (params) => request({
    url: commonUrl + '/getById',
    method: 'get',
    params
});

// 更新业务信息
export const updateBusiness = (data) => request({
    url: commonUrl + '/update',
    method: 'post',
    data
});

