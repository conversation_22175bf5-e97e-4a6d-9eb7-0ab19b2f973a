import request from '@/utils/request'

// 查询巷道子列表
export function listTunnel(query) {
  return request({
    url: '/operationalplan/tunnel/list',
    method: 'get',
    params: query
  })
}

// 查询巷道子详细
export function getTunnel(id) {
  return request({
    url: '/operationalplan/tunnel/' + id,
    method: 'get'
  })
}

// 新增巷道子
export function addTunnel(data) {
  return request({
    url: '/operationalplan/tunnel',
    method: 'post',
    data: data
  })
}

// 修改巷道子
export function updateTunnel(data) {
  return request({
    url: '/operationalplan/tunnel',
    method: 'put',
    data: data
  })
}

// 删除巷道子
export function delTunnel(id) {
  return request({
    url: '/operationalplan/tunnel/' + id,
    method: 'delete'
  })
}

// 查询生产动态日报
export function getDailyList(tunnelId) {
  return request({
    url: '/operationalplan/tunnel/daily/' + tunnelId,
    method: 'get'
  })
}


// 查询接施工队下拉
export function getConstructionTeamOption() {
  return request({
    url: '/operationalplan/option/getConstructionTeamOption',
    method: 'get'
  })
}


// 查询采区下拉
export function getPanelOption() {
  return request({
    url: '/operationalplan/option/getPanelOption',
    method: 'get'
  })
}


// 查询工作面下拉
export function getWorkFaceList() {
  return request({
    url: '/operationalplan/option/getWorkFaceList',
    method: 'get'
  })
}
