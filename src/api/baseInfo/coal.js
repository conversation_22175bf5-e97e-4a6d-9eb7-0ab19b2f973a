import request from '@/utils/request'

// 查询煤种列表
export function listCoal(query) {
  return request({
    url: '/operationalplan/coal/list',
    method: 'get',
    params: query
  })
}

// 查询煤种详细
export function getCoal(id) {
  return request({
    url: '/operationalplan/coal/' + id,
    method: 'get'
  })
}

// 新增煤种
export function addCoal(data) {
  return request({
    url: '/operationalplan/coal',
    method: 'post',
    data: data
  })
}

// 修改煤种
export function updateCoal(data) {
  return request({
    url: '/operationalplan/coal',
    method: 'put',
    data: data
  })
}

// 删除煤种
export function delCoal(id) {
  return request({
    url: '/operationalplan/coal/' + id,
    method: 'delete'
  })
}

// 修改状态
export function updateStartStop(id,state) {
  return request({
    url: '/operationalplan/coal/updateStartStop/'+ id +'/' + state,
    method: 'get'
  })
}
