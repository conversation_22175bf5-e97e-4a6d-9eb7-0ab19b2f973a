import request from '@/utils/request'

// 查询班组信息列表
export function listTeams(query) {
  return request({
    url: '/operationalplan/teams/list',
    method: 'get',
    params: query
  })
}

// 查询班组信息详细
export function getTeams(id) {
  return request({
    url: '/operationalplan/teams/' + id,
    method: 'get'
  })
}

// 新增班组信息
export function addTeams(data) {
  return request({
    url: '/operationalplan/teams',
    method: 'post',
    data: data
  })
}

// 修改班组信息
export function updateTeams(data) {
  return request({
    url: '/operationalplan/teams',
    method: 'put',
    data: data
  })
}

// 删除班组信息
export function delTeams(id) {
  return request({
    url: '/operationalplan/teams/' + id,
    method: 'delete'
  })
}


// 修改状态
export function updateStartStop(id,state) {
  return request({
    url: '/operationalplan/teams/updateStartStop/'+ id +'/' + state,
    method: 'get'
  })
}

// 查询区队下拉
export function getTeamsOption() {
  return request({
    url: '/operationalplan/option/getTeamsOption',
    method: 'get'
  })
}


// 查询区队人员下拉
export function getUserByTeamList(deptId) {
  return request({
    url: '/operationalplan/option/getUserByTeamList/' + deptId,
    method: 'get'
  })
}
