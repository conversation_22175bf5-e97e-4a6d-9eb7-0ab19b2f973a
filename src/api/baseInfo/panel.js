import request from '@/utils/request'

// 查询盘(采)区列表
export function listPanel(query) {
  return request({
    url: '/operationalplan/panel/list',
    method: 'get',
    params: query
  })
}

// 查询盘(采)区详细
export function getPanel(id) {
  return request({
    url: '/operationalplan/panel/' + id,
    method: 'get'
  })
}

// 新增盘(采)区
export function addPanel(data) {
  return request({
    url: '/operationalplan/panel',
    method: 'post',
    data: data
  })
}

// 修改盘(采)区
export function updatePanel(data) {
  return request({
    url: '/operationalplan/panel',
    method: 'put',
    data: data
  })
}

// 删除盘(采)区
export function delPanel(id) {
  return request({
    url: '/operationalplan/panel/' + id,
    method: 'delete'
  })
}

