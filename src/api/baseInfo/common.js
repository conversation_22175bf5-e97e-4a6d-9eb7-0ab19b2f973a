/*
 * @Author: <PERSON>@coalmineinfo.cn
 * @Date: 2022-08-25 09:53:49
 * @LastEditors: <PERSON> w<PERSON>@coalmineinfo.cn
 * @LastEditTime: 2022-08-27 17:35:53
 * @FilePath: \coalmine-ui\src\api\baseInfo\common.js
 * @Description: 表格通用获取数据方法 和 公共路径
 */
import request from '@/utils/request';

// 公共路径  
export const baseUrl = '/coalmine-system';

// 通用获取数据方法
export const getData = ({ url, method = 'post', params = undefined }) => {
    let key = method === 'get' ? 'params' : 'data';
    return request({
        url: baseUrl + url,
        method,
        [key]: params
    })
};

/**
 * @description: 表格下载方法
 * @author: <PERSON> w<PERSON>@coalmineinfo.cn
 * @param {*} url
 * @param {*} params
 * @param {*} filename
 * @param {*} headers
 * @return {*}
 */
export const downloadExcel = (url, data, filename, headers = { 'content-type': 'application/json;charset=UTF-8' }) =>
    request({
        url: baseUrl + url,
        method: 'post',
        data,
        headers,
        responseType: 'blob'
    }).then((data) => {
        const blob = new Blob([data], { type: 'application/x-msdownload' })
        if ('download' in document.createElement('a')) {
            const elink = document.createElement('a')
            elink.download = filename
            elink.style.display = 'none'
            elink.href = URL.createObjectURL(blob)
            document.body.appendChild(elink)
            elink.click()
            URL.revokeObjectURL(elink.href)
            document.body.removeChild(elink)
        } else {
            navigator.msSaveBlob(blob, filename)
        }
    }).catch((err) => {
        console.error(err)
    });

// 所属系统
export const getAlarmSystem = () => request({
    url: '/system/dict/data/type/alm_system',
    method: 'get'
});

// 业务类型
export const getEventType = () => request({
    url: '/alarm/alarmevent/selectEventTypeTree',
    method: 'get'
});

// 矿井名称
export const getMineName = () => request({
    url: '/system/dict/data/type/alm_organize',
    method: 'get'
});