import request from '@/utils/request'
import { praseStrEmpty } from "@/utils/coalmine";

// 查询重点工作列表
export function listWork(query) {
  return request({
    url: '/system/workSpeed/list',
    method: 'get',
    params: query
  })
}


// 查询指标类
export function getEchartsData(query) {
  return request({
    url: '/system/workSpeed/targetList',
    method: 'get',
    params: query
  })
}


// 查询任务看板列表
export function listTaskWork(query) {
  return request({
    url: '/system/workSpeed/listTaskWork',
    method: 'get',
    params: query
  })
}

// 查询任务看板统计
export function getTaskCount(query) {
  return request({
    url: '/system/workSpeed/getTaskCount',
    method: 'get',
    params: query
  })
}

// 查询任务看板统计
export function getTaskSystemType(query) {
  return request({
    url: '/system/workSpeed/getTaskSystem',
    method: 'get',
    params: query
  })
}
