export default class OnlineColumnController {
  static list (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/list', 'post', params, axiosOption, httpOption);
  }

  static view (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/view', 'get', params, axiosOption, httpOption);
  }

  static export (sender, params, fileName) {
    return sender.download('/flow/onlineColumn/export', params, fileName);
  }

  static add (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/add', 'post', params, axiosOption, httpOption);
  }

  static update (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/update', 'post', params, axiosOption, httpOption);
  }

  static refreshColumn (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/refresh', 'post', params, axiosOption, httpOption);
  }

  static delete (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/delete', 'post', params, axiosOption, httpOption);
  }

  static listOnlineColumnRule (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/listOnlineColumnRule', 'post', params, axiosOption, httpOption);
  }

  static listNotInOnlineColumnRule (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/listNotInOnlineColumnRule', 'post', params, axiosOption, httpOption);
  }

  static addOnlineColumnRule (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/addOnlineColumnRule', 'post', params, axiosOption, httpOption);
  }

  static deleteOnlineColumnRule (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/deleteOnlineColumnRule', 'post', params, axiosOption, httpOption);
  }

  static updateOnlineColumnRule (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/updateOnlineColumnRule', 'post', params, axiosOption, httpOption);
  }

  static viewOnlineColumnRule (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineColumn/viewOnlineColumnRule', 'get', params, axiosOption, httpOption);
  }
}
