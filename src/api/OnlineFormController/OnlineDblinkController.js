export default class OnlineDblinkController {
  static list (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDblink/list', 'post', params, axiosOption, httpOption);
  }

  static listDblinkTables (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDblink/listDblinkTables', 'get', params, axiosOption, httpOption);
  }

  static listDblinkTableColumns (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDblink/listDblinkTableColumns', 'get', params, axiosOption, httpOption);
  }
}
