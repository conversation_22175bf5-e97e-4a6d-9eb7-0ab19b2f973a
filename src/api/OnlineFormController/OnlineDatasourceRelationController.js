export default class OnlineDatasourceRelationController {
  static list (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDatasourceRelation/list', 'post', params, axiosOption, httpOption);
  }

  static view (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDatasourceRelation/view', 'get', params, axiosOption, httpOption);
  }

  static export (sender, params, fileName) {
    return sender.download('/flow/onlineDatasourceRelation/export', params, fileName);
  }

  static add (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDatasourceRelation/add', 'post', params, axiosOption, httpOption);
  }

  static update (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDatasourceRelation/update', 'post', params, axiosOption, httpOption);
  }

  static delete (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDatasourceRelation/delete', 'post', params, axiosOption, httpOption);
  }
}
