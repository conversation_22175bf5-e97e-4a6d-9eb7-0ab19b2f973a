export default class OnlineDatasourceController {
  static list (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDatasource/list', 'post', params, axiosOption, httpOption);
  }

  static view (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDatasource/view', 'get', params, axiosOption, httpOption);
  }

  static export (sender, params, fileName) {
    return sender.download('/flow/onlineDatasource/export', params, fileName);
  }

  static add (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDatasource/add', 'post', params, axiosOption, httpOption);
  }

  static update (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDatasource/update', 'post', params, axiosOption, httpOption);
  }

  static delete (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineDatasource/delete', 'post', params, axiosOption, httpOption);
  }
}
