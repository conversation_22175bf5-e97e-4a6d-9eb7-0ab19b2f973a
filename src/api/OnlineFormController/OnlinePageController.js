export default class OnlinePageController {
  static list (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/list', 'post', params, axiosOption, httpOption);
  }

  static listAllPageAndForm (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/listAllPageAndForm', 'post', params, axiosOption, httpOption);
  }

  static view (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/view', 'get', params, axiosOption, httpOption);
  }

  static export (sender, params, fileName) {
    return sender.download('/flow/onlinePage/export', params, fileName);
  }

  static add (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/add', 'post', params, axiosOption, httpOption);
  }

  static update (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/update', 'post', params, axiosOption, httpOption);
  }

  static updatePublished (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/updatePublished', 'post', params, axiosOption, httpOption);
  }

  static delete (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/delete', 'post', params, axiosOption, httpOption);
  }

  static updateStatus (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/updateStatus', 'post', params, axiosOption, httpOption);
  }

  static listOnlinePageDatasource (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/listOnlinePageDatasource', 'post', params, axiosOption, httpOption);
  }

  static listNotInOnlinePageDatasource (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/listNotInOnlinePageDatasource', 'post', params, axiosOption, httpOption);
  }

  static addOnlinePageDatasource (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/addOnlinePageDatasource', 'post', params, axiosOption, httpOption);
  }

  static deleteOnlinePageDatasource (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/deleteOnlinePageDatasource', 'post', params, axiosOption, httpOption);
  }

  static updateOnlinePageDatasource (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/updateOnlinePageDatasource', 'post', params, axiosOption, httpOption);
  }

  static viewOnlinePageDatasource (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlinePage/viewOnlinePageDatasource', 'get', params, axiosOption, httpOption);
  }
}
