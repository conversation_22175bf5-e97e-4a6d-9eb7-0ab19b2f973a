export default class OnlineFormController {
  static list (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineForm/list', 'post', params, axiosOption, httpOption);
  }

  static view (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineForm/view', 'get', params, axiosOption, httpOption);
  }

  static render (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineForm/render', 'get', params, axiosOption, httpOption);
  }

  static export (sender, params, fileName) {
    return sender.download('/flow/onlineForm/export', params, fileName);
  }

  static add (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineForm/add', 'post', params, axiosOption, httpOption);
  }

  static update (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineForm/update', 'post', params, axiosOption, httpOption);
  }

  static delete (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineForm/delete', 'post', params, axiosOption, httpOption);
  }
}
