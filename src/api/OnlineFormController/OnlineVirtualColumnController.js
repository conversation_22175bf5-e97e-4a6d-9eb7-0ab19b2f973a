export default class OnlineVirtualColumnController {
  static list (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineVirtualColumn/list', 'post', params, axiosOption, httpOption);
  }

  static view (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineVirtualColumn/view', 'get', params, axiosOption, httpOption);
  }

  static add (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineVirtualColumn/add', 'post', params, axiosOption, httpOption);
  }

  static update (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineVirtualColumn/update', 'post', params, axiosOption, httpOption);
  }

  static delete (sender, params, axiosOption, httpOption) {
    return sender.doUrl('/flow/onlineVirtualColumn/delete', 'post', params, axiosOption, httpOption);
  }
}
