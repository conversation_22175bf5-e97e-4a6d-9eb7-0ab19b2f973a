/*
 * @Author: <PERSON>@coalmineinfo.cn
 * @Date: 2022-11-22 15:30:55
 * @LastEditors: <PERSON> w<PERSON>@coalmineinfo.cn
 * @LastEditTime: 2022-11-22 20:25:50
 * @FilePath: \coalmine-ui\src\api\dfsAutoDrainage\linkage.js
 * @Description: 自动化排水联动控制 / 联动设定
 */

import request from '@/utils/axios'
const baseUrl = '/things/services/setting'

/* 主表 */

// 条件查询下拉框
export const getPullDown = () => request({
    url: baseUrl + '/pullDown'
})

//列表数据
export const getList = (params) => request({
    url: baseUrl + '/list',
    params
})

// 新增
export const addData = (data) => request({
    url: baseUrl,
    method: 'post',
    data
})

// 编辑
export const updateData = (data) => request({
    url: baseUrl,
    method: 'put',
    data
})

// 获取编辑数据
export const getColumnData = (params) => request({
    url: baseUrl + '/' + params,
});
// 删除
export const deleteData = (data) => request({
    url: baseUrl + '/' + data,
    method: 'delete',
});

/* 子表 */

// 获取联动设定数据
export const getLinkageSetting = (params) => request({
    url: baseUrl + '/linkageSetting',
    params
});

// 新增 /  更新联动设定
export const updateLinageSetting = (data) => request({
    url: '/things/services/item/linkageSetting',
    method: 'post',
    data
})