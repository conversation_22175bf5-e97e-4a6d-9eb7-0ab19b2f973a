import request from '@/utils/request'

// 查询订单测试列表
export function listTempOrder(query) {
  return request({
    url: '/template/tempOrder/list',
    method: 'get',
    params: query
  })
}

// 查询订单测试详细
export function getTempOrder(id) {
  return request({
    url: '/template/tempOrder/' + id,
    method: 'get'
  })
}

// 新增订单测试
export function addTempOrder(data) {
  debugger
  return request({
    url: '/template/tempOrder',
    method: 'post',
    data: data
  })
}

// 修改订单测试
export function updateTempOrder(data) {
  return request({
    url: '/template/tempOrder',
    method: 'put',
    data: data
  })
}

// 删除订单测试
export function delTempOrder(id) {
  return request({
    url: '/template/tempOrder/' + id,
    method: 'delete'
  })
}
