import request from '@/utils/request'

// 查询模板服务列表
export function listTempService(query) {
  return request({
    url: '/template/tempService/list',
    method: 'get',
    params: query
  })
}

// 查询模板服务详细
export function getTempService(serviceId) {
  return request({
    url: '/template/tempService/' + serviceId,
    method: 'get'
  })
}

// 新增模板服务
export function addTempService(data) {
  return request({
    url: '/template/tempService',
    method: 'post',
    data: data
  })
}

// 修改模板服务
export function updateTempService(data) {
  return request({
    url: '/template/tempService',
    method: 'put',
    data: data
  })
}

// 删除模板服务
export function delTempService(serviceId) {
  return request({
    url: '/template/tempService/' + serviceId,
    method: 'delete'
  })
}
