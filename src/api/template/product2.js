import request from '@/utils/request'

// 查询产品2列表
export function listProduct2(query) {
  return request({
    url: '/template/product2/list',
    method: 'get',
    params: query
  })
}

// 查询产品2详细
export function getProduct2(id) {
  return request({
    url: '/template/product2/' + id,
    method: 'get'
  })
}

// 新增产品2
export function addProduct2(data) {
  return request({
    url: '/template/product2',
    method: 'post',
    data: data
  })
}

// 修改产品2
export function updateProduct2(data) {
  return request({
    url: '/template/product2',
    method: 'put',
    data: data
  })
}

// 删除产品2
export function delProduct2(id) {
  return request({
    url: '/template/product2/' + id,
    method: 'delete'
  })
}
