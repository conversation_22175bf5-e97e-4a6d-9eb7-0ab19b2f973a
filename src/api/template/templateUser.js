import request from '@/utils/request'

// 查询用户列表
export function selectUserByTemplateId(query) {
  return request({
    url: '/assisted/template/selectUserByTemplateId',
    method: 'get',
    params: query
  })
}

// 查询用户ID列表
export function selectUserIdByTemplateId(query) {
  return request({
    url: '/assisted/template/selectUserIdByTemplateId',
    method: 'get',
    params: query
  })
}

// 新增用户
export function addTemplateUser(data) {
  return request({
    url: '/assisted/template/addTemplateUser',
    method: 'post',
    data: data
  })
}

// 删除用户
export function deleteTemplateUser(data) {
  return request({
    url: '/assisted/template/deleteTemplateUser',
    method: 'post',
    data: data
  })
}


// 批量删除用户
export function deleteTemplateUsers(data) {
  return request({
    url: '/assisted/template/deleteTemplateUsers',
    method: 'post',
    data: data
  })
}
