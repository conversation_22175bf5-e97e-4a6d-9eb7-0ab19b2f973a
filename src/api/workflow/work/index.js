import request from "@/utils/request";
// List
export function tabList(params) {
  return request({
    url: "/procedure/work_flow/list",
    method: "get",
    params: params,
  });
}
// 详情
export function workItem(id) {
  return request({
    url: "/procedure/work_flow/" + id,
    method: "get",
  });
}
// 新增
export function workAdd(params) {
  return request({
    url: "/procedure/work_flow/add",
    method: "post",
    data: params,
  });
}
// 保存
export function workSubmit(params) {
  return request({
    url: "/procedure/work_flow/update",
    method: "put",
    data: params,
  });
}
// 推送
export function addPush(params) {
  return request({
    url: "/procedure/work_flow/addAndEditPush",
    method: "put",
    data: params,
  });
}
// 推送记录
export function pushRecord(params) {
  return request({
    url: "/procedure/work_flow/pushRecord",
    method: "put",
    params: params,
  });
}
// 重新推送（树）
export function getTree(id) {
  return request({
    url: "/procedure/tree/workIdOrTechIdPostTree/" + id,
    method: "get",
  });
}
// 重新推送
export function rePush(params) {
  return request({
    url: "/procedure/work_flow/push",
    method: "put",
    params: params,
  });
}
// 附件上传
export function uploadFile(params) {
  return request({
    url: "/filecore/upload",
    method: "post",
    data: params,
  });
}
// 附件下载
export function downloadFile(params) {
  return request({
    url: "/filecore/downloadSourceFile",
    method: "get",
    params: params,
    responseType: "arraybuffer",
  });
}
// 附件删除
export function deleteFile(ids) {
  return request({
    url: "/filecore/recycleBin/clear/" + ids,
    method: "delete",
  });
}
// 查看附件
export function openFile(params) {
  return request({
    url: "/filecore/openImgFile",
    method: "get",
    params: params,
    responseType: "blob",
  });
}
// 岗位树
export function workTree(params) {
  return request({
    url: "/system/tree/postTree",
    method: "get",
    params: params,
  });
}
// 删除作业流程记录
export function deleteItem(id) {
  return request({
    url: "/procedure/work_flow/" + id,
    method: "delete",
  });
}
