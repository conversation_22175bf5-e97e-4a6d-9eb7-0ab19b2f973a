import request from "@/utils/request";
// List
export function tabList(params) {
  return request({
    url: "/procedure/tech_disclose/list",
    method: "get",
    params: params,
  });
}
// 新增
export function workAdd(params) {
  return request({
    url: "/procedure/tech_disclose/add",
    method: "post",
    data: params,
  });
}
// 详情
export function workItem(id) {
  return request({
    url: "/procedure/tech_disclose/" + id,
    method: "get",
  });
}
// 更新
export function workSubmit(params) {
  return request({
    url: "/procedure/tech_disclose/update",
    method: "put",
    data: params,
  });
}
// 删除
export function deleteItem(id) {
  return request({
    url: "/procedure/tech_disclose/" + id,
    method: "delete",
  });
}
// 岗位树
export function workTree(params) {
  return request({
    url: "/system/tree/postTree",
    method: "get",
    params: params,
  });
}
// 推送
export function addPush(params) {
  return request({
    url: "/procedure/tech_disclose/addAndEditPush",
    method: "put",
    data: params,
  });
}
// 重新推送（树）
export function getTree(id) {
  return request({
    url: "/procedure/tree/workIdOrTechIdPostTree/" + id,
    method: "get",
  });
}
// 重新推送
export function rePush(params) {
  return request({
    url: "/procedure/tech_disclose/push",
    method: "put",
    params: params,
  });
}
// 推送记录
export function pushRecord(params) {
  return request({
    url: "/procedure/tech_disclose/pushRecord",
    method: "put",
    params: params,
  });
}
// 附件上传
export function uploadFile(params) {
  return request({
    url: "/filecore/upload",
    method: "post",
    data: params,
  });
}
// 附件下载
export function downloadFile(params) {
  return request({
    url: "/filecore/downloadSourceFile",
    method: "get",
    params: params,
    responseType: "arraybuffer",
  });
}
// 附件删除
export function deleteFile(ids) {
  return request({
    url: "/filecore/recycleBin/clear/" + ids,
    method: "delete",
  });
}
