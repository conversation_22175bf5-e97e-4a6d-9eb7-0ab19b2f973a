import request from '@/utils/request'

// 查询 综合管理列表
export function listBusiness(query) {
  return request({
    url: '/system/business/list',
    method: 'get',
    params: query
  })
}

// 查询 综合管理详细
export function getBusiness(id) {
  return request({
    url: '/system/business/' + id,
    method: 'get'
  })
}

// 新增 综合管理
export function addBusiness(data) {
  return request({
    url: '/system/business',
    method: 'post',
    data: data
  })
}

// 修改 综合管理
export function updateBusiness(data) {
  return request({
    url: '/system/business',
    method: 'put',
    data: data
  })
}

// 删除 综合管理
export function delBusiness(id) {
  return request({
    url: '/system/business/' + id,
    method: 'delete'
  })
}

// 查询 业务类型
export function getEventType() {
  return request({
    url: '/alarm/alarmevent/selectEventTypeTree',
    method: 'get'
  })
}
// 查询 业务树
export function selectBusinessTree() {
  return request({
    url: '/system/business/selectBusinessTree',
    method: 'get'
  })
}


// 查询 系统
export function getSystem() {
  return request({
    url: '/system/dict/data/type/alm_system',
    method: 'get'
  })
}

