import request from '@/utils/request'

// 查询矿井区域管理列表
export function listArea(query) {
  return request({
    url: '/system/area/list',
    method: 'get',
    params: query
  })
}

// 查询矿井区域管理详细
export function getArea(id) {
  return request({
    url: '/system/area/' + id,
    method: 'get'
  })
}

// 新增矿井区域管理
export function addArea(data) {
  return request({
    url: '/system/area',
    method: 'post',
    data: data
  })
}

// 修改矿井区域管理
export function updateArea(data) {
  return request({
    url: '/system/area',
    method: 'put',
    data: data
  })
}

// 删除矿井区域管理
export function delArea(id) {
  return request({
    url: '/system/area/' + id,
    method: 'delete'
  })
}
