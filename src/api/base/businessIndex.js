import request from '@/utils/request'

// 查询业务指标列表
export function listIndex(query) {
  return request({
    url: '/system/businessIndex/list',
    method: 'get',
    params: query
  })
}

// 查询业务指标详细
export function getIndex(id) {
  return request({
    url: '/system/businessIndex/' + id,
    method: 'get'
  })
}

// 新增业务指标
export function addIndex(data) {
  return request({
    url: '/system/businessIndex',
    method: 'post',
    data: data
  })
}

// 修改业务指标
export function updateIndex(data) {
  return request({
    url: '/system/businessIndex',
    method: 'put',
    data: data
  })
}

// 删除业务指标
export function delIndex(id) {
  return request({
    url: '/system/businessIndex/' + id,
    method: 'delete'
  })
}

// 查询系统业务指标列表
export function getSysBusinessIndexList(query) {
  return request({
    url: '/system/sysBusinessIndex/list',
    method: 'get',
    params: query
  })
}

// 查询系统业务指标类型
export function getSysBusinessIndexTypeList() {
  return request({
    url: '/system/sysBusinessIndex/getSysBusinessIndexTypeList',
    method: 'get'
  })
}

// 新增业务指标
export function saveIndex(data) {
  return request({
    url: '/system/businessIndex/saveIndex',
    method: 'post',
    data: data
  })
}

// 刷新系统业务指标类型
export function flushData() {
  return request({
    url: '/system/sysBusinessIndex/flushData',
    method: 'get'
  })
}
// 查询设备属性列表
export function getBusinessIndexByIds(businessIds) {
  return request({
    url: '/system/businessIndex/getBusinessIndexByIds/' + businessIds,
    method: 'get'
  })
}
