import request from '@/utils/request'

//计算平方
export function squareCalculate(){
  return request({
    url:'alarm/algorithm/square/',
    method:'get',
  })
}

//计算平方
export function cubeCalculate(){
  return request({
    url:'alarm/algorithm/cube/',
    method:'get',
  });
}

//计算平方根
export function squareRootCalculate(){
  return request({
    url:'alarm/algorithm/squareRoot/',
    method:'get',
  });
}

//计算立方根
export function cubeRootCalculate(){
  return request({
    url:'alarm/algorithm/cubeRoot/',
    method:'get',
  });
}

//计算模运算
export function moduloCalculate(){
  return request({
    url:'alarm/algorithm/modulo/',
    method:'get',
  });
}

//字符串转换成大写
export function convertUpper(){
  return request({
    url:'alarm/algorithm/convertUpper/',
    method:'get',
  });
}

//字符串转换成小写
export function convertDown(){
  return request({
    url:'alarm/algorithm/convertDown/',
    method:'get',
  });
}

//获取字符串长度
export function getLength(){
  return request({
    url:'alarm/algorithm/getLength/',
    method:'get',
  });
}


//校验式子
export function checkFormula(data){
  return request({
    url:'alarm/algorithm/checkFormula',
    method:'post',
    data:data,
  });
}

//修改字符串
export function modifyString(data){
  return request({
    url:'alarm/algorithm/modifyString',
    method:'post',
    data:data,
  });
}
