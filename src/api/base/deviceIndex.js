import request from '@/utils/request'

// 查询设备树
export function treeEquipmentSelect() {
  return request({
    url: '/system/equipment/treeEquipmentSelect',
    method: 'get'
  })
}

// 查询设备指标列表
export function listDeviceIndex(query) {
  return request({
    url: '/system/deviceIndex/list',
    method: 'get',
    params: query
  })
}
// 查询设备属性列表
export function getPropertyList(query) {
  return request({
    url: '/system/property/getPropertyList',
    method: 'get',
    params: query
  })
}
// 保存设备指标配置
export function addDeviceIndex(data) {
  return request({
    url: '/system/deviceIndex/addDeviceIndex',
    method: 'post',
    data: data
  })
}

// 删除设备指标
export function delDeviceIndex(ids) {
  return request({
    url: '/system/deviceIndex/' + ids,
    method: 'delete'
  })
}

// 修改设备指标配置
export function updateDeviceIndex(data) {
  return request({
    url: '/system/deviceIndex',
    method: 'put',
    data: data
  })
}

// 查询设备属性列表
export function getPropertyByDeviceIds(ids) {
  return request({
    url: '/system/deviceIndex/selectByIds/' + ids,
    method: 'get'
  })
}


