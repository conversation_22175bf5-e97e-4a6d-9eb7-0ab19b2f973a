import request from '@/utils/request'

// 查询设备列表
export function listBaseColumn(query) {
  return request({
    url: '/supervise/baseColumn/list',
    method: 'get',
    params: query
  })
}

// 查询设备所有列表
export function listAllBaseColumn(query) {
  return request({
    url: '/supervise/baseColumn/listAll',
    method: 'get',
    params: query
  })
}

// 查询设备详细
export function getBaseColumn(id) {
  return request({
    url: '/supervise/baseColumn/getInfo/' + id,
    method: 'get'
  })
}

// 新增设备
export function addBaseColumn(data) {
  return request({
    url: '/supervise/baseColumn/add',
    method: 'post',
    data: data
  })
}

// 修改设备
export function updateBaseColumn(data) {
  return request({
    url: '/supervise/baseColumn/edit',
    method: 'put',
    data: data
  })
}

// 删除设备
export function delBaseColumn(id) {
  return request({
    url: '/supervise/baseColumn/remove/' + id,
    method: 'delete'
  })
}

//复制
export function copyBaseColumn(id) {
  return request({
    url: '/supervise/baseColumn/copy/' + id,
    method: 'get'
  })
}
