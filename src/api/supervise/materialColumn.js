import request from '@/utils/request'

// 查询管柱列表
export function listMaterialColumn(query) {
  return request({
    url: '/supervise/materialColumn/list',
    method: 'get',
    params: query
  })
}

// 查询管柱所有列表
export function listAllMaterialColumn(query) {
  return request({
    url: '/supervise/materialColumn/listAll',
    method: 'get',
    params: query
  })
}

// 查询管柱详细
export function getMaterialColumn(id) {
  return request({
    url: '/supervise/materialColumn/getInfo/' + id,
    method: 'get'
  })
}

// 新增管柱
export function addMaterialColumn(data) {
  return request({
    url: '/supervise/materialColumn/add',
    method: 'post',
    data: data
  })
}

// 修改管柱
export function updateMaterialColumn(data) {
  return request({
    url: '/supervise/materialColumn/edit',
    method: 'put',
    data: data
  })
}

// 删除管柱
export function delMaterialColumn(id) {
  return request({
    url: '/supervise/materialColumn/remove/' + id,
    method: 'delete'
  })
}

//复制
export function copyMaterialColumn(id) {
  return request({
    url: '/supervise/materialColumn/copy/' + id,
    method: 'get'
  })
}
