import request from '@/utils/request'

// 查询井基础列表
export function listWellInfo(query) {
  return request({
    url: '/supervise/wellInfo/list',
    method: 'get',
    params: query
  })
}

// 查询井基础所有列表
export function listAllWellInfo(query) {
  return request({
    url: '/supervise/wellInfo/listAll',
    method: 'get',
    params: query
  })
}

// 查询井基础详细
export function getWellInfo(id) {
  return request({
    url: '/supervise/wellInfo/getInfo/' + id,
    method: 'get'
  })
}

// 新增井基础
export function addWellInfo(data) {
  return request({
    url: '/supervise/wellInfo/add',
    method: 'post',
    data: data
  })
}

// 修改井基础
export function updateWellInfo(data) {
  return request({
    url: '/supervise/wellInfo/edit',
    method: 'put',
    data: data
  })
}

// 删除井基础
export function delWellInfo(id) {
  return request({
    url: '/supervise/wellInfo/remove/' + id,
    method: 'delete'
  })
}

// 查询井别下拉
export function getWellClassOptions() {
  return request({
    url: '/supervise/wellInfo/getWellClassOptions',
    method: 'get'
  })
}

// 查询井型下拉
export function getWellTypeOptions() {
  return request({
    url: '/supervise/wellInfo/getWellTypeOptions',
    method: 'get'
  })
}
