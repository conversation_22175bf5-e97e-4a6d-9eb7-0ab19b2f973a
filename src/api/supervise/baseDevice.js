import request from '@/utils/request'

// 查询设备列表
export function listBaseDevice(query) {
  return request({
    url: '/supervise/baseDevice/list',
    method: 'get',
    params: query
  })
}

// 查询设备所有列表
export function listAllBaseDevice(query) {
  return request({
    url: '/supervise/baseDevice/listAll',
    method: 'get',
    params: query
  })
}

// 查询设备详细
export function getBaseDevice(id) {
  return request({
    url: '/supervise/baseDevice/getInfo/' + id,
    method: 'get'
  })
}

// 新增设备
export function addBaseDevice(data) {
  return request({
    url: '/supervise/baseDevice/add',
    method: 'post',
    data: data
  })
}

// 修改设备
export function updateBaseDevice(data) {
  return request({
    url: '/supervise/baseDevice/edit',
    method: 'put',
    data: data
  })
}

// 删除设备
export function delBaseDevice(id) {
  return request({
    url: '/supervise/baseDevice/remove/' + id,
    method: 'delete'
  })
}

//复制
export function copyBaseDevice(id) {
  return request({
    url: '/supervise/baseDevice/copy/' + id,
    method: 'get'
  })
}
