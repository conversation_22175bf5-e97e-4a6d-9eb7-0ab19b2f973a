import request from '@/utils/request'

// 1.基本信息：
export function getBaseInfo() {
  return request({
    url: '/supervise/homePage/getBaseInfo',
    method: 'get',
  })
}

// 查询作业完成情况：
export function getTaskCompletedInfo() {
  return request({
    url: '/supervise/homePage/getTaskCompletedInfo',
    method: 'get',
  })
}

// 查询各类作业完成情况
export function getTaskCompletedInfoByType() {
  return request({
    url: '/supervise/homePage/getTaskCompletedInfoByType',
    method: 'get'
  })
}
// 查询各类作业统计：
export function getTaskInfo() {
    return request({
      url: '/supervise/homePage/getTaskInfo',
      method: 'get'
    })
  }
// .查询各类作业数占比：
export function getTaskRate() {
    return request({
      url: '/supervise/homePage/getTaskRate',
      method: 'get'
    })
  }
// .查询各类作业数占比：
export function getTaskHistoryByType(params) {
    return request({
      url: '/supervise/homePage/getTaskHistoryByType',
      method: 'get',
      params
    })
  }

// 所有油田点位信息：
export function getOilfieldInfoListAll(params) {
  return request({
    url: '/supervise/homePage/getOilfieldInfoList',
    method: 'get',
    params
  })
}
  