import request from '@/utils/request'

// 查询油田列表
export function listOilfieldInfo(query) {
  return request({
    url: '/supervise/oilfieldInfo/list',
    method: 'get',
    params: query
  })
}

// 查询油田列表
export function listOilfieldInfoAll(query) {
  return request({
    url: '/supervise/oilfieldInfo/listAll',
    method: 'get',
    params: query
  })
}

// 查询油田详细
export function getOilfieldInfo(id) {
  return request({
    url: '/supervise/oilfieldInfo/getInfo/' + id,
    method: 'get'
  })
}

// 新增油田
export function addOilfieldInfo(data) {
  return request({
    url: '/supervise/oilfieldInfo/add',
    method: 'post',
    data: data
  })
}

// 修改油田
export function updateOilfieldInfo(data) {
  return request({
    url: '/supervise/oilfieldInfo/edit',
    method: 'put',
    data: data
  })
}

// 删除油田
export function delOilfieldInfo(id) {
  return request({
    url: '/supervise/oilfieldInfo/remove/' + id,
    method: 'delete'
  })
}
