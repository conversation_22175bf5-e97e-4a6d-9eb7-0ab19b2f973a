import request from '@/utils/request'

// 查询采油厂列表
export function listOilfactory(query) {
  return request({
    url: '/supervise/oilfactory/list',
    method: 'get',
    params: query
  })
}

// 查询采油厂详细
export function getOilfactory(id) {
  return request({
    url: '/supervise/oilfactory/getInfo/' + id,
    method: 'get'
  })
}

// 新增采油厂
export function addOilfactory(data) {
  return request({
    url: '/supervise/oilfactory/add',
    method: 'post',
    data: data
  })
}

// 修改采油厂
export function updateOilfactory(data) {
  return request({
    url: '/supervise/oilfactory/edit',
    method: 'put',
    data: data
  })
}

// 删除采油厂
export function delOilfactory(id) {
  return request({
    url: '/supervise/oilfactory/remove/' + id,
    method: 'delete'
  })
}
