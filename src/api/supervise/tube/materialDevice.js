import request from '@/utils/request'

// 查询设备列表
export function listMaterialDevice(query) {
  return request({
    url: '/supervise/materialDevice/list',
    method: 'get',
    params: query
  })
}

// 查询设备所有列表
export function listAllMaterialDevice(query) {
  return request({
    url: '/supervise/materialDevice/listAll',
    method: 'get',
    params: query
  })
}

// 查询设备详细
export function getMaterialDevice(id) {
  return request({
    url: '/supervise/materialDevice/getInfo/' + id,
    method: 'get'
  })
}

// 新增设备
export function addMaterialDevice(data) {
  return request({
    url: '/supervise/materialDevice/add',
    method: 'post',
    data: data
  })
}

// 修改设备
export function updateMaterialDevice(data) {
  return request({
    url: '/supervise/materialDevice/edit',
    method: 'put',
    data: data
  })
}

// 删除设备
export function delMaterialDevice(id) {
  return request({
    url: '/supervise/materialDevice/remove/' + id,
    method: 'delete'
  })
}

//复制
export function copyMaterialDevice(id) {
  return request({
    url: '/supervise/materialDevice/copy/' + id,
    method: 'get'
  })
}
