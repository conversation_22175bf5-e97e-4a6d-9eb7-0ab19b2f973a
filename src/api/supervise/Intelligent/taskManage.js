import request from "@/utils/request";

const baseUrl = "/supervise/oilSuperviseTask";



export const taskManageAPI = {
  //获取井树
  getWellTreeAPI: async (params) => {
    return request({
      url: `${baseUrl}/getWellTree`,
      method: "get",
      params,
    });
  },
  //获取右边list分页查询
  getListAPI: async (params) => {
    return request({
      url: `${baseUrl}/list`,
      method: "get",
      params,
    });
  },

  //创建监督任务(第一步，获取自动生成编号)
  getJobTaskIdAPI: async () => {
    return request({
      url: `${baseUrl}/getJobTaskId`,
      method: "get",
    });
  },
  //创建监督任务(第一步，保存数据)
  getTaskAddAPI: async (data) => {
    return request({
      url: `${baseUrl}/add`,
      method: "post",
      data,
    });
  },
  // 作业任务新增编辑
  getTaskEditAPI: async (data) => {
    return request({
      url: `${baseUrl}/edit`,
      method: "post",
      data,
    });
  },
  // 作业任务删除
  getTaskDeleteAPI: async (id) => {
    return request({
      url: `${baseUrl}/remove/${id}`,
      method: "get",
    });
  },
  // 上传文件
  getUploadFileAPI: async (data) => {
    return request({
      url: `filecore/uploads`,
      method: "post",
      data,
    });
  },
  // 获取任务详情
  getTaskDetailsAPI: async (id) => {
    return request({
      url: `${baseUrl}/getInfo/${id}`,
      method: "get",
    });
  },
  // 获取KKfile文件地址
  getKKFileUrlAPI: async (params) => {
    return request({
      url: `filecore/getFileURL`,
      method: "get",
      params,
    });
  },
  // 获取信息配置列表
  getConfigInfoAPI: async (params) => {
    return request({
      url: `/supervise/oilConfigExtContent/listAll`,
      method: "get",
      params,
    });
  },

  // 获取新增提取信息
  getAddConfigInfoAPI: async (data) => {
    return request({
      url: `/supervise/oilSuperviseTaskExt/add`,
      method: "post",
      data,
    });
  },
  // 获取修改提取信息接口---通过字表id查询
  // getEditConfigInfoAPI: async (data) => {
  //   return request({
  //     url: `supervise/oilSuperviseTaskExt/edit`,
  //     method: "post",
  //     data,
  //   });
  // },
  // 获取修改提取信息接口---通过主表id查询
  getEditConfigInfoAPI: async (data) => {
    return request({
      url: `supervise/oilSuperviseTaskExt/editByTaskId`,
      method: "post",
      data,
    });
  },
  // 获取删除提取信息接口
  getDeleteConfigInfoAPI: async (ids) => {
    return request({
      url: `supervise/oilSuperviseTask/remove/${ids}`,
      method: "get",
    });
  },
  // 根据taskid提取信息列表
  getTaskExtListAPI: async (params) => {
    return request({
      // url: `${baseUrl}/getInfoByTaskId/${id}`,
      url: `supervise/oilSuperviseTaskExt/getInfoByTaskId`,
      method: "get",
      params,
    });
  },
  // 抽取pdf内容接口
  getPdfContentAPI: async (data) => {
    return request({
      // url: `techmanage/chat/getPdfContent`,
      url: `techDocManage/chat/getPdfContent`,
      method: "post",
      data,
    });
  },
  // 根据配置id获取json
  getConfigIDJsonAPI: async (id) => {
    return request({
      url: `/supervise/oilSuperviseTaskExt/getPdfContentById/${id}`,
      method: "get",
    });
  },
  // 根据配置id获取井眼轨迹json
  getConfigIDJsonTrackAPI: async (id) => {
    return request({
      url: `/supervise/oilSuperviseTaskExt/getPdfTableById/${id}`,
      method: "get",
    });
  },
  // 提交信息配置数据
  submitConfigDataAPI: async (data) => {
    return request({
      url: `supervise/oilSuperviseTaskExt/addTaskExtData`,
      method: "post",
      data,
    });
  },
  // 原文件下载
  getOriginalFileAPI: async (params) => {
    return request({
          url: "/filecore/downloadSourceFile",
          params: params,
          method: "get",
          responseType: "blob",
        });
  },
  // 获取版列表
  getVersionLogAPI: async (params) => {
    return request({
      // url: "filecore/docLog/list",
      url: "filecore/docVersion/list",
      method: "get",
      params,
    });
  },
  // 删除版本
  getDeleteVersionAPI: async (id) => {
    return request({
      url: `filecore/docVersion/${id}`,
      method: "delete",
    });
  },
  // 删除计划书管理
  getDeletePlanAPI: async (id) => {
    return request({
      url: '/assisted/repRecord/' + id,
      method: 'delete',
    });
  },
  // 通过井号查询是否存在
  getFileByNumberAPI: async (str) => {
    return request({
      url: `supervise/wellInfo/getInfoByWellNo/${str}`,
      method: "get",
    });
  },
  // 生成计划书
  createPlanFileAPI: async (data) => {
    return request({
      url: "supervise/oilSuperviseTask/createPlanFile",
      method: "post",
      data,
    });
  },
  // 总结报告管
  createSummaryFileAPI: async (data) => {
    return request({
      url: "supervise/oilSuperviseTask/createSummaryFile",
      method: "post",
      data,
    });
  },
  // 获取计划书列表
  getPlanFileListAPI: async (data) => {
    return request({
      url: "assisted/repRecord/listPlanTask",
      method: "post",
      data,
    });
  },
};
