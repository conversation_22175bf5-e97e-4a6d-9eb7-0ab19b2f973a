import request from "@/utils/request";




export const logManageAPI = {
  //=======================================井下监督日志：
        // 获取井下日志信息列表
        getDownholeLogListAPI: async (params) => {
            return request({
            url: `supervise/downholeLog/list`,
            method: "get",
            params
            });
        },
        // 上传文件后批次新增数据
        getDownholeLogAddBatchAPI: async (params) => {
            return request({
            url: `supervise/downholeLog/addBatchByFile`,
            method: "get",
            params,
            });
        },
        // 批次新增数据
        getDownholeLogCustomAddAPI: async (data) => {
            return request({
            url: `supervise/downholeLog/addBatch`,
            method: "post",
            data,
            });
        },
        //   创建井下监督日志-批次更新数据
        getDownholeLogAddAPI: async (data) => {
            return request({
            url: `supervise/downholeLog/updateBatch`,
            method: "post",
            data,
            });
        },
        //   获取日志及子表详细信息
        getDownholeLogDetailsAPI: async (id) => {
            return request({
            url: `supervise/downholeLog/getAllInfo/${id}`,
            method: "get",
            });
        },
        // 删除list
        getDownholeLogDeleteAPI: async (ids) => {
            return request({
            url: `supervise/downholeLog/remove/${ids}`,
            method: "delete",
            });
        },
        // 批量复制数据
        getDownholeLogCopyAPI: async (ids) => {
            return request({
            url: `supervise/downholeLog/copyBatch/${ids}`,
            method: "get",
            });
        },
  //=======================================钻井监督日志：
        // 获取日志信息列表
        getSuperviseDrillingLogListAPI: async (params) => {
            return request({
            url: `supervise/superviseDrillingLog/list`,
            method: "get",
            params
            });
        },
        // 上传文件后批次新增数据
        getSuperviseDrillingLogAddBatchAPI: async (params) => {
            return request({
            url: `supervise/superviseDrillingLog/addBatchByFile`,
            method: "get",
            params,
            });
        },
        // 批次新增数据
        getSuperviseDrillingLogCustomAddAPI: async (data) => {
            return request({
            url: `supervise/superviseDrillingLog/addBatch`,
            method: "post",
            data,
            });
        },
        //   创建钻井监督日志-批次更新数据
        getSuperviseDrillingLogAddAPI: async (data) => {
            return request({
            url: `supervise/superviseDrillingLog/updateBatch`,
            method: "post",
            data,
            });
        },
        //   获取日志及子表详细信息
        getSuperviseDrillingLogDetailsAPI: async (id) => {
            return request({
            url: `supervise/superviseDrillingLog/getAllInfo/${id}`,
            method: "get",
            });
        },
        // 删除list
        getSuperviseDrillingLogDeleteAPI: async (ids) => {
            return request({
            url: `supervise/superviseDrillingLog/remove/${ids}`,
            method: "delete",
            });
        },
        // 批量复制数据
        getSuperviseDrillingLogCopyAPI: async (ids) => {
            return request({
            url: `supervise/superviseDrillingLog/copyBatch/${ids}`,
            method: "get",
            });
        },

  //=======================================地质监督日志：
        // 获取日志信息列表
        getSuperviseGeologyLogListAPI: async (params) => {
            return request({
            url: `supervise/superviseGeologyLog/list`,
            method: "get",
            params
            });
        },
        // 上传文件后批次新增数据
        getSuperviseGeologyLogAddBatchAPI: async (params) => {
            return request({
            url: `supervise/superviseGeologyLog/addBatchByFile`,
            method: "get",
            params,
            });
        },
                // 批次新增数据
                getSuperviseGeologyLogCustomAddAPI: async (data) => {
            return request({
            url: `supervise/superviseGeologyLog/addBatch`,
            method: "post",
            data,
            });
        },
        //   创建地质监督日志-批次更新数据
        getSuperviseGeologyLogAddAPI: async (data) => {
            return request({
            url: `supervise/superviseGeologyLog/updateBatch`,
            method: "post",
            data,
            });
        },
        //   获取日志及子表详细信息
        getSuperviseGeologyLogDetailsAPI: async (id) => {
            return request({
            url: `supervise/superviseGeologyLog/getAllInfo/${id}`,
            method: "get",
            });
        },
        // 删除list
        getSuperviseGeologyLogDeleteAPI: async (ids) => {
            return request({
            url: `supervise/superviseGeologyLog/remove/${ids}`,
            method: "delete",
            });
        },
        // 批量复制数据
        getSuperviseGeologyLogCopyAPI: async (ids) => {
            return request({
            url: `supervise/superviseGeologyLog/copyBatch/${ids}`,
            method: "get",
            });
        },

  //=======================================试油监督日志：
        // 获取日志信息列表
        getSuperviseTestLogListAPI: async (params) => {
            return request({
            url: `supervise/superviseTestLog/list`,
            method: "get",
            params
            });
        },
        // 上传文件后批次新增数据
        getSuperviseTestLogAddBatchAPI: async (params) => {
            return request({
            url: `supervise/superviseTestLog/addBatchByFile`,
            method: "get",
            params,
            });
        },
        // 批次新增数据
        getSuperviseTestLogCustomAddAPI: async (data) => {
            return request({
            url: `supervise/superviseTestLog/add`,
            method: "post",
            data,
            });
        },
        //   创建试油监督日志-批次更新数据
        getSuperviseTestLogAddAPI: async (data) => {
            return request({
            url: `supervise/superviseTestLog/updateBatch`,
            method: "post",
            data,
            });
        },
        //   获取日志及子表详细信息
        getSuperviseTestLogDetailsAPI: async (id) => {
            return request({
            url: `supervise/superviseTestLog/getInfo/${id}`,
            method: "get",
            });
        },
        // 删除list
        getSuperviseTestLogDeleteAPI: async (ids) => {
            return request({
            url: `supervise/superviseTestLog/remove/${ids}`,
            method: "delete",
            });
        },
        // 批量复制数据
        getSuperviseTestLogCopyAPI: async (ids) => {
            return request({
            url: `supervise/superviseTestLog/copyBatch/${ids}`,
            method: "get",
            });
        },
};
