import request from '@/utils/request'

// 查询提取内容明细列表
export function listDetail(query) {
  return request({
    url: '/supervise/oilConfigExtContentDetail/list',
    method: 'get',
    params: query
  })
}

// 查询提取内容明细所有列表
export function listAllDetail(query) {
  return request({
    url: '/supervise/oilConfigExtContentDetail/listAll',
    method: 'get',
    params: query
  })
}

// 查询提取内容明细详细
export function getDetail(id) {
  return request({
    url: '/supervise/oilConfigExtContentDetail/getInfo/' + id,
    method: 'get'
  })
}

// 查询提取内容明细详细
export function getDetailByExtContentId(extContentId) {
  return request({
    url: '/supervise/oilConfigExtContentDetail/getDetailByExtContentId/' + extContentId,
    method: 'get'
  })
}

// 新增提取内容明细
export function addDetail(data) {
  return request({
    url: '/supervise/oilConfigExtContentDetail/add',
    method: 'post',
    data: data
  })
}

// 修改提取内容明细
export function updateDetail(data) {
  return request({
    url: '/supervise/oilConfigExtContentDetail/edit',
    method: 'put',
    data: data
  })
}

// 删除提取内容明细
export function delDetail(id) {
  return request({
    url: '/supervise/oilConfigExtContentDetail/remove/' + id,
    method: 'delete'
  })
}

// 获取所有表明
export function getTableName() {
  return request({
    url: '/supervise/oilConfigExtContentDetail/queryTableAllList',
    method: 'get'
  })
}

// 获取所有字段
export function getTableColumn(tableName) {
  return request({
    url: '/supervise/oilConfigExtContentDetail/queryTableColumnList/' + tableName,
  })
}
