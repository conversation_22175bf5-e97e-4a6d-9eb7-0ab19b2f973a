import { taskManageAPI } from "@/api/supervise/Intelligent/taskManage.js";
import { logManageAPI } from "@/api/supervise/Intelligent/logManage.js";
import { reviewAPI } from "@/api/supervise/Intelligent/review.js";
import request from "@/utils/request";

// 智能监督
const IntelligentAPI = {
  // 作业任务管理
  ...taskManageAPI,
  // 监督日志管理
  ...logManageAPI,
  // 监督审核
  ...reviewAPI,
  // 首页左边两个接口：
  // 查询井的最新的井眼轨迹
  getWellboreTrack: async (params) => {
    return request({
      url: "/supervise/oilSuperviseMain/getWellboreTrack",
      method: "get",
      params,
    });
  },
  // 查询井的最新的井身结构
  geteWellStruct: async (params) => {
    return request({
      url: "/supervise/oilSuperviseMain/geteWellStruct",
      method: "get",
      params,
    });
  },
  // 查询井的最新的井眼轨迹
  getCalculateByWellNo: async (params) => {
    return request({
      url: "/supervise/oilSuperviseMain/getCalculateByWellNo",
      method: "get",
      params,
    });
  },
  //  查询所有监督日志文件
  getSuperviseTestLogByTastId: async (params) => {
    return request({
      url: "/supervise/superviseTestLog/getSuperviseTestLogByTastId",
      method: "get",
      params,
    });
  },
  //  查询所有任务文件
  getAllDocByTaskId: async (id) => {
    return request({
      url: "/supervise/oilSuperviseTask/getAllDocByTaskId/" + id,
      method: "get",
    });
  },
};

export default IntelligentAPI;
