import request from '@/utils/request'

// 查询提取内容配置列表
export function listOilConfigExtContent(query) {
  return request({
    url: '/supervise/oilConfigExtContent/list',
    method: 'get',
    params: query
  })
}

// 查询提取内容配置所有列表
export function listAllOilConfigExtContent(query) {
  return request({
    url: '/supervise/oilConfigExtContent/listAll',
    method: 'get',
    params: query
  })
}

// 查询提取内容配置详细
export function getOilConfigExtContent(id) {
  return request({
    url: '/supervise/oilConfigExtContent/getInfo/' + id,
    method: 'get'
  })
}

// 新增提取内容配置
export function addOilConfigExtContent(data) {
  return request({
    url: '/supervise/oilConfigExtContent/add',
    method: 'post',
    data: data
  })
}

// 修改提取内容配置
export function updateOilConfigExtContent(data) {
  return request({
    url: '/supervise/oilConfigExtContent/edit',
    method: 'put',
    data: data
  })
}

// 删除提取内容配置
export function delOilConfigExtContent(id) {
  return request({
    url: '/supervise/oilConfigExtContent/remove/' + id,
    method: 'delete'
  })
}
