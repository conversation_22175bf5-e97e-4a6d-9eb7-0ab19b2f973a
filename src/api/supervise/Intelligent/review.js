import request from "@/utils/request";




export const reviewAPI = {
    // 根据taskid提取井眼轨迹三维坐标
    getTrajectory: async (params) => {
        return request({
            url: '/supervise/oilSuperviseTaskExt/getCalculateByTaskid',
            method: 'get',
            params
        })
    },
    // 根据taskid提取井基本信息
    getWellInfo: async (params) => {
        return request({
            url: '/supervise/wellInfo/listAll',
            method: 'get',
            params
        })
    },
    // 根据taskid提取井眼轨迹(设计)
    getDesignTrajectory: async (params) => {
        return request({
            url: '/supervise/baseWellboreTrack/listAll',
            method: 'get',
            params
        })
    },
    // 根据taskid提取井眼轨迹(实施)
    getImplementTrajectory: async (params) => {
        return request({
            url: '/supervise/executeWellboreTrack/listAll',
            method: 'get',
            params
        })
    },
    // 根据taskid提取井身结构(设计)
    getDesignStructure: async (params) => {
        return request({
            url: '/supervise/baseWellStruct/listAll',
            method: 'get',
            params
        })
    },
    // 根据taskid提取井身结构(实施)
    getImplementStructure: async (params) => {
        return request({
            url: '/supervise/executeWellStruct/listAll',
            method: 'get',
            params
        })
    },
    //  对比设计和实施的结果
    getDiffResult: async (data) => {
        return request({
            url: '/supervise/superviseTestLog/getDiffResult',
            method: 'post',
            data
        })
    },
    // 根据taskid获取对比审核结果列表
    getDiffListByTaskId: async (params) => {
        return request({
            url: '/supervise/superviseReviewDetail/getDiffListByTaskId',
            method: 'get',
            params
        })
    },
    // 批量上传对比结果：
    getDiffAddBatch: async (data) => {
        return request({
            url: `supervise/superviseReviewDetail/addBatch`,
            method: "post",
            data,
        });
    },
};
