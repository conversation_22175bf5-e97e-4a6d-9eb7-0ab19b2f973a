import request from '@/utils/request'

// 查询标签列表
export function listKnowledgeLabel(query) {
  return request({
    url: 'techDocManage/knowledgeLabel/list',
    method: 'get',
    params: query
  })
}

// 查询标签所有列表
export function listAllKnowledgeLabel(query) {
  return request({
    url: 'techDocManage/knowledgeLabel/listAll',
    method: 'get',
    params: query
  })
}

// 查询标签详细
export function getKnowledgeLabel(id) {
  return request({
    url: 'techDocManage/knowledgeLabel/getInfo/' + id,
    method: 'get'
  })
}

// 新增标签
export function addKnowledgeLabel(data) {
  return request({
    url: 'techDocManage/knowledgeLabel/add',
    method: 'post',
    data: data
  })
}

// 修改标签
export function updateKnowledgeLabel(data) {
  return request({
    url: 'techDocManage/knowledgeLabel/edit',
    method: 'post',
    data: data
  })
}

// 删除标签
export function delKnowledgeLabel(id) {
  return request({
    url: 'techDocManage/knowledgeLabel/remove/' + id,
    method: 'get'
  })
}

export function check(query) {
  return request({
    url: 'techDocManage/knowledgeLabel/check',
    method: 'get',
    params: query
  })
}
