import request from '@/utils/request'

// 查询标签列表
export function listKnowledgeKeywords(query) {
  return request({
    url: 'techDocManage/knowledgeKeywords/list',
    method: 'get',
    params: query
  })
}

// 查询标签所有列表
export function listAllKnowledgeKeywords(query) {
  return request({
    url: 'techDocManage/knowledgeKeywords/listAll',
    method: 'get',
    params: query
  })
}

// 查询标签详细
export function getKnowledgeKeywords(id) {
  return request({
    url: 'techDocManage/knowledgeKeywords/getInfo/' + id,
    method: 'get'
  })
}

// 新增标签
export function addKnowledgeKeywords(data) {
  return request({
    url: 'techDocManage/knowledgeKeywords/add',
    method: 'post',
    data: data
  })
}

// 修改标签
export function updateKnowledgeKeywords(data) {
  return request({
    url: 'techDocManage/knowledgeKeywords/edit',
    method: 'post',
    data: data
  })
}

// 删除标签
export function delKnowledgeKeywords(id) {
  return request({
    url: 'techDocManage/knowledgeKeywords/remove/' + id,
    method: 'get'
  })
}

export function check(query) {
  return request({
    url: 'techDocManage/knowledgeKeywords/check',
    method: 'get',
    params: query
  })
}
