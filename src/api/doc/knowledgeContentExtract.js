import request from '@/utils/request'

// 查询标签列表
export function listKnowledgeContentExtract(query) {
  return request({
    url: 'techDocManage/knowledgeContentExtract/list',
    method: 'get',
    params: query
  })
}

// 查询标签所有列表
export function listAllKnowledgeContentExtract(query) {
  return request({
    url: 'techDocManage/knowledgeContentExtract/listAll',
    method: 'get',
    params: query
  })
}

// 查询标签详细
export function getKnowledgeContentExtract(id) {
  return request({
    url: 'techDocManage/knowledgeContentExtract/getInfo/' + id,
    method: 'get'
  })
}

// 新增标签
export function addKnowledgeContentExtract(data) {
  return request({
    url: 'techDocManage/knowledgeContentExtract/add',
    method: 'post',
    data: data
  })
}

// 修改标签
export function updateKnowledgeContentExtract(data) {
  return request({
    url: 'techDocManage/knowledgeContentExtract/edit',
    method: 'post',
    data: data
  })
}

// 删除标签
export function delKnowledgeContentExtract(id) {
  return request({
    url: 'techDocManage/knowledgeContentExtract/remove/' + id,
    method: 'get'
  })
}

export function check(query) {
  return request({
    url: 'techDocManage/knowledgeContentExtract/check',
    method: 'get',
    params: query
  })
}
