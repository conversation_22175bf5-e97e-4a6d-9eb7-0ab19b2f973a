import request from '@/utils/request'
import { param } from 'jquery'

// 登录方法
export function login(username, password, code, uuid) {
  return request({
    url: '/auth/login',
    method: 'post',
    data: { username, password, code, uuid }
  })
}
// 内登录接口
export function loginByToken(data) {
  return request({
    url: '/auth/loginByToken',
    method: 'post',
    data
  })
}
// 注册方法
export function register(data) {
  return request({
    url: '/auth/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 刷新方法
export function refreshToken() {
  return request({
    url: '/auth/refresh',
    method: 'post'
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/system/user/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/auth/TokenLogout',
    method: 'delete'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/code',
    method: 'get'
  })
}

// 获取文件系统Token
export function getFileToken() {
  return request({
    url: '/system/userDistribute/getFileSysToken',
    method: 'get'
  })
}


// 退出快开
export function devLoginOut() {
  return request({
    url: '/ServerCommand/logout',
    method: 'post'
  })
}

// 获取系统名称，系统图标和登录背景图接口
export function getSystemConfig() {
  return request({
    url: '/system/config/configKey/getSystemConfig',
    method: 'get'
  })
}

// Cas登录
export function casLogin() {
    return request({
      url: '/auth/casLogin',
      headers: {
        isToken: false
      },
      method: 'post'
    })
  }


//发起登录获取TGT和code
export function ssoLogin(username, password, code, uuid, redirectUri, clientId,responseType) {
  return request({
    url: '/sso/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: { username, password, code, uuid, redirectUri, clientId,responseType }
  })


}
// 密码模式登录
export function loginPassword(username, password, code, uuid,clientId,clientSecret) {
  const grant_type = 'password'
  return request({
    url: '/auth/oauth/token',
    method: 'post',
    params: { username, password, code, uuid, clientId, clientSecret}
  })
}

//如果已经登录会返回cdoe
export function checkSsoLogin(redirectUri, clientId) {
  let data = { 'redirectUri': redirectUri, 'clientId': clientId }
  return request({
    url: '/sso/loginCheck',
    method: 'get',
    params: data,
    headers: {
      isToken: false
    }
  })
}

//退出
export function ssoLogout() {
  return request({
    url: '/sso/logout',
    method: 'get',
    headers: {
      isToken: false
    }
  })
}
// 免登录
export function freeSecretToken(data) {
  return request({
    url: '/auth/getFreeSecretToken',
    method: 'post',
    headers: {
      isToken: false
    },
    data
  })
}