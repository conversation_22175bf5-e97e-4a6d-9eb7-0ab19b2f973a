import request from '@/utils/request'

// 查询业务字典类型列表
export function listMdmDictType(query) {
  return request({
    url: '/system/mdmDictType/list',
    method: 'get',
    params: query
  })
}

// 查询业务字典类型详细
export function getMdmDictType(dictId) {
  return request({
    url: '/system/mdmDictType/' + dictId,
    method: 'get'
  })
}

// 新增业务字典类型
export function addMdmDictType(data) {
  return request({
    url: '/system/mdmDictType',
    method: 'post',
    data: data
  })
}

// 修改业务字典类型
export function updateMdmDictType(data) {
  return request({
    url: '/system/mdmDictType',
    method: 'put',
    data: data
  })
}

// 删除业务字典类型
export function delMdmDictType(dictId) {
  return request({
    url: '/system/mdmDictType/' + dictId,
    method: 'delete'
  })
}
