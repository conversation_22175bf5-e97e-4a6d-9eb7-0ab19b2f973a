import request from '@/utils/request'

// 查询业务字典数据列表
export function listData(query) {
  return request({
    url: '/system/mdmDictData/list',
    method: 'get',
    params: query
  })
}

// 查询业务字典数据详细
export function getData(dictCode) {
  return request({
    url: '/system/mdmDictData/' + dictCode,
    method: 'get'
  })
}

// 新增业务字典数据
export function addData(data) {
  return request({
    url: '/system/mdmDictData',
    method: 'post',
    data: data
  })
}

// 修改业务字典数据
export function updateData(data) {
  return request({
    url: '/system/mdmDictData',
    method: 'put',
    data: data
  })
}

// 删除业务字典数据
export function delData(dictCode) {
  return request({
    url: '/system/mdmDictData/' + dictCode,
    method: 'delete'
  })
}
