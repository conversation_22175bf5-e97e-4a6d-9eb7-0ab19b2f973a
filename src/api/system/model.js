import request from '@/utils/request'

// 查询模型库管理列表
export function listModel(query) {
  return request({
    url: '/system/model/list',
    method: 'get',
    params: query
  })
}

// 查询模型库管理详细
export function getModel(id) {
  return request({
    url: '/system/model/' + id,
    method: 'get'
  })
}

// 新增模型库管理
export function addModel(data) {
  return request({
    url: '/system/model',
    method: 'post',
    data: data
  })
}

// 修改模型库管理
export function updateModel(data) {
  return request({
    url: '/system/model',
    method: 'put',
    data: data
  })
}

// 删除模型库管理
export function delModel(id) {
  return request({
    url: '/system/model/' + id,
    method: 'delete'
  })
}

// 修改状态
export function updateStartStop(id,state) {
  return request({
    url: '/system/model/updateStartStop/'+ id +'/' + state,
    method: 'get'
  })
}
