import request from '@/utils/request'

// 查询岗位列表
export function listPost(query) {
  return request({
    url: '/system/post/list',
    method: 'get',
    params: query
  })
}
// 查询岗位列表
export function selectPostByDept(query) {
  return request({
    url: '/system/post/selectPostByDept',
    method: 'get',
    params: query
  })
}
// 查询岗位详细
export function getPost(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'get'
  })
}

// 新增岗位
export function addPost(data) {
  return request({
    url: '/system/post',
    method: 'post',
    data: data
  })
}

// 修改岗位
export function updatePost(data) {
  return request({
    url: '/system/post',
    method: 'put',
    data: data
  })
}

// 删除岗位
export function delPost(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'delete'
  })
}

// 查询用户列表
export function selectUserByPostId(query) {
  return request({
    url: '/system/post/selectUserByPostId',
    method: 'get',
    params: query
  })
}

// 查询用户ID列表
export function selectUserIdByPostId(query) {
  return request({
    url: '/system/post/selectUserIdByPostId',
    method: 'get',
    params: query
  })
}

// 新增岗位用户
export function addPostUser(data) {
  return request({
    url: '/system/post/addPostUser',
    method: 'post',
    data: data
  })
}

// 删除岗位用户
export function deletePostUser(data) {
  return request({
    url: '/system/post/deletePostUser',
    method: 'post',
    data: data
  })
}


// 批量删除岗位用户
export function deletePostUsers(data) {
  return request({
    url: '/system/post/deletePostUsers',
    method: 'post',
    data: data
  })
}