import request from '@/utils/request'

// 查询自动排班配置列表
export function listScheduleConfig(query) {
  return request({
    url: '/system/dispatchScheduleConfig/list',
    method: 'get',
    params: query
  })
}

// 查询自动排班配置详细
export function getScheduleConfig(id) {
  return request({
    url: '/system/dispatchScheduleConfig/' + id,
    method: 'get'
  })
}

// 新增自动排班配置
export function addScheduleConfig(data) {
  return request({
    url: '/system/dispatchScheduleConfig',
    method: 'post',
    data: data
  })
}

// 修改自动排班配置
export function updateScheduleConfig(data) {
  return request({
    url: '/system/dispatchScheduleConfig',
    method: 'put',
    data: data
  })
}

// 删除自动排班配置
export function delScheduleConfig(id) {
  return request({
    url: '/system/dispatchScheduleConfig/' + id,
    method: 'delete'
  })
}
// 排班人员
export function getUsers(query) {
  return request({
    url: '/system/dispatchScheduleConfig/getUsers',
    method: 'get',
    params: query
  })
}
// 定制规则列表
export function getRule(query) {
  return request({
    url: '/system/scheduleRule/list',
    method: 'get',
    params: query
  })
}
// 修改自动排班规则
export function updateScheduleRule(data) {
  return request({
    url: '/system/scheduleRule',
    method: 'put',
    data: data
  })
}
