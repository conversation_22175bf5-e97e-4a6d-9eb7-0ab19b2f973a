import request from '@/utils/request'

// 查询排班详情列表
export function listScheduleDetail(query) {
  return request({
    url: '/system/dispatchScheduleDetail/list',
    method: 'get',
    params: query
  })
}
// 排班报表
export function report(query) {
  return request({
    url: '/system/dispatchScheduleDetail/report',
    method: 'get',
    params: query
  })
}
// 查询排班详情详细
export function getScheduleDetail(id) {
  return request({
    url: '/system/dispatchScheduleDetail/' + id,
    method: 'get'
  })
}

// 新增排班详情
export function addScheduleDetail(data) {
  return request({
    url: '/system/dispatchScheduleDetail',
    method: 'post',
    data: data
  })
}

// 修改排班详情
export function updateScheduleDetail(data) {
  return request({
    url: '/system/dispatchScheduleDetail',
    method: 'put',
    data: data
  })
}

// 删除排班详情
export function delScheduleDetail(id) {
  return request({
    url: '/system/dispatchScheduleDetail/' + id,
    method: 'delete'
  })
}
