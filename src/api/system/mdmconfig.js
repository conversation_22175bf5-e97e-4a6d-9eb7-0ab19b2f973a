import request from '@/utils/request'

// 查询快开平台业务参数配置列表
export function listMdmconfig(query) {
  return request({
    url: '/system/mdmconfig/list',
    method: 'get',
    params: query
  })
}

// 查询快开平台业务参数配置详细
export function getMdmconfig(configId) {
  return request({
    url: '/system/mdmconfig/' + configId,
    method: 'get'
  })
}

// 新增快开平台业务参数配置
export function addMdmconfig(data) {
  return request({
    url: '/system/mdmconfig',
    method: 'post',
    data: data
  })
}

// 修改快开平台业务参数配置
export function updateMdmconfig(data) {
  return request({
    url: '/system/mdmconfig',
    method: 'put',
    data: data
  })
}

// 删除快开平台业务参数配置
export function delMdmconfig(configId) {
  return request({
    url: '/system/mdmconfig/' + configId,
    method: 'delete'
  })
}
