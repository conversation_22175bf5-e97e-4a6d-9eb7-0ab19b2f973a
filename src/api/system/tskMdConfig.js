import request from '@/utils/request'

// 查询任务模板配置列表
export function listTskMdConfig(query) {
  return request({
    url: '/system/tskMdConfig/list',
    method: 'get',
    params: query
  })
}

// 查询任务模板配置详细
export function getTskMdConfig(id) {
  return request({
    url: '/system/tskMdConfig/' + id,
    method: 'get'
  })
}

// 新增任务模板配置
export function addTskMdConfig(data) {
  return request({
    url: '/system/tskMdConfig',
    method: 'post',
    data: data
  })
}

// 修改任务模板配置
export function updateTskMdConfig(data) {
  return request({
    url: '/system/tskMdConfig',
    method: 'put',
    data: data
  })
}

// 删除任务模板配置
export function delTskMdConfig(id) {
  return request({
    url: '/system/tskMdConfig/' + id,
    method: 'delete'
  })
}
// 查询任务模板配置详细
export function handleStatus(params) {
  return request({
    url: '/system/tskMdConfig/handleStatus',
    method: 'get',
    params: params
  })
}
