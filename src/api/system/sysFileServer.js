import request from '@/utils/request'

// 查询技术管理服务器列表
export function listServer() {
  return request({
    url: '/system/sysFile/server/list',
    method: 'get'
  })
}

// 查询技术管理服务器详细
export function getServer(id) {
  return request({
    url: '/system/sysFile/server/' + id,
    method: 'get'
  })
}

// 新增技术管理服务器
export function addServer(data) {
  return request({
    url: '/system/sysFile/server',
    method: 'post',
    data: data
  })
}

// 修改技术管理服务器
export function updateServer(data) {
  return request({
    url: '/system/sysFile/server',
    method: 'put',
    data: data
  })
}

// 删除技术管理服务器
export function delServer(id) {
  return request({
    url: '/system/sysFile/server/' + id,
    method: 'delete'
  })
}

// 分发
export function sendServer(data) {
  return request({
    url: '/system/sysFile/server/send',
    method: 'post',
    data: data
  })
}
// 清空机构
export function clearAllDept(data) {
  return request({
    url: '/system/sysFile/server/clearDept',
    method: 'post',
    data: data
  })
}
// 清空用户
export function clearAllUser(data) {
  return request({
    url: '/system/sysFile/server/clearUser',
    method: 'post',
    data: data
  })
}
