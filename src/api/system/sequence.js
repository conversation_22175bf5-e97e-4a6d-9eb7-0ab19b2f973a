import request from '@/utils/request'

// 查询业务编码列表
export function listSequence(query) {
  return request({
    url: '/system/sequence/list',
    method: 'get',
    params: query
  })
}

// 查询业务编码详细
export function getSequence(key) {
  return request({
    url: '/system/sequence/' + key,
    method: 'get'
  })
}

// 新增业务编码
export function addSequence(data) {
  return request({
    url: '/system/sequence',
    method: 'post',
    data: data
  })
}

// 修改业务编码
export function updateSequence(data) {
  return request({
    url: '/system/sequence',
    method: 'put',
    data: data
  })
}

// 删除业务编码
export function delSequence(key) {
  return request({
    url: '/system/sequence/' + key,
    method: 'delete'
  })
}

// 查询业务编码详细
export function getSequenceCode(data) {
  return request({
    url: '/system/sequence/getCode',
    method: 'get',
    params: data
  })
}
