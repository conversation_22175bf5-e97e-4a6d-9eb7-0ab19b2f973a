import request from '@/utils/request'
import $ from 'jquery'
import Cookies from "js-cookie";
// 查询大数据视图列表
export function listDataView(query) {
  return request({
    url: '/system/dataView/list',
    method: 'get',
    params: query
  })
}

// 查询大数据视图详细
export function getDataView(viewId) {
  return request({
    url: '/system/dataView/' + viewId,
    method: 'get'
  })
}

// 查询当前用户的大数据视图详细
export function getUserDataView() {
  return request({
    url: '/system/dataView/getUserDataView',
    method: 'get'
  })
}

// 新增大数据视图
export function addDataView(data) {
  return request({
    url: '/system/dataView',
    method: 'post',
    data: data
  })
}

// 修改大数据视图
export function updateDataView(data) {
  return request({
    url: '/system/dataView',
    method: 'put',
    data: data
  })
}

// 删除大数据视图
export function delDataView(viewId) {
  return request({
    url: '/system/dataView/' + viewId,
    method: 'delete'
  })
}

