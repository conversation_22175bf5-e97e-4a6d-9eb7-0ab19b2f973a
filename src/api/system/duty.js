import request from '@/utils/request'

// 查询值班信息列表
export function listDuty(query) {
  return request({
    url: '/system/duty/list',
    method: 'get',
    params: query
  })
}

// 查询值班信息详细
export function getDuty(id) {
  return request({
    url: '/system/duty/' + id,
    method: 'get'
  })
}

// 新增值班信息
export function addDuty(data) {
  return request({
    url: '/system/duty',
    method: 'post',
    data: data
  })
}

// 修改值班信息
export function updateDuty(data) {
  return request({
    url: '/system/duty',
    method: 'put',
    data: data
  })
}

// 删除值班信息
export function delDuty(id) {
  return request({
    url: '/system/duty/' + id,
    method: 'delete'
  })
}

// 发布
export function sendDuty(ids) {
  return request({
    url: '/system/duty/send/' + ids,
    method: 'get'
  })
}