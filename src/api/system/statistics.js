import request from '@/utils/request'

// 查询任务统计列表
export function listStatistics(query) {
  return request({
    url: '/system/statistics/list',
    method: 'get',
    params: query
  })
}

// 查询任务统计详细
export function getStatistics(systemNo) {
  return request({
    url: '/system/statistics/' + systemNo,
    method: 'get'
  })
}

// 新增任务统计
export function addStatistics(data) {
  return request({
    url: '/system/statistics',
    method: 'post',
    data: data
  })
}

// 修改任务统计
export function updateStatistics(data) {
  return request({
    url: '/system/statistics',
    method: 'put',
    data: data
  })
}

// 删除任务统计
export function delStatistics(systemNo) {
  return request({
    url: '/system/statistics/' + systemNo,
    method: 'delete'
  })
}
