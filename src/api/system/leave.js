import request from '@/utils/request'

// 查询请假列表
export function listLeave(query) {
  return request({
    url: '/system/leave/list',
    method: 'get',
    params: query
  })
}

// 查询请假详细
export function getLeave(id) {
  return request({
    url: '/system/leave/' + id,
    method: 'get'
  })
}

// 新增请假
export function addLeave(data) {
  return request({
    url: '/system/leave',
    method: 'post',
    data: data
  })
}

// 修改请假
export function updateLeave(data) {
  return request({
    url: '/system/leave',
    method: 'put',
    data: data
  })
}

// 删除请假
export function delLeave(id) {
  return request({
    url: '/system/leave/' + id,
    method: 'delete'
  })
}
// 获取流程变量-传统编码
export function getProcessVariable(id) {
  return request({
    url: '/system/leave/getProcessVariable/' + id,
    method: 'get'
  })
}
// 获取流程变量-快开平台
export function getProcessVal(query) {
  return request({
    url: '/activiti/table/getProcessVal',
    method: 'get',
    params: query
  })
}
// 启动流程
export function start(data) {
  return request({
    url: '/activiti/act/process/start',
    method: 'post',
    data: data
  })
}
//查看审批历史
export function taskHistory(id) {
  return request({
    url: '/activiti/act/task/'+id+'/detail',
    method: 'get',
  })
}
//更换审批人
export function taskRun(processId,updateId,id) {
  return request({
    url: '/activiti/act/task/run/'+processId+'/'+updateId+'/'+id,
    method: 'get',
  })
}
//选择抄送人
export function getUserList(query) {
  return request({
    url: '/activiti/group/queryUserList',
    method: 'get',
    params: query
  })
}