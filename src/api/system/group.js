import request from '@/utils/request'

// 查询工作组列表
export function listGroup(query) {
  return request({
    url: '/system/group/list',
    method: 'get',
    params: query
  })
}

// 新增工作组
export function addGroup(data) {
  return request({
    url: '/system/group',
    method: 'post',
    data: data
  })
}

// 修改工作组
export function updateGroup(data) {
  return request({
    url: '/system/group',
    method: 'put',
    data: data
  })
}

// 工作组类型树
export function getGroupTypeTree(query) {
  return request({
    url: '/system/group/getGroupTypeTree',
    method: 'get',
    params: query
  })
}




// 查询用户列表
export function selectUserByGroupId(query) {
  return request({
    url: '/system/group/selectUserByGroupId',
    method: 'get',
    params: query
  })
}

// 查询用户ID列表
export function selectUserIdByGroupId(query) {
  return request({
    url: '/system/group/selectUserIdByGroupId',
    method: 'get',
    params: query
  })
}

// 新增工作组用户
export function addGroupUser(data) {
  return request({
    url: '/system/group/addGroupUser',
    method: 'post',
    data: data
  })
}

// 删除工作组用户
export function deleteGroupUser(data) {
  return request({
    url: '/system/group/deleteGroupUser',
    method: 'post',
    data: data
  })
}


// 批量删除工作组用户
export function deleteGroupUsers(data) {
  return request({
    url: '/system/group/deleteGroupUsers',
    method: 'post',
    data: data
  })
}
