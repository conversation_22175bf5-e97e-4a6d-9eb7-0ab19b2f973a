import request from '@/utils/request'

// 查询定时任务管理列表
export function listTask(query) {
  return request({
    url: '/system/task/list',
    method: 'get',
    params: query
  })
}

// 查询定时任务管理详细
export function getTask(id) {
  return request({
    url: '/system/task/' + id,
    method: 'get'
  })
}

// 新增定时任务管理
export function addTask(data) {
  return request({
    url: '/system/task',
    method: 'post',
    data: data
  })
}

// 修改定时任务管理
export function updateTask(data) {
  return request({
    url: '/system/task',
    method: 'put',
    data: data
  })
}

// 删除定时任务管理
export function delTask(id) {
  return request({
    url: '/system/task/' + id,
    method: 'delete'
  })
}

//启用或停用定时任务
export function startOrStopTask(data) {
  return request({
    url: '/system/task/startOrStop',
    method: 'put',
    data: data
  })
}


//执行定时任务
export function executeTask(data) {
  return request({
    url: '/system/task/execute',
    method: 'post',
    data: data
  })
}