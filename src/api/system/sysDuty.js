import request from '@/utils/request'

// 用户下拉 type: 1:矿领导；2：队干；3：调度中心；4：部室
export function selectUsers(query) {
  return request({
    url: '/system/sysDuty/selectUserList',
    method: 'get',
    params: query
  })
}
//  矿领导带班  调度中心列表   type: 1:矿领导带班；2：调度中心；
export function selectLeaderClass(query) {
  return request({
    url: '/system/sysDuty/selectLeaderClass',
    method: 'get',
    params: query
  })
}
//   领导值班  部室值班  type:  1、领导值班 2、部室值班  可以不传查询2个
export function selectDutyLeader(query) {
  return request({
    url: '/system/sysDuty/selectDutyLeader',
    method: 'get',
    params: query
  })
}
// 队干跟班  班次下拉选择
export function selectClass(query) {
  return request({
    url: '/system/sysDuty/selectClass',
    method: 'get',
    params: query
  })
}
// 队干跟班  班次下拉选择
export function selectTeamClass(query) {
  return request({
    url: '/system/sysDuty/selectTeamClass',
    method: 'get',
    params: query
  })
}
// 队干值班
export function selectTeamDutyLeader(query) {
  return request({
    url: '/system/sysDuty/selectTeamDutyLeader',
    method: 'get',
    params: query
  })
}
// 分发用户
export function saveInfo(uri,data) {
  return request({
    url: uri,
    method: 'post',
    data: data
  })
}
