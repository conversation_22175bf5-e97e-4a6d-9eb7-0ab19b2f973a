import request from '@/utils/request'

// 查询列表
export function listReport(query) {
  return request({
    url: '/operationalplan/report/list',
    method: 'get',
    params: query
  })
}
// 保存报表
export function saveReport(data) {
  return request({
    url: '/operationalplan/report/save',
    method: 'post',
    data: data
  })
}
// 获取计划详情
export function getPlan(query) {
  return request({
    url: '/operationalplan/plan/query/single',
    method: 'get',
    params: query
  })
}
