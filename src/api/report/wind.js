import request from '@/utils/request'

//用风地点  硐室
export function getAirLocationType(query) {
  return request({
    url: '/operationalplan/airDistribution/getByDictType',
    method: 'get',
    params: query
  })
}
// 查询产量计划报表列表
export function listReport(query) {
  return request({
    url: '/operationalplan/airDistribution/getAirDistributionResp',
    method: 'get',
    params: query
  })
}

// 保存报表
export function saveReport(data) {
  return request({
    url: '/operationalplan/airDistribution/batchInsert',
    method: 'post',
    data: data
  })
}

// 获取计划详情
export function getPlan(query) {
  return request({
    url: '/operationalplan/plan/query/single',
    method: 'get',
    params: query
  })
}

// 预览
export function preview(data) {
  return request({
    url: '/operationalplan/airDistribution/previewAirDistributionResp',
    method: 'post',
    data: data
  })
}

// 获取配风方法
export function generalStatements(query) {
  return request({
    url: '/operationalplan/generalStatements/getList',
    method: 'get',
    params: query
  })
}
