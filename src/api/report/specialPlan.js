import request from '@/utils/request'

// 查询产量计划报表列表
export function listReport(query) {
  return request({
    url: '/operationalplan/reportSpecialPlan/list',
    method: 'get',
    params: query
  })
}

// 保存报表
export function saveReport(data) {
  return request({
    url: '/operationalplan/reportSpecialPlan/save',
    method: 'post',
    data: data
  })
}

// 获取计划详情
export function getPlan(query) {
  return request({
    url: '/operationalplan/plan/query/single',
    method: 'get',
    params: query
  })
}

// 项目编号-名称
export function projectList(query) {
  return request({
    url: '/operationalplan/project/list',
    method: 'get',
    params: query
  })
}
