import request from '@/utils/request'
/*罗华*/
// 查询列表
export function listInfo(query) {
  return request({
    url: '/operationalplan/template/list',
    method: 'get',
    params: query
  })
}

//  获取模板配置某一条详细信息
export function getInfo(templateId) {
  return request({
    url: '/operationalplan/template/' + templateId,
    method: 'get'
  })
}

// 新增表单定义
export function addInfo(data) {
  return request({
    url: '/operationalplan/template',
    method: 'post',
    data: data
  })
}

// 修改表单定义
export function updateInfo(data) {
  return request({
    url: '/operationalplan/template',
    method: 'put',
    data: data
  })
}

// 删除表单定义
export function delInfo(id) {
  return request({
    url: '/operationalplan/template/' + id,
    method: 'delete'
  })
}

//查询模板配置数据表，主表子表
export function selectTables() {
  return request({
    url: '/operationalplan/template/selectTables',
    method: 'get'
  })
}

/*家琪*/
//查询列表
export function getTaskList(data) {
  return request({
    url: '/operationalplan/operationTask/getTaskList',
    method: 'post',
    data: data
  })
}

//新增
export function insertTask(data) {
  return request({
    url: '/operationalplan/operationTask/insert',
    method: 'post',
    data: data
  })
}

//编辑详情
export function getTask(params) {
  return request({
    url: '/operationalplan/operationTask/getTask',
    method: 'get',
    params:params
  })
}

//编辑主任务
export function editMainTask(data) {
  return request({
    url: '/operationalplan/operationTask/editMainTask',
    method: 'post',
    data: data
  })
}

//编辑主任务
export function editChildTask(data) {
  return request({
    url: '/operationalplan/operationTask/editChildTask',
    method: 'post',
    data: data
  })
}

//删除
export function deleteTask(params) {
  return request({
    url: '/operationalplan/operationTask/delete',
    method: 'get',
    params:params
  })
}

//启动停止任务
export function startOrStopTask(data) {
  return request({
    url: '/operationalplan/operationTask/startOrStopTask',
    method: 'post',
    data:data
  })
}

//日志
export function getLogList(query) {
  return request({
    url: '/operationalplan/logs/list',
    method: 'get',
    params:query
  })
}

//李奕
//新增主计划
export function addMajorPlan(data) {
  return request({
    url: '/operationalplan/plan/add/majorPlan',
    method: 'post',
    data:data
  })
}
//修改主计划
export function editdMajorPlan(data) {
  return request({
    url: '/operationalplan/plan/edit/majorPlan',
    method: 'post',
    data:data
  })
}
//主计划列表
export function listMajorList(data) {
  return request({
    url: '/operationalplan/plan/query/list/majorList',
    method: 'post',
    data:data
  })
}
//子计划列表
export function listChildList(data) {
  return request({
    url: '/operationalplan/plan/query/list/childList',
    method: 'post',
    data:data
  })
}
//主计划详情
export function queryPlanId(planId) {
  return request({
    url: '/operationalplan/plan/query/single',
    method: 'get',
    params: planId
  })
}
//编辑子计划
export function editChildPlan(data) {
  return request({
    url: '/operationalplan/plan/edit/childPlan',
    method: 'post',
    data: data
  })
}
//删除主计划列表
export function deleteSingle(data) {
  return request({
    url: '/operationalplan/plan/delete/single',
    method: 'get',
    params: data
  })
}
//固定负责人
export function queryGetPrincipal(query) {
  return request({
    url: '/operationalplan/plan/query/getPrincipal',
    method: 'get',
    params: query
  })
}
//日志下拉
export function planGetMainPlanAndChildPlanById(query) {
  return request({
    url: '/operationalplan/plan/getMainPlanAndChildPlanById',
    method: 'get',
    params: query
  })
}


//审批流
//委派
export function actionAppoint(data) {
  return request({
    url: '/operationalplan/bizStatus/action/appoint',
    method: 'post',
    data: data
  })
}
//提交
export function actionSubmit(data) {
  return request({
    url: '/operationalplan/bizStatus/action/submit',
    method: 'post',
    data: data
  })
}
//派发
export function actionDistribute(data) {
  return request({
    url: '/operationalplan/bizStatus/action/distribute',
    method: 'post',
    data: data
  })
}
//撤回
export function actionRecall(data) {
  return request({
    url: '/operationalplan/bizStatus/action/recall',
    method: 'post',
    data: data
  })
}
//核定-通过
export function actionCheckApprove(data) {
  return request({
    url: '/operationalplan/bizStatus/action/checkApprove',
    method: 'post',
    data: data
  })
}
//驳回
export function actionCheckReject(data) {
  return request({
    url: '/operationalplan/bizStatus/action/checkReject',
    method: 'post',
    data: data
  })
}
//审批
export function actionApprove(data) {
  return request({
    url: '/operationalplan/bizStatus/action/approve',
    method: 'post',
    data: data
  })
}
//发布
export function actionPublish(data) {
  return request({
    url: '/operationalplan/bizStatus/action/publish',
    method: 'post',
    data: data
  })
}
