import axios from "axios";
import { getToken } from "@/utils/auth";
import request from "@/utils/request";

const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env.VUE_APP_BASE_AI,
  // 超时
  timeout: 0,
});
// const service2 = axios.create({
//   // axios中请求配置有baseURL选项，表示请求URL公共部分
//   baseURL: '',
//   // 超时
//   timeout: 0,
// });

// 文件搜索
// const fileSearchUrl = process.env.VUE_APP_BASE_AI + "/chat/knowledge_base_chat";
const serviceKnowledge = {
  //获取知识库列表
  getKnowledgeList: async () => {
    return request({
      url: "techDocManage/knowledge_base/list_knowledge_bases",
      method: "get",
    });
  },

  //创建知识库
  addKnowledgeList: async (data) => {
    return request({
      url: "techDocManage/knowledge_base/create_knowledge_base",
      method: "post",
      data,
    });
  },
  //删除知识库-ai端
  deleteKnowledgeList: async (data) => {
    return request({
      url: "techDocManage/knowledge_base/delete_knowledge_base",
      method: "post",
      data,
      headers: {
        "Content-Type": "application/json",
      },
    });
  },
  // 删除知识库-本地数据库
  removeKnowledgeDatabase: (params) => {
    return request({
      url: "techDocManage/knowledge/removeKnowledgeDatabase",
      method: "get",
      params,
    });
  },

  getPromptNameList: (params) => {
    return request({
      url: "techDocManage/knowledgePrompt/listAll",
      method: "get",
      params,
    });
  },

  //获取知识库内的文件列表
  getKnowledgeFileList: async (params) => {
    return request({
      url: "techDocManage/knowledge_base/list_files",
      method: "get",
      params,
    });
  },
  // 搜索知识库
  getDocs: async (data) => {
    // const value = await service.post("/knowledge_base/upload_docs", data);
    // return value.data;
    return request({
      url: "techDocManage/knowledge_base/upload_docs",
      method: "post",
      data,
    });
  },
  // 上传文件到知识库
  uploadFile: async (data) => {
    return request({
      url: "techDocManage/knowledge_base/upload_docs",
      method: "post",
      data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },
  republishFile: async (data) => {
    return request({
      url: "techDocManage/knowledge_base/update_docs",
      method: "post",
      data,
    });
  },
  // 删除知识库内指定文件
  deleteFile: async (data) => {
    return request({
      url: "techDocManage/knowledge_base/delete_docs",
      method: "post",
      data,
    });
  },
  // 下载对应的知识文件
  downloadFile: async (params) => {
    return request({
      url: "techDocManage/knowledge_base/download_doc",
      method: "get",
      params,
      responseType: "blob",
    });
  },
  // 发送大文件概述请求
  sendBigFileQuestion: async (data) => {
    return request({
      url: "techDocManage/knowledge_base/kb_summary_api/summary_file_to_vector_store",
      method: "post",
      data,
    });
  },
  // 与知识库对话-axios
  newChatWithKnowledge: (data) => {
    return request({
      url: "techDocManage/knowledge/answerResult",
      method: "post",
      data,
    });
  },
  // 搜索知识库
  searchDocsDetail: async (data) => {
    return request({
      url: "techDocManage/knowledge_base/search_docs_detail",
      method: "post",
      data,
    });
  },
  // 获取对话历史记录列表
  getHistory: (params) => {
    return request({
      url: "techDocManage/knowledge/getHistory",
      method: "get",
      params,
    });
  },
  getHistoryList: (params) => {
    return request({
      url: "techDocManage/knowledge/getHistoryList",
      method: "get",
      params,
    });
  },
  // 删除对话历史记录列表
  getHistoryDeleted: (id) => {
    return request({
      url: "techDocManage/knowledge/" + id,
      method: "delete",
    });
  },
  // 获取聊天参数设置
  getChatSetting: (params) => {
    if (params&&params.knowledgeName && params.knowledgeName.startsWith("template")) {
      params.knowledgeName = "临时对话";
    }
    return request({
      url: "techDocManage/knowledge/getKnowledgeSetting",
      method: "get",
      params,
    });
  },
  // 修改聊天参数设置
  chatSetting: (data) => {
    return request({
      url: "techDocManage/knowledge/updateKnowledgeSetting",
      method: "post",
      data,
    });
  },
  //翻译文件
  tslFileEng: async (url) => {
    const value = await axios.get(url, { responseType: "blob" });
    return value.data;
  },
  //文件搜索
  fileSearchList: async (data) => {
    return request({
      url: "techDocManage/chat/knowledge_base_chat",
      method: "post",
      data,
    });
  },
  // 查询是否存在选中文件的临时知识库
  queryFilesKnoledge: (params) => {
    return request({
      url: "techDocManage/knowledge/getKnowledgeDatabase",
      method: "get",
      params,
    });
  },
  // 新增临时知识库
  insertKnowledgeDatabase: (data) => {
    return request({
      url: "techDocManage/knowledge/insertKnowledgeDatabase",
      method: "post",
      data,
    });
  },
  /*   模型配置list：
  get方法：knowledgeModel/list

  模型配置获取某一个配置详情：
  get方法：knowledgeModel/getInfo/{id}

  模型配置：
  post方法：传结构体
  put方法：
  delete方法 传ids数组


  提示词配置list：
  get方法：knowledgePrompt/list

  提示词配置获取某一个配置详情：
  get方法：knowledgePrompt/getInfo/{id}

  提示词配置：
  post方法：传结构体
  put方法：
  delete方法 传ids数组 */
  // 获取模型配置list
  getModelList: (params) => {
    return request({
      url: "techDocManage/knowledgeModel/list",
      params,
    });
  },
  // 获取模型配置中某一个配置详情
  getModelInfo: (id) => {
    return request({
      url: "techDocManage/knowledgeModel/" + id,
    });
  }, // 新增模型配置
  getModelAdd: (data) => {
    return request({
      url: "techDocManage/knowledgeModel",
      method: "post",
      data,
    });
  }, // 修改模型配置
  getModelPut: (data) => {
    return request({
      url: "techDocManage/knowledgeModel",
      method: "put",
      data,
    });
  }, // 删除模型配置
  getModelDel: (id) => {
    return request({
      url: "techDocManage/knowledgeModel/" + id,
      method: "delete",
    });
  },
  // 获取提示词配置list
  getPromptList: (params) => {
    return request({
      url: "techDocManage/knowledgePrompt/list",
      params,
    });
  },
  // 获取提示词配置中某一个配置详情
  getPromptInfo: (id) => {
    return request({
      url: "techDocManage/knowledgePrompt/" + id,
    });
  }, // 新增提示词配置
  getPromptAdd: (data) => {
    return request({
      url: "techDocManage/knowledgePrompt/insertKnowledgePrompt",
      method: "post",
      data,
    });
  }, // 修改提示词配置
  getPromptPut: (data) => {
    return request({
      url: "techDocManage/knowledgePrompt/updateKnowledgePrompt",
      method: "post",
      data,
    });
  },
  // 获取知识库提示词模板
  getPromptJson: () => {
    return request({
      url: "techDocManage/knowledgePrompt/getPromptJson",
      method: "get",
    });
  },
  // 更新远程文件
  uploadRemoteFile: () => {
    return request({
      url: "techDocManage/knowledgePrompt/uploadRemoteFile",
      method: "get",
    });
  },
  // 启动远程命令
  runRemoteCommand: (params) => {
    return request({
      url: "techDocManage/knowledgePrompt/runRemoteCommand",
      method: "get",
	  params,
    });
  },
  // 删除提示词配置
  getPromptDel: (id) => {
    return request({
      url: "techDocManage/knowledgePrompt/" + id,
      method: "delete",
    });
  },
  // 获取知识库配置列表
  getKnowledgeSetting: (params) => {
    return request({
      url: "techDocManage/knowledge/getKnowledgeSetting",
      params,
    });
  },
  // 插入和更新知识库配置列表
  updateKnowledgeSetting: (data) => {
    return request({
      url: "techDocManage/knowledge/updateKnowledgeSetting",
      method: "post",
      data,
    });
  },

  // 知识库配置和功能配置列表删除
  getConfigDel: (params) => {
    return request({
      url: "techDocManage/knowledge/removeKnowledgeSetting",
      method: "get",
      params,
    });
  },
  // 生成通用SQl
  generateCreateSQL: async (question, sql) => {
    const sqlUrl =
      process.env.VUE_APP_BASE_SQL +
      "/generate_nosql?question=" +
      question +
      "&sql=" +
      sql;
    const value = await axios.get(sqlUrl);
    return value.data;
  },

  // 生成sql语句
  createSQL: async (question) => {
    const sqlUrl =
      process.env.VUE_APP_BASE_SQL + "/generate_sql?question=" + question;
    const value = await axios.get(sqlUrl);
    return value.data;
  },
  // 执行sql语句
  executeSQL: async (id) => {
    const sqlUrl = process.env.VUE_APP_BASE_SQL + "/run_sql?id=" + id;
    const value = await axios.get(sqlUrl);
    return value.data;
  },
  // 使用plotly绘图
  usePlotlyDraw: async (id) => {
    const sqlUrl =
      process.env.VUE_APP_BASE_SQL + "/generate_plotly_figure?id=" + id;
    const value = await axios.get(sqlUrl);
    return value.data;
  },
  // 查询数据库数据
  queryDatabase: async (id) => {
    const sqlUrl = process.env.VUE_APP_BASE_SQL + "/load_question?id=" + id;
    const value = await axios.get(sqlUrl);
    return value.data;
  },
  // 插入数据库聊天历史记录
  saveDatabase: (data) => {
    return request({
      url: "techDocManage/knowledge",
      method: "post",
      data,
    });
  },
  // 图数据库问答
  graphdataBaseChat: async (query) => {
    const url =
      process.env.VUE_APP_BASE_AI +
      "/knowledge_base/search_neo4j?query=" +
      query;
    const value = await axios.get(url);
    return value.data;
  },
  // 点赞操作
  giveThumbsUp: (params) => {
    return request({
      url: "techDocManage/knowledge/setKnowledgeChatLike",
      method: "get",
      params,
    });
  },
  // 图数据库查询sql
  graphdataBaseSQl: (params) => {
    return request({
      url: "techDocManage/knowledge/excuteSql",
      method: "get",
      params,
    });
  },
  // 更新历史记录
  updateKnowledgeChat: (data) => {
    return request({
      url: "techDocManage/knowledge/updateKnowledgeChat",
      method: "post",
      data,
    });
  },
  // 设置数据库公开私有
  updateKnowledgeOpen: (data) => {
    return request({
      url: "techDocManage/knowledge/updateKnowledgeOpen",
      method: "post",
      data,
    });
  },
  // 获取所有公开的知识库
  getKnowledgeOpen: () => {
    return request({
      url: "techDocManage/knowledge/getKnowledgeOpen",
      method: "get",
    });
  },
  // 查看文件切片
  viewFileSlices: async (data) => {
    return request({
      url: "techDocManage/knowledge_base/search_docs",
      method: "post",
      data,
    });
  },
  // 编辑切片文件
  editFileSlices: async (data) => {
    return request({
      url: "techDocManage/knowledge_base/update_docs_by_id",
      method: "post",
      data,
    });
  },
  // 获取文件权限详情
  getFileAuth: (params) => {
    return request({
      url: "techDocManage/result/getDocumentAuthList",
      params,
    });
  },
  // 获取功能集成文件夹信息
  getSpecifiedFolder: (params) => {
    return request({
      url: "techDocManage/docDocumentUser/getSpecifiedFolder",
      params,
    });
  },
  // 获取功能集成列表
  getCardOperation: (params) => {
    return request({
      url: "techDocManage/docDocumentUser/list",
      params,
    });
  },
  // doc转成docx文件流
  docFilesToDocxFileStreams: (data) => {
    return request({
      url: "/filecore/uploadDocToDocx",
      method: "post",
      data,
      responseType: "arraybuffer",
    });
  },
  // 通过文件名获取文件地址
  getFileUrlByName: (params) => {
    return request({
      url: "techDocManage/result/getDocumentbyFileName",
      method: "get",
      params,
    });
  },
  // 通过文件名获取文件地址
  getConvertQuery: (params) => {
    return request({
      url: "techDocManage/knowledge/getConvertQuery",
      method: "get",
      params,
    });
  },
  // es检索替换检索
  getSearchKbKeywords: async (data) => {
    return request({
      url: "techDocManage/knowledge_base/search_kb_keywords",
      method: "post",
      data,
    });
  },
  // 通过query判断 调用流式请求还是axios请求
  useEventSourceOrAioxsByQuery: async (data) => {
    return request({
      url: "techDocManage/knowledge/checkQueryType",
      method: "post",
      data,
    });
  },
  // 增加历史记录
  addEventSourceHistory: async (data) => {
    return request({
      url: "techDocManage/knowledge",
      method: "post",
      data,
    });
  },
  // 文件生成
  generateFile: async (data) => {
    return request({
      url: "assisted/repRecord/addAi",
      method: "post",
      data,
    });
  },
  // 获取历史问答记录
  getHistoryChat: async (id) => {
    return request({
      url: `techDocManage/knowledge/${id}`,
      method: "get",
    });
  },
  // 获取md格式文本
  getMdText: async (id) => {
    return request({
      url: `techDocManage/knowledge/getDocKnowledgeChatByFid/${id}`,
      method: "get",
    });
  },
  // 获取versionID
  getVersionId: async (params) => {
    return request({
      url: `filecore/docVersion/lastVersion`,
      method: "get",
      params,
    });
  },
  // 文件暂存
  saveFile: async (data) => {
    return request({
      url: `assisted/content/add`,
      method: "post",
      data,
    });
  },
  // 暂存列表
  getSaveFileList: async (params) => {
    return request({
      url: `assisted/content/getProTemList`,
      method: "get",
      params,
    });
  },
  // 删除暂存
  deleteSaveFile: async (id) => {
    return request({
      url: `assisted/content/del/${id}`,
      method: "delete",
    });
  },
  // 提取文件大纲
  getOutline: async (params) => {
    return request({
      url: `filecore/docVersion/getSynopsisToMarkDown`,
      method: "get",
      params,
    });
  },
  // 获取知识图谱数据
  getKnowledgeGraph: async ({
    docNameN = null,
    docNameM = null,
    ref = "引用",
  }) => {
    return request({
      url: `techDocManage/neoDocument/getDocumentsWithReferencesByDocName?docNameN=${docNameN}&docNameM=${docNameM}&ref=${ref}`,
      method: "post",
    });
  },
  // 获取KKfile文件地址
  getKKFileUrl: async (params) => {
    return request({
      url: `filecore/getFileURL`,
      method: "get",
      params,
    });
  },
  // 检索-根据地址获取下载文件流
  toUrlDownload: async (params) => {
    return request({
      url: `techDocManage/knowledge_base/get_search_doc`,
      method: "get",
      params,
      responseType: "blob",
    });
  },
  // 获取模板知识库信息
  getTemplateKnowledge: (params) => {
    return request({
      url: "techDocManage/knowledge/getTemplateKnowledge",
      method: "get",
      params,
    });
  },
};

export default serviceKnowledge;
