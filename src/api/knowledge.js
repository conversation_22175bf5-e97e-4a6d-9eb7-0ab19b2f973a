import request from '@/utils/http'

/**
 * 知识库相关 API
 */
const knowledgeApi = {
  /**
   * 获取知识库列表
   */
  getKnowledgeList: () => {
    return request({
      url: 'techDocManage/knowledge_base/list_knowledge_bases',
      method: 'get'
    })
  },

  /**
   * 获取用户详细信息（包含知识库权限）
   */
  getUser: (userId) => {
    return request({
      url: `/system/user/${userId}`,
      method: 'get'
    })
  },





  /**
   * 与知识库对话 - 非流式
   * @param {Object} data 对话数据
   */
  newChatWithKnowledge: (data) => {
    return request({
      url: 'techDocManage/knowledge/answerResult',
      method: 'post',
      data
    })
  },

  /**
   * 获取历史对话列表
   * @param {Object} params 查询参数
   */
  getHistoryList: (params) => {
    return request({
      url: 'techDocManage/knowledge/getHistoryList',
      method: 'get',
      params
    })
  },

  /**
   * 获取历史对话详情
   * @param {string} chatId 对话ID
   */
  getHistoryChat: (chatId) => {
    return request({
      url: `techDocManage/knowledge/getHistoryChat/${chatId}`,
      method: 'get'
    })
  },

  /**
   * 删除对话历史记录
   * @param {string} id 记录ID
   */
  deleteHistory: (id) => {
    return request({
      url: `techDocManage/knowledge/${id}`,
      method: 'delete'
    })
  },

  /**
   * 获取聊天参数设置
   * @param {Object} params 查询参数
   */
  getChatSetting: (params) => {
    if (params && params.knowledgeName && params.knowledgeName.startsWith('template')) {
      params.knowledgeName = '临时对话'
    }
    return request({
      url: 'techDocManage/knowledge/getKnowledgeSetting',
      method: 'get',
      params
    })
  },

  /**
   * 保存聊天历史记录
   * @param {Object} data 聊天数据
   */
  saveDatabase: (data) => {
    return request({
      url: 'techDocManage/knowledge',
      method: 'post',
      data
    })
  },

  /**
   * 点赞操作
   * @param {Object} params 点赞参数
   */
  giveThumbsUp: (params) => {
    return request({
      url: 'techDocManage/knowledge/setKnowledgeChatLike',
      method: 'get',
      params
    })
  },

  /**
   * 获取常用问题列表
   * @param {Object} params 查询参数
   */
  getCommonQuestions: (params) => {
    return request({
      url: 'techDocManage/help/list',
      method: 'get',
      params
    })
  }
}

export default knowledgeApi
