import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/transaction/order/list',
    method: 'get',
    params: query
  })
}

// 查询订单详细
export function getOrder(id) {
  return request({
    url: '/transaction/order/' + id,
    method: 'get'
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/transaction/order',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: '/transaction/order',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delOrder(id) {
  return request({
    url: '/transaction/order/' + id,
    method: 'delete'
  })
}

// 下订单
export function addProductOrder(uid,pid,amount) {
  return request({
    url: '/transaction/order/addOrder/' + uid + '/' + pid + '/' + amount,
    method: 'get'
  })
}