import request from "@/utils/request";
// 步骤条  ruleEdit:rule_edit:course
export function workStep(id) {
  return request({
    url: "/procedure/rule_edit/course/" + id,
    method: "get",
  });
}
// 协同编制保存  ruleEdit:rule_edit:synAdd
export function workAdd(params) {
  return request({
    url: "/procedure/rule_edit/synAdd",
    method: "post",
    data: params,
  });
}
// 协同编制完成  ruleEdit:rule_edit:synComplete
export function workFinish(params) {
  return request({
    url: "/procedure/rule_edit/synComplete",
    method: "put",
    data: params,
  });
}
// List  ruleEdit:rule_edit:list
export function tabList(params) {
  return request({
    url: "/procedure/rule_edit/ruleEditList",
    method: "get",
    params: params,
  });
}
// 详情  ruleEdit:rule_edit:query
export function workEdit(id) {
  return request({
    url: "/procedure/rule_edit/" + id,
    method: "get",
  });
}
// 删除  ruleEdit:rule_edit:remove
export function workDel(id) {
  return request({
    url: "/procedure/rule_edit/" + id,
    method: "delete",
  });
}
// 派发  ruleEditTask:rule_edit_task:dispanseTask
export function sendTask(params) {
  return request({
    url: "/procedure/rule_edit_task/dispanseTask",
    method: "put",
    data: params,
  });
}
// 独立编制保存  ruleEdit:rule_edit:independentAdd
export function workSelf(params) {
  return request({
    url: "/procedure/rule_edit/independentAdd",
    method: "post",
    data: params,
  });
}
// 独立编制完成   ruleEdit:rule_edit:independentComplete
export function finishSelf(params) {
  return request({
    url: "/procedure/rule_edit/independentComplete",
    method: "put",
    data: params,
  });
}
// 新增派发任务   ruleEditTask:rule_edit_task:addRuleEditTask
export function newTask(params) {
  return request({
    url: "/procedure/rule_edit_task/addRuleEditTask",
    method: "put",
    data: params,
  });
}
// 批量删除派发任务  ruleEditTask:rule_edit_task:removes
export function delNewTask(ids) {
  return request({
    url: "/procedure/rule_edit_task/all/" + ids,
    method: "delete",
  });
}
// 任务回退 ruleEditTask:rule_edit_task:rollbackList
export function backTask(ids) {
  return request({
    url: "/procedure/rule_edit_task/rollbackList/" + ids,
    method: "put",
  });
}
// 附件上传
export function uploadFile(params) {
  return request({
    url: "/filecore/upload",
    method: "post",
    data: params,
  });
}
// 附件下载
export function downloadFile(params) {
  return request({
    url: "/filecore/downloadSourceFile",
    method: "get",
    params: params,
    responseType: "arraybuffer",
  });
}
// 附件删除
export function deleteFile(ids) {
  return request({
    url: "/filecore/recycleBin/clear/" + ids,
    method: "delete",
  });
}
// tree
export function workTree(params) {
  return request({
    url: "/system/tree/deptTree",
    method: "get",
    params: params,
  });
}
// 撤回  ruleEdit:rule_edit:recall
export function recall(id) {
  return request({
    url: "/procedure/rule_edit/recall/" + id,
    method: "get",
  });
}

// 发布  ruleEdit:rule_edit:release
export function release(params) {
  return request({
    url: "/procedure/rule_edit/release/",
    method: "post",
    data: params,
  });
}

//审批  ruleEdit:rule_edit:approval
export function approve(params) {
  return request({
    url: "/procedure/rule_edit/approval/",
    method: "post",
    data: params,
  });
}
// 日志  ruleEditRecord:record:journal
export function logger(params) {
  return request({
    url: "/procedure/record/journal/",
    method: "post",
    data: params,
  });
}

// 日志选择框  ruleEditRecord:record:journalList
export function loggerSelect(params) {
  return request({
    url: "/procedure/record/journalList/" + params,
    method: "get",
  });
}

// 导出 ruleEditTask:rule_edit_task:export
export function exports(params) {
  return request({
    url: "/procedure/rule_edit_task/export/",
    method: "post",
    data: params,
  });
}

// 数据权限   ruleEdit:rule_edit:onOff
export function onOff(params) {
  return request({
    url: "/procedure/rule_edit/onOff/" + params,
    method: "get",
  });
}
