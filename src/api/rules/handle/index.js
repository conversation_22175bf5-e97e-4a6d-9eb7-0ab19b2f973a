import request from '@/utils/request'
// List  ruleEditTask:rule_edit_task:list
export function tabList(params) {
  return request({
    url: '/procedure/rule_edit/ruleEditTaskList',
    method: 'get',
    params: params,
  })
}
// 详情  ruleEditTask:rule_edit_task:query
export function workEdit(id) {
  return request({
    url: '/procedure/rule_edit_task/' + id,
    method: 'get',
  })
}
// 附件下载
export function downloadFile(params){
  return request({
    url:"/filecore/downloadSourceFile",
    method:"get",
    params:params,
    responseType:"arraybuffer"
  })
}
// 附件删除  ruleEditTask:rule_edit_task:remove
export function deleteFile(ids){
  return request({
    url:"/filecore/recycleBin/clear/" + ids,
    method:"delete",
  })
}
// 附件上传
export function uploadFile(params){
  return request({
    url:"/filecore/upload",
    method:"post",
    data:params
  })
}
// 暂存  ruleEditTask:rule_edit_task:interim
export function workAdd(params){
  return request({
    url:"/procedure/rule_edit_task/interim",
    method:"put",
    data:params
  })
}
// 完成  ruleEditTask:rule_edit_task:edit
export function workFinish(params){
  return request({
    url:"/procedure/rule_edit_task/complete",
    method:"put",
    data:params
  })
}
// 审批  ruleEditTask:rule_edit_task:taskApproval
export function taskApproval(id){
  return request({
    url:"/procedure/rule_edit_task/taskApproval/",
    method:"post",
    data:id
  })
}

// 提交 ruleEditTask:rule_edit_task:refer
export function taskRefer(params) {
  return request({
    url:"/procedure/rule_edit_task/refer",
    method: "put",
    data: params
  })
}