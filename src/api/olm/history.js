import request from '@/utils/request'

// 查询设备状态变更记录列表
export function listHistory(query) {
  return request({
    url: '/olm/history/list',
    method: 'get',
    params: query
  })
}

// 查询设备状态变更记录详细
export function getHistory(id) {
  return request({
    url: '/olm/history/' + id,
    method: 'get'
  })
}

// 新增设备状态变更记录
export function addHistory(data) {
  return request({
    url: '/olm/history',
    method: 'post',
    data: data
  })
}

// 修改设备状态变更记录
export function updateHistory(data) {
  return request({
    url: '/olm/history',
    method: 'put',
    data: data
  })
}

// 删除设备状态变更记录
export function delHistory(id) {
  return request({
    url: '/olm/history/' + id,
    method: 'delete'
  })
}

// 导出设备状态变更记录
export function exportHistory(query) {
  return request({
    url: '/olm/history/export',
    method: 'get',
    params: query
  })
}