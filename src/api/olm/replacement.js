import request from '@/utils/request'

// 查询替换设备列表
export function listReplacement(query) {
  return request({
    url: '/olm/replacement/list',
    method: 'get',
    params: query
  })
}

// 查询替换设备详细
export function getReplacement(id) {
  return request({
    url: '/olm/replacement/' + id,
    method: 'get'
  })
}

// 新增替换设备
export function addReplacement(data) {
  return request({
    url: '/olm/replacement',
    method: 'post',
    data: data
  })
}

// 修改替换设备
export function updateReplacement(data) {
  return request({
    url: '/olm/replacement',
    method: 'put',
    data: data
  })
}

// 删除替换设备
export function delReplacement(id) {
  return request({
    url: '/olm/replacement/' + id,
    method: 'delete'
  })
}

// 导出替换设备
export function exportReplacement(query) {
  return request({
    url: '/olm/replacement/export',
    method: 'get',
    params: query
  })
}