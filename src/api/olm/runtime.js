import request from '@/utils/request'

// 查询运行时长汇总列表
export function listRuntime(query) {
  return request({
    url: '/olm/runtime/list',
    method: 'get',
    params: query
  })
}

// 查询运行时长汇总详细
export function getRuntime(id) {
  return request({
    url: '/olm/runtime/' + id,
    method: 'get'
  })
}

// 新增运行时长汇总
export function addRuntime(data) {
  return request({
    url: '/olm/runtime',
    method: 'post',
    data: data
  })
}

// 修改运行时长汇总
export function updateRuntime(data) {
  return request({
    url: '/olm/runtime',
    method: 'put',
    data: data
  })
}

// 删除运行时长汇总
export function delRuntime(id) {
  return request({
    url: '/olm/runtime/' + id,
    method: 'delete'
  })
}

// 导出运行时长汇总
export function exportRuntime(query) {
  return request({
    url: '/olm/runtime/export',
    method: 'get',
    params: query
  })
}