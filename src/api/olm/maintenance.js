import request from '@/utils/request'

// 查询设备检修记录列表
export function listMaintenance(query) {
  return request({
    url: '/olm/maintenance/list',
    method: 'get',
    params: query
  })
}

// 查询设备检修记录详细
export function getMaintenance(id) {
  return request({
    url: '/olm/maintenance/' + id,
    method: 'get'
  })
}

// 新增设备检修记录
export function addMaintenance(data) {
  return request({
    url: '/olm/maintenance',
    method: 'post',
    data: data
  })
}

// 修改设备检修记录
export function updateMaintenance(data) {
  return request({
    url: '/olm/maintenance',
    method: 'put',
    data: data
  })
}

// 删除设备检修记录
export function delMaintenance(id) {
  return request({
    url: '/olm/maintenance/' + id,
    method: 'delete'
  })
}

// 导出设备检修记录
export function exportMaintenance(query) {
  return request({
    url: '/olm/maintenance/export',
    method: 'get',
    params: query
  })
}