import request from '@/utils/request'

// 查询运行时长明细列表
export function listDetail(query) {
  return request({
    url: '/olm/detail/list',
    method: 'get',
    params: query
  })
}

// 查询运行时长明细详细
export function getDetail(id) {
  return request({
    url: '/olm/detail/' + id,
    method: 'get'
  })
}

// 新增运行时长明细
export function addDetail(data) {
  return request({
    url: '/olm/detail',
    method: 'post',
    data: data
  })
}

// 修改运行时长明细
export function updateDetail(data) {
  return request({
    url: '/olm/detail',
    method: 'put',
    data: data
  })
}

// 删除运行时长明细
export function delDetail(id) {
  return request({
    url: '/olm/detail/' + id,
    method: 'delete'
  })
}

// 导出运行时长明细
export function exportDetail(query) {
  return request({
    url: '/olm/detail/export',
    method: 'get',
    params: query
  })
}