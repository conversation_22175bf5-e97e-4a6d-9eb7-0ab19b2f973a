import request from '@/utils/request'

// 查询设备参数列表
export function listParameter(query) {
  return request({
    url: '/olm/parameter/list',
    method: 'get',
    params: query
  })
}

// 查询设备参数详细
export function getParameter(id) {
  return request({
    url: '/olm/parameter/' + id,
    method: 'get'
  })
}

// 新增设备参数
export function addParameter(data) {
  return request({
    url: '/olm/parameter',
    method: 'post',
    data: data
  })
}

// 修改设备参数
export function updateParameter(data) {
  return request({
    url: '/olm/parameter',
    method: 'put',
    data: data
  })
}

// 删除设备参数
export function delParameter(id) {
  return request({
    url: '/olm/parameter/' + id,
    method: 'delete'
  })
}

// 导出设备参数
export function exportParameter(query) {
  return request({
    url: '/olm/parameter/export',
    method: 'get',
    params: query
  })
}