import request from '@/utils/request'


export const NODETYPE = {
  TOP: 0, //0   组织机构 1 级别，即矿务局
  MINE: 1, // 1   机构2级  矿
  SYSTYPE: 2, // 2   系统大类
  SYS: 3, // 3   系统
  EQ: 4, // 4   设备
}



/**
 * 得到树状结构上的错误信息树
 * @returns 
 */
export function getTreeErr() {
  return request({
    url: '/ServerCommand/eqtreeErrs',
    method: 'get',
  })
}
/**
 * 得到树结构的节点
 * @param {*} parentId 
 * @returns 
 */
export function getTreeData(parentId) {
  return request({
    url: '/ServerCommand/eqtree?parent_id=' + parentId,
    method: 'get',
  })
}

/**
 * 递归获取树节点
 * @param {*} parentId 
 * @returns 
 */
function eqtreeDataAll() {
  return request({
    url: '/ServerCommand/eqtreeDataAll',
    method: 'get',
  })
}

function eqtreeDataDeptAll() {
  return request({
    url: '/ServerCommand/eqtreeDataDeptAll',
    method: 'get',
  })
}


//得到
function RecursionTree(dataarray, parentId) {
  let rs = [];
  for (let i = 0; i < dataarray.length; i++) {
    let d = dataarray[i]
    if (d.parent_id == parentId)
      rs.push(d);
  }
  return rs;
}
/**
 * 得到按照分系统显示的全部节点
 * @returns 
 */
export function eqtreeSys() {
  let p = new Promise((resolve, reject) => {
    eqtreeDataAll().then((rs) => {
      let tree = []
      let data = rs.data;
      tree = RecursionTree(data, '');
      tree.forEach(element => {
        let r = RecursionTree(data, element.id)
        if (r.length > 0) {
          element.children = r;
          r.forEach(v => {
            let r1 = RecursionTree(data, v.id)
            if (r1.length > 0) {
              v.children = r1;
            }
          })
        }
      });
      resolve(tree);
    })
  });
  return p;
}


export function eqtreeDept() {
  let p = new Promise((resolve, reject) => {
    eqtreeDataDeptAll().then((rs) => {
      let tree = []
      let data = rs.data;
      //==1
      tree = RecursionTree(data, '');
      //==2
      tree.forEach(element => {
        let r = RecursionTree(data, element.id)
        if (r.length > 0) {
          element.children = r;
          //==3
          r.forEach(v => {
            let r1 = RecursionTree(data, v.id)
            if (r1.length > 0) {
              v.children = r1;

              //==4
              r1.forEach(v1 => {
                let r2 = RecursionTree(data, v1.id)
                if (r2.length > 0) {
                  v1.children = r2;
                }
              })

            }
          })
        }
      });
      resolve(tree);
    })
  });
  return p;
}


/**
 *  点击节点，得到设备和工作时间段信息
 * @param {*} id 
 * @param {*} level 
 * @returns 
 */
export function getStatuCount(id, level) {
  return request({
    url: '/ServerCommand/getStatuCount?id=' + id + "&level=" + level,
    method: 'get',
  })
}

/**
 * 得到仪表盘显示的参数最新值
 * @param {*} equipment_id 
 * @returns 
 */
export function getParameterLastValue(equipment_id) {
  return request({
    url: '/ServerCommand/getParameterLastValue?equipment_id=' + equipment_id,
    method: 'get',
  })
}
/**
 * 修改设备状态
 * @param {*} equipment_id 
 * @param {*} status 
 * @returns 
 */
export function updateEquipmentStatus(equipment_id, status) {
  return request({
    url: '/ServerCommand/changeEquipmentStatus',
    method: 'post',
    data: {
      id: equipment_id,
      status: status
    }
  })
}

/**
 * 回撤系统
 * @param {*} sys_id 
 * @param {*} status 
 * @returns 
 */
export function changeEquipmentStatusBySysId(sys_id, status) {
  return request({
    url: '/ServerCommand/changeEquipmentStatusBySysId',
    method: 'post',
    data: {
      id: sys_id,
      status: status
    }
  })
}



/**
 * 增加临时检修记录
 * @param {*} form 
 * @returns 
 */
export function updateTemparyFix(form) {
  return request({
    url: '/ServerCommand/TemparyFix',
    method: 'post',
    data: form
  })
}

//-----一下为设备运行监控-----------
/**
 * 得到指定时间段内设备额 过煤量 运行时长的平均值，卡片显示用
 * @param {*} id 
 * @param {*} level 
 * @param {*} begintime 
 * @param {*} endtime 
 * @returns 
 */
export function getEquipmentAvg(id, level, begintime, endtime, sumtype) {
  return request({
    url: '/ServerCommand/getEquipmentAvg?'
      + "id=" + id
      + "&level=" + level
      + "&begintime=" + begintime
      + "&endtime=" + endtime
      + "&sumtype=" + sumtype
    ,
    method: 'get'

  })
}

/**
 * 得到指定时间段内过煤量，按照汇总方式汇总
 * @param {*} id 
 * @param {*} level 
 * @param {*} begintime 
 * @param {*} endtime 
 * @param {*} sumtype 
 * @returns 
 */
export function getChartCoal(id, level, begintime, endtime, sumtype) {
  return request({
    url: '/ServerCommand/getChartCoal?'
      + "id=" + id
      + "&level=" + level
      + "&begintime=" + begintime
      + "&endtime=" + endtime
      + "&sumtype=" + sumtype
    ,
    method: 'get'

  })
}


/**
 * 得到报警信息
 * @returns 
 */
export function getAlarms() {
  return request({
    url: '/ServerCommand/getAlarms',
    method: 'get'

  })
}


