import request from '@/utils/request'

// 查询系统名列表
export function listSubsys(query) {
  return request({
    url: '/olm/subsys/list',
    method: 'get',
    params: query
  })
}

// 查询系统名详细
export function getSubsys(id) {
  return request({
    url: '/olm/subsys/' + id,
    method: 'get'
  })
}

// 新增系统名
export function addSubsys(data) {
  return request({
    url: '/olm/subsys',
    method: 'post',
    data: data
  })
}

// 修改系统名
export function updateSubsys(data) {
  return request({
    url: '/olm/subsys',
    method: 'put',
    data: data
  })
}

// 删除系统名
export function delSubsys(id) {
  return request({
    url: '/olm/subsys/' + id,
    method: 'delete'
  })
}

// 导出系统名
export function exportSubsys(query) {
  return request({
    url: '/olm/subsys/export',
    method: 'get',
    params: query
  })
}