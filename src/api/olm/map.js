import request from '@/utils/request'

// 查询数据映射列表
export function listMap(query) {
  return request({
    url: '/olm/map/list',
    method: 'get',
    params: query
  })
}

// 查询数据映射详细
export function getMap(id) {
  return request({
    url: '/olm/map/' + id,
    method: 'get'
  })
}

// 新增数据映射
export function addMap(data) {
  return request({
    url: '/olm/map',
    method: 'post',
    data: data
  })
}

// 修改数据映射
export function updateMap(data) {
  return request({
    url: '/olm/map',
    method: 'put',
    data: data
  })
}

// 删除数据映射
export function delMap(id) {
  return request({
    url: '/olm/map/' + id,
    method: 'delete'
  })
}

// 导出数据映射
export function exportMap(query) {
  return request({
    url: '/olm/map/export',
    method: 'get',
    params: query
  })
}