import request from '@/utils/request'

// 查询系统参数配置列表
export function listConfig(query) {
  return request({
    url: '/olm/config/list',
    method: 'get',
    params: query
  })
}

/**
 * 测试取数数据库是否成功
 * @param {*} params 
 * @returns 
 */
export function test(params) {
  return request({
    url: '/olm/config/test',
    method: 'post',
    data: params
  })
}

// 查询系统参数配置详细
export function getConfigDB(mineId) {
  return request({
    url: '/olm/config/dbcfg/' + mineId,
    method: 'get'
  })
}


// 查询系统参数配置详细
export function getConfig(id) {
  return request({
    url: '/olm/config/' + id,
    method: 'get'
  })
}

// 新增系统参数配置
export function addConfig(data) {
  return request({
    url: '/olm/config',
    method: 'post',
    data: data
  })
}

// 修改系统参数配置
export function updateConfig(data) {
  return request({
    url: '/olm/config',
    method: 'put',
    data: data
  })
}

// 删除系统参数配置
export function delConfig(id) {
  return request({
    url: '/olm/config/' + id,
    method: 'delete'
  })
}

// 导出系统参数配置
export function exportConfig(query) {
  return request({
    url: '/olm/config/export',
    method: 'get',
    params: query
  })
}



// 得到工业数据库中表
export function getAvailableTable(mineId) {
  return request({
    url: '/olm/config/available/' +mineId,
    method: 'get'
  })
}

