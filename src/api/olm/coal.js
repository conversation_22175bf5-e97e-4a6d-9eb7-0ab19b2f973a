import request from '@/utils/request'

// 查询系统过煤量列表
export function listCoal(query) {
  return request({
    url: '/olm/coal/list',
    method: 'get',
    params: query
  })
}

// 查询系统过煤量详细
export function getCoal(id) {
  return request({
    url: '/olm/coal/' + id,
    method: 'get'
  })
}

// 新增系统过煤量
export function addCoal(data) {
  return request({
    url: '/olm/coal',
    method: 'post',
    data: data
  })
}

// 修改系统过煤量
export function updateCoal(data) {
  return request({
    url: '/olm/coal',
    method: 'put',
    data: data
  })
}

// 删除系统过煤量
export function delCoal(id) {
  return request({
    url: '/olm/coal/' + id,
    method: 'delete'
  })
}

// 导出系统过煤量
export function exportCoal(query) {
  return request({
    url: '/olm/coal/export',
    method: 'get',
    params: query
  })
}