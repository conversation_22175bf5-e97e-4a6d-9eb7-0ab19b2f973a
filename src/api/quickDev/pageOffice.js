import request from '@/utils/request'

export function crtWord(query) {
  return request({
    url: '/system/pageOffice/createWord',
    method: 'get',
    params: query
  })
}

export function openWord(data) {
    return request({
      url: '/system/pageOffice/preview',
      method: 'get',
      params: data
    })
}

export function updateWord(data) {
  return request({
    url: '/system/pageOffice/updateWord',
    method: 'get',
    params: data
  })
}

