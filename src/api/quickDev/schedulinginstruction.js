import request from '@/utils/request'

// 查询调度指令列表
export function listSchedulinginstruction(query) {
  return request({
    url: '/system/schedulinginstruction/list',
    method: 'get',
    params: query
  })
}

// 查询调度指令详细
export function getSchedulinginstruction(id) {
  return request({
    url: '/system/schedulinginstruction/' + id,
    method: 'get'
  })
}

// 查询部门下拉树结构
export function treeselect() {
  return request({
    url: '/system/schedulinginstruction/treeselect',
    method: 'get'
  })
}

// 新增调度指令
export function addSchedulinginstruction(data) {
  return request({
    url: '/system/schedulinginstruction',
    method: 'post',
    data: data
  })
}

// 修改调度指令
export function updateSchedulinginstruction(data) {
  return request({
    url: '/system/schedulinginstruction',
    method: 'put',
    data: data
  })
}

// 删除调度指令
export function delSchedulinginstruction(id) {
  return request({
    url: '/system/schedulinginstruction/' + id,
    method: 'delete'
  })
}

// 上传
export function uploadSysFile(data) {
  return request({
    url: '/system/userDistribute/upload',
    method: 'post',
    data: data
  })
}

// 更新文件路径
export function updateFilePath(data) {
  return request({
    url: '/system/schedulinginstruction/updateFilePath',
    method: 'post',
    data: data
  })
}



// 修改是否归档
export function updateState(data) {
  return request({
    url: '/system/schedulinginstruction/updateState',
    method: 'post',
    data: data
  })
}

// 获取流程审批历史List
export function getActDeHiTasksByInstId(id) {
  return request({
    url: '/system/schedulinginstruction/getActDeHiTasksByInstId/' + id,
    method: 'get'
  })
}


// 获取流程变量-传统编码
export function getProcessVariable(id) {
  return request({
    url: '/system/schedulinginstruction/getProcessVariable/' + id,
    method: 'get'
  })
}


// 启动流程
export function start(data) {
  return request({
    url: '/activiti/act/process/start',
    method: 'post',
    data: data
  })
}
