import request from '@/utils/request'


// 获取快开页面地址
export function getUrl(data) {
  return request({
    url: '/auth/grapecity/getGrapecityUrl',
    method: 'post',
    data: data
  })
}



// 获取文件管理系统登录页面地址
export function getSignInPath() {
  return request({
    url: '/system/userDistribute/getSignInPath',
    method: 'get'
  })
}

// 获取文件管理系统结构目录
export function getFileSysList(data) {
  return request({
    url: '/system/userDistribute/getFileSysList',
    method: 'post',
    data : data
  })
}

// 调用快开子任务同步状态检查更新接口
export function auditstatusupdate(subtaskid) {
  return request({
    url: '/ServerCommand/task_auditstatusupdate?subtaskid='+subtaskid,
    method: 'post'
  })
}

  //更换审批人
  export function taskRun(processId,updateId,id) {
    return request({
      url: '/activiti/act/task/run/'+processId+'/'+updateId+'/'+id,
      method: 'get',
    })
  }
  //附件预览
  export function preview(data) {
    return request({
      url: '/file/preview',
      method: 'post',
      data : data
    })
  }
