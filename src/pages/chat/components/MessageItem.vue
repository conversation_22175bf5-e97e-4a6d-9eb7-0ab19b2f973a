<script setup lang="ts">
import { computed } from 'vue'
import MarkdownRenderer from './MarkdownRenderer.vue'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  isStreaming?: boolean
  docs?: any[]
  chart_data?: string
}

interface Props {
  message: Message
  currentKnowledgeName?: string
}

const props = defineProps<Props>()

// 处理文档数据
const processedDocs = computed(() => {
  if (!props.message.docs?.length) return []

  const docMap = new Map()

  props.message.docs.forEach(docStr => {
    try {
      const doc = JSON.parse(docStr)
      const isPdf = doc.filename?.toLowerCase().endsWith('.pdf')
      const fileName = doc.filename

      if (docMap.has(fileName)) {
        if (isPdf && doc.page) {
          const existing = docMap.get(fileName)
          if (!existing.pages.includes(doc.page)) {
            existing.pages.push(doc.page)
          }
        }
      } else {
        docMap.set(fileName, {
          fileName,
          link: doc.url,
          page: doc.page,
          fileType: isPdf ? 'pdf' : 'word',
          pages: isPdf && doc.page ? [doc.page] : []
        })
      }
    } catch (error) {
      console.error('解析文档失败:', error)
    }
  })

  return Array.from(docMap.values())
})

// 处理图表数据
const processedChart = computed(() => {
  if (!props.message.chart_data) return null

  try {
    return JSON.parse(props.message.chart_data)
  } catch (error) {
    console.error('解析图表数据失败:', error)
    return null
  }
})
</script>

<template>
  <view 
    class="message-item"
    :class="{ 'user-message': message.type === 'user', 'ai-message': message.type === 'assistant' }"
  >
    <view class="message-content">
      <!-- 头像 -->
      <view class="avatar" :class="message.type === 'user' ? 'avatar-user' : 'avatar-ai'">
        <text class="avatar-text">{{ message.type === 'user' ? '我' : 'AI' }}</text>
      </view>

      <!-- 消息气泡 -->
      <view class="message-bubble">
        <!-- 用户消息显示纯文本 -->
        <text v-if="message.type === 'user'" class="message-text">{{ message.content }}</text>

        <!-- AI消息使用 Markdown 渲染 -->
        <MarkdownRenderer
          v-else
          :content="message.content"
          :is-streaming="message.isStreaming"
        />

        <!-- 流式输出动画 -->
        <view v-if="message.isStreaming" class="typing-indicator">
          <text class="typing-dot">●</text>
          <text class="typing-dot">●</text>
          <text class="typing-dot">●</text>
        </view>

        <!-- 文档来源 -->
        <view v-if="message.type === 'assistant' && processedDocs.length > 0" class="docs-section">
          <view class="section-title">参考文件</view>
          <view class="docs-list">
            <view v-for="(doc, index) in processedDocs" :key="index" class="doc-item">
              <text class="doc-name">{{ doc.fileName }}</text>
              <view v-if="doc.fileType === 'pdf' && doc.pages.length > 0" class="pages-info">
                <text class="pages-label">页码：</text>
                <text v-for="(page, pageIndex) in doc.pages" :key="pageIndex" class="page-number">{{ page }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 图表数据 -->
        <view v-if="message.type === 'assistant' && processedChart" class="chart-section">
          <view class="section-title">图表数据</view>
          <view class="chart-container">
            <!-- 简单的表格显示 -->
            <view v-if="processedChart.type === 'table' && processedChart.data" class="chart-table">
              <view class="table-title">{{ processedChart.title }}</view>
              <view class="table-content">
                <view v-for="(row, rowIndex) in processedChart.data.slice(0, 5)" :key="rowIndex" class="table-row">
                  <text v-for="(value, key) in row" :key="key" class="table-cell">{{ value }}</text>
                </view>
                <view v-if="processedChart.data.length > 5" class="table-more">
                  <text class="more-text">还有 {{ processedChart.data.length - 5 }} 行数据...</text>
                </view>
              </view>
            </view>
            <!-- 其他图表类型的提示 -->
            <view v-else class="chart-placeholder">
              <text class="chart-type">{{ processedChart.type || '图表' }}</text>
              <text class="chart-title">{{ processedChart.title }}</text>
            </view>
          </view>
        </view>


      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.message-item {
  margin-bottom: $spacing-xl;

  &.user-message .message-content {
    flex-direction: row-reverse;

    .message-bubble {
      background-color: $accent-color;
      color: #FFFFFF;
      margin-left: 80rpx;
    }
  }

  &.ai-message .message-content {
    flex-direction: row;

    .message-bubble {
      background-color: $chat-bubble-bot-bg;
      color: $text-primary;
      border: 1rpx solid $border-color;
      margin-right: 80rpx;
    }
  }
}

.message-content {
  display: flex;
  gap: $spacing-lg;
  align-items: flex-start;
}

.avatar {
  width: $avatar-size;
  height: $avatar-size;
  border-radius: $border-radius-circle;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 500;

  &.avatar-user {
    background: linear-gradient(135deg, $accent-color 0%, #0d47d9 100%);
    color: #FFFFFF;
  }

  &.avatar-ai {
    background: linear-gradient(135deg, #10B981 0%, #06B6D4 50%, #3B82F6 100%);
    color: #FFFFFF;
  }
}

.message-bubble {
  max-width: calc(100% - 144rpx);
  padding: $spacing-lg $spacing-xl;
  border-radius: $border-radius-sm;
  position: relative;
  word-wrap: break-word;
}

.message-text {
  font-size: 30rpx;
  line-height: 1.6;
}

// Markdown 渲染器样式调整
:deep(.markdown-renderer) {
  .markdown-content {
    .github-markdown-body {
      font-size: 30rpx !important;
    }
  }

  .streaming-indicator {
    margin-top: 8rpx;
  }
}



// 文档来源样式
.docs-section {
  margin-top: $spacing-lg;
  padding: $spacing-lg;
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: $border-radius-sm;
  border-left: 4rpx solid $accent-color;
}

.section-title {
  font-size: 26rpx;
  font-weight: 600;
  color: $accent-color;
  margin-bottom: $spacing-sm;
}

.docs-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.doc-item {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.doc-name {
  font-size: 28rpx;
  color: $text-primary;
  font-weight: 500;
}

.pages-info {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.pages-label {
  font-size: 24rpx;
  color: $text-secondary;
}

.page-number {
  font-size: 24rpx;
  color: $accent-color;
  background-color: rgba(20, 91, 255, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-right: $spacing-xs;
}

// 图表数据样式
.chart-section {
  margin-top: $spacing-lg;
  padding: $spacing-lg;
  background-color: rgba(16, 185, 129, 0.05);
  border-radius: $border-radius-sm;
  border-left: 4rpx solid #10B981;
}

.chart-container {
  margin-top: $spacing-sm;
}

.chart-table {
  .table-title {
    font-size: 28rpx;
    font-weight: 600;
    color: $text-primary;
    margin-bottom: $spacing-sm;
  }

  .table-content {
    border: 1rpx solid $border-color;
    border-radius: $border-radius-sm;
    overflow: hidden;
  }

  .table-row {
    display: flex;
    border-bottom: 1rpx solid $border-color;

    &:last-child {
      border-bottom: none;
    }
  }

  .table-cell {
    flex: 1;
    padding: $spacing-sm;
    font-size: 26rpx;
    color: $text-primary;
    border-right: 1rpx solid $border-color;
    word-break: break-all;

    &:last-child {
      border-right: none;
    }
  }

  .table-more {
    padding: $spacing-sm;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.02);
  }

  .more-text {
    font-size: 24rpx;
    color: $text-secondary;
  }
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-xl;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: $border-radius-sm;

  .chart-type {
    font-size: 32rpx;
    font-weight: 600;
    color: #10B981;
    margin-bottom: $spacing-sm;
  }

  .chart-title {
    font-size: 28rpx;
    color: $text-primary;
    text-align: center;
  }
}

.typing-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: $spacing-sm;
}

.typing-dot {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  animation: typing 1.4s infinite;

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

@keyframes typing {
  0%, 60%, 100% {
    opacity: 0.3;
  }
  30% {
    opacity: 1;
  }
}
</style>
