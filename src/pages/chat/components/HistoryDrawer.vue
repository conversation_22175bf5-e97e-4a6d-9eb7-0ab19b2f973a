<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/store/user'

interface History {
  id: string
  query?: string
  create_time: string
}

interface Props {
  show: boolean
  historyList: History[]
}

defineProps<Props>()

const emit = defineEmits<{
  close: []
  selectHistory: [history: History]
}>()

const userStore = useUserStore()

// 触摸相关状态
const touchStartX = ref(0)
const touchStartY = ref(0)
const drawerTransform = ref(0)
const isDragging = ref(false)

const handleClose = () => {
  emit('close')
}

const handleSelectHistory = (history: History) => {
  emit('selectHistory', history)
}

// 触摸开始
const handleTouchStart = (e: TouchEvent) => {
  touchStartX.value = e.touches[0].clientX
  touchStartY.value = e.touches[0].clientY
  isDragging.value = false
}

// 触摸移动
const handleTouchMove = (e: TouchEvent) => {
  const currentX = e.touches[0].clientX
  const currentY = e.touches[0].clientY
  const deltaX = currentX - touchStartX.value
  const deltaY = Math.abs(currentY - touchStartY.value)

  // 只有水平滑动距离大于垂直滑动距离时才处理
  if (Math.abs(deltaX) > deltaY && deltaX < 0) {
    isDragging.value = true
    drawerTransform.value = Math.max(deltaX, -300) // 限制最大拖拽距离
    e.preventDefault()
  }
}

// 触摸结束
const handleTouchEnd = () => {
  if (isDragging.value) {
    // 如果拖拽距离超过阈值，关闭抽屉
    if (drawerTransform.value < -100) {
      handleClose()
    }
    // 重置状态
    drawerTransform.value = 0
    isDragging.value = false
  }
}

// 退出登录
const handleLogout = () => {
  // 先关闭历史抽屉
  emit('close')

  // 延迟显示确认对话框，确保抽屉完全关闭
  setTimeout(() => {
    uni.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          userStore.logout()
        }
      }
    })
  }, 200)
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 24 * 60 * 60 * 1000) {
    // 今天
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  } else if (diff < 7 * 24 * 60 * 60 * 1000) {
    // 一周内
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  } else {
    // 超过一周
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  }
}
</script>

<template>
  <!-- 使用 transition 包裹整个抽屉 -->
  <transition name="drawer" appear>
    <view v-if="show" class="history-drawer" @click="handleClose">
      <!-- 抽屉内容也使用 transition 实现滑入效果 -->
      <transition name="drawer-slide" appear>
        <view
          v-if="show"
          class="drawer-content"
          :class="{ 'is-dragging': isDragging }"
          :style="{ transform: `translateX(${drawerTransform}px)` }"
          @click.stop
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
        >
          <view class="drawer-header">
            <text class="drawer-title">历史对话</text>
          </view>
          <scroll-view class="history-list" scroll-y>
            <!-- 为历史项添加进入动画 -->
            <transition-group name="history-item" appear>
              <view
                v-for="(history, index) in historyList"
                :key="history.id"
                class="history-item"
                :style="{ 'animation-delay': `${index * 50}ms` }"
                @click="handleSelectHistory(history)"
              >
                <view class="history-content">
                  <text class="history-title">{{ history.query || '对话记录' }}</text>
                  <text class="history-time">{{ formatTime(history.create_time) }}</text>
                </view>
                <view class="history-arrow">
                  <text class="arrow-icon">›</text>
                </view>
              </view>
            </transition-group>
            <view v-if="historyList.length === 0" class="empty-history">
              <text class="empty-text">暂无历史对话</text>
            </view>
          </scroll-view>

          <!-- 用户信息区域 -->
          <view class="user-info-section">
            <view class="user-info">
              <view class="user-avatar">
                <text class="avatar-text">{{ (userStore.userInfo.userName || '用户').charAt(0).toUpperCase() }}</text>
              </view>
              <view class="user-details">
                <text class="user-name">{{ userStore.userInfo.userName || '用户' }}</text>
                <text class="user-role">{{ userStore.userInfo.nickName || '普通用户' }}</text>
              </view>
            </view>
            <view class="logout-btn" @click="handleLogout">
              <text class="logout-text">退出</text>
            </view>
          </view>
        </view>
      </transition>
    </view>
  </transition>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

// 抽屉遮罩动画
.drawer-enter-active, .drawer-leave-active {
  transition: opacity 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.drawer-enter-from, .drawer-leave-to {
  opacity: 0;
}

// 抽屉内容滑入动画
.drawer-slide-enter-active {
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.drawer-slide-leave-active {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.6, 1);
}

.drawer-slide-enter-from {
  transform: translateX(-100%);
}

.drawer-slide-leave-to {
  transform: translateX(-100%);
}

// 历史项动画
.history-item-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.history-item-enter-from {
  opacity: 0;
  transform: translateX(-30rpx);
}

.history-drawer {
  @include drawer-style;
  backdrop-filter: blur(10rpx);
}

.drawer-content {
  @include drawer-content;
  border-radius: 0 24rpx 24rpx 0;
  overflow: hidden;

  &.is-dragging {
    transition: none; // 拖拽时禁用过渡动画
  }
}

.drawer-header {
  @include header-style;
  background: linear-gradient(135deg, $header-bg-color 0%, rgba($accent-color, 0.05) 100%);
  justify-content: center; // 标题居中显示
}

.drawer-title {
  font-size: 32rpx;
  font-weight: 600;
  color: $text-primary;
}

.history-list {
  flex: 1;
  padding: $spacing-md 0;
}

.history-item {
  padding: $spacing-lg $spacing-xl;
  margin: 0 $spacing-md $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba($border-color, 0.5);
  transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:active {
    background-color: rgba($accent-color, 0.1);
    border-color: rgba($accent-color, 0.3);
    transform: translateX(8rpx);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.history-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
  flex: 1;
  min-width: 0; // 确保文本截断正常工作
}

.history-title {
  font-size: 28rpx;
  color: $text-primary;
  line-height: 1.4;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.history-time {
  font-size: 24rpx;
  color: $text-secondary;
  font-weight: 400;
}

.history-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: $spacing-md;
  opacity: 0.6;
  transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.arrow-icon {
  font-size: 32rpx;
  color: $text-secondary;
  font-weight: 300;
}

.history-item:active .history-arrow {
  opacity: 1;
  transform: translateX(4rpx);
}

.empty-history {
  height: 200rpx;
  @include empty-state;
  margin: $spacing-xl;
  border-radius: $border-radius-md;
  background-color: rgba(255, 255, 255, 0.5);
}

.empty-text {
  font-size: 28rpx;
  color: $text-secondary;
  font-weight: 400;
}

.user-info-section {
  border-top: 1rpx solid rgba($border-color, 0.3);
  padding: $spacing-lg $spacing-xl;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba($accent-color, 0.05) 100%);
  backdrop-filter: blur(20rpx);
}

.user-info {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex: 1;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: $border-radius-circle;
  background: linear-gradient(135deg, $accent-color 0%, lighten($accent-color, 10%) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba($accent-color, 0.3);
}

.avatar-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #FFFFFF;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: $text-primary;
}

.user-role {
  font-size: 24rpx;
  color: $text-secondary;
  font-weight: 400;
}

.logout-btn {
  padding: 16rpx 24rpx;
  background-color: rgba($accent-color, 0.1);
  border: 1rpx solid rgba($accent-color, 0.2);
  border-radius: $border-radius-md;
  transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);

  &:active {
    background-color: rgba($accent-color, 0.2);
    border-color: rgba($accent-color, 0.4);
    transform: scale(0.98);
  }
}

.logout-text {
  font-size: 26rpx;
  color: $accent-color;
  font-weight: 600;
}
</style>
