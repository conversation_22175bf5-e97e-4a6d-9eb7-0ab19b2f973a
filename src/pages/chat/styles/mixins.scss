// 公共混入样式
@import './variables.scss';

// 按钮悬停效果
@mixin button-hover {
  transition: $transition-fast;
  
  &:active {
    background-color: $button-hover-bg;
  }
}

// 卡片样式
@mixin card-style {
  background-color: $sidebar-bg-color;
  border-radius: $border-radius-sm;
  box-shadow: $shadow-light;
}

// 弹窗遮罩
@mixin modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

// 弹窗内容
@mixin modal-content {
  @include card-style;
  width: 80%;
  max-height: 60%;
  overflow: hidden;
  box-shadow: $shadow-medium;
}

// 抽屉样式
@mixin drawer-style {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

// 抽屉内容
@mixin drawer-content {
  width: 80%;
  height: 100%;
  background-color: $sidebar-bg-color;
  box-shadow: $shadow-heavy;
  display: flex;
  flex-direction: column;
}

// 头部样式
@mixin header-style {
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $spacing-xl;
  border-bottom: 1rpx solid $border-color;
  background-color: $header-bg-color;
}

// 列表项样式
@mixin list-item {
  padding: $spacing-lg $spacing-xl;
  border-bottom: 1rpx solid #F0F0F0;
  transition: $transition-fast;
  
  &:active {
    background-color: $button-hover-bg;
  }
  
  &:last-child {
    border-bottom: none;
  }
}

// 空状态样式
@mixin empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: $spacing-sm;
  color: $text-secondary;
}

// 加载动画
@mixin loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #F3F3F3;
  border-top: 6rpx solid $accent-color;
  border-radius: $border-radius-circle;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
