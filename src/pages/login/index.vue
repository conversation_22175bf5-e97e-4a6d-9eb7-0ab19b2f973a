<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { getCodeImg } from '@/api/auth'
import { encryptPassword } from '@/utils/crypto'

const userStore = useUserStore()

// 表单数据
const formData = ref({
  username: 'admin',
  password: 'bzcAiAdmin2024',
  code: '',
  uuid: ''
})

// 登录状态
const isLoading = ref(false)

// 验证码相关
const codeUrl = ref('')
const captchaOnOff = ref(true)

// 获取验证码
const getCode = async () => {
  try {
    const response = await getCodeImg()
    if (response.code === 200) {
      captchaOnOff.value = response.captchaOnOff === undefined ? true : response.captchaOnOff
      if (captchaOnOff.value) {
        codeUrl.value = "data:image/gif;base64," + response.img
        formData.value.uuid = response.uuid
      }
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    uni.showToast({
      title: '获取验证码失败',
      icon: 'none'
    })
  }
}

// 登录方法
const handleLogin = async () => {
  // 表单验证
  if (!formData.value.username || !formData.value.password) {
    uni.showToast({
      title: '请输入用户名和密码',
      icon: 'none'
    })
    return
  }

  if (captchaOnOff.value && !formData.value.code) {
    uni.showToast({
      title: '请输入验证码',
      icon: 'none'
    })
    return
  }

  isLoading.value = true

  try {
    // 加密密码
    const loginData = {
      ...formData.value,
      password: encryptPassword(formData.value.password)
    }

    const result = await userStore.login(loginData)

    if (result.success) {
      uni.showToast({
        title: '登录成功',
        icon: 'success'
      })

      // 跳转到聊天页面
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/chat/index'
        })
      }, 1500)
    }

  } catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: error.message || '登录失败，请重试',
      icon: 'none'
    })

    // 登录失败后刷新验证码
    if (captchaOnOff.value) {
      getCode()
    }
  } finally {
    isLoading.value = false
  }
}

// 忘记密码
const handleForgotPassword = () => {
  uni.showToast({
    title: '请联系管理员重置密码',
    icon: 'none'
  })
}

// 注册
const handleRegister = () => {
  uni.showToast({
    title: '请联系管理员开通账号',
    icon: 'none'
  })
}

// 页面加载时获取验证码
onMounted(() => {
  getCode()
})
</script>

<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="background-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 登录卡片 -->
    <view class="login-card">
      <!-- 头部 -->
      <view class="login-header">
        <view class="logo-container">
          <view class="logo-icon">🤖</view>
        </view>
        <text class="app-name">灵犀智问</text>
        <text class="app-desc">智能问答助手</text>
      </view>

      <!-- 表单区域 -->
      <view class="login-form">
        <!-- 用户名输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <image class="icon-svg" src="/static/icons/user.svg" mode="aspectFit" />
            </view>
            <input
              class="form-input"
              v-model="formData.username"
              placeholder="请输入用户名"
              type="text"
            />
          </view>
        </view>

        <!-- 密码输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <image class="icon-svg" src="/static/icons/lock.svg" mode="aspectFit" />
            </view>
            <input
              class="form-input"
              v-model="formData.password"
              placeholder="请输入密码"
              type="password"
            />
          </view>
        </view>

        <!-- 验证码 -->
        <view class="input-group" v-if="captchaOnOff">
          <view class="captcha-row">
            <view class="input-wrapper captcha-input-wrapper">
              <view class="input-icon">
                <image class="icon-svg" src="/static/icons/code.svg" mode="aspectFit" />
              </view>
              <input
                class="form-input"
                v-model="formData.code"
                placeholder="请输入验证码"
                type="text"
              />
            </view>
            <view class="captcha-container" @click="getCode">
              <image
                v-if="codeUrl"
                class="captcha-image"
                :src="codeUrl"
                mode="aspectFit"
              />
              <view v-else class="captcha-placeholder">
                <text class="captcha-text">点击获取</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 登录按钮 -->
        <button
          class="login-btn"
          :class="{ 'loading': isLoading }"
          @click="handleLogin"
          :disabled="isLoading"
        >
          <view v-if="isLoading" class="loading-spinner"></view>
          <text class="btn-text">{{ isLoading ? '登录中...' : '立即登录' }}</text>
        </button>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
// 使用与聊天页面一致的颜色变量
$bg-color: #F7F8FB;
$sidebar-bg-color: #FFFFFF;
$text-primary: #313233;
$text-secondary: #949699;
$accent-color: #145BFF;
$border-color: #E8E8E8;
$placeholder-text: #C6C8CC;

.login-container {
  height: 100vh;
  background-color: $bg-color;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

/* 背景装饰 - 简化版 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  opacity: 0.4;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(20, 91, 255, 0.03) 0%, rgba(13, 71, 217, 0.01) 100%);

  &.circle-1 {
    width: 400rpx;
    height: 400rpx;
    top: -200rpx;
    right: -200rpx;
  }

  &.circle-2 {
    width: 300rpx;
    height: 300rpx;
    bottom: -150rpx;
    left: -150rpx;
  }

  &.circle-3 {
    width: 200rpx;
    height: 200rpx;
    top: 50%;
    left: -100rpx;
    transform: translateY(-50%);
  }
}

/* 登录卡片 */
.login-card {
  width: 100%;
  max-width: 560rpx;
  max-height: 90vh;
  background-color: $sidebar-bg-color;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.02);
  overflow: hidden;
  z-index: 1;
  position: relative;
}

/* 头部区域 */
.login-header {
  text-align: center;
  padding: 40rpx 32rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.logo-container {
  margin-bottom: 24rpx;
}

.logo-icon {
  font-size: 48rpx;
  display: inline-block;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, $accent-color 0%, #0d47d9 100%);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 4rpx 16rpx rgba(20, 91, 255, 0.15);
}

.app-name {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: 8rpx;
}

.app-desc {
  display: block;
  font-size: 24rpx;
  color: $text-secondary;
  line-height: 1.4;
}

/* 表单区域 */
.login-form {
  padding: 32rpx;
}

.input-group {
  margin-bottom: 20rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background-color: #FAFBFC;
  border: 1rpx solid #E8E9EB;
  border-radius: 16rpx;
  padding: 0 20rpx;
  transition: all 0.2s ease;

  &:focus-within {
    border-color: $accent-color;
    background-color: #FFFFFF;
    box-shadow: 0 0 0 3rpx rgba(20, 91, 255, 0.08);
  }
}

.input-icon {
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon-svg {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.input-wrapper:focus-within .icon-svg {
  opacity: 1;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 30rpx;
  color: $text-primary;
  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: $placeholder-text;
  }
}

/* 验证码行 */
.captcha-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.captcha-input-wrapper {
  flex: 1;
}

.captcha-container {
  width: 160rpx;
  height: 80rpx;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.captcha-image {
  width: 100%;
  height: 100%;
}

.captcha-placeholder {
  text-align: center;
  background-color: #F8F9FA;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.captcha-text {
  font-size: 24rpx;
  color: $text-secondary;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 76rpx;
  background-color: $accent-color;
  color: #FFFFFF;
  border: none;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  margin: 24rpx 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.2s ease;

  &:active {
    background-color: #0d47d9;
    transform: scale(0.98);
  }

  &.loading {
    opacity: 0.8;
  }

  &[disabled] {
    opacity: 0.6;
    background-color: #D1D5DB;
    transform: none;
  }
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #FFFFFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
}



@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
