/*基础公共样式*/
ul,li{
	padding: 0;
	list-style: none;
}
.w100{
	width: 100% !important;
}
.w50{
	width: 50% !important;
}
.flex{
	display: flex;
}
.flex-start{
	justify-content: flex-start;
}
.flex-end{
	justify-content: flex-end;
}
.flex-space-around{
  justify-content: space-around;
}
.flex-space-between{
	justify-content: space-between;
}
.flex-align-items-center {
  align-items: center;
}
.flex-align-items-end {
  align-items: end;
}
.flex-justify-content-center {
  justify-content: center;
}
.flex-column{
  flex-direction: column;
}
.list-table{
	margin-top: 20px;
	td{
		// padding: 13px 0;
	}
}
.mb20{
	margin-bottom: 20px;
}
.mr5{
	margin-right: 5px;
}
.mr10{
	margin-right: 10px;
}
.primary {
  color: #409EFF;
}

.success {
  color: #67C23A;
}

.warning {
  color: #E6A23C;
}

.danger {
  color: #F56C6C;
}

.info {
  color: #909399;
}
.el-card__header{
	.header{
		span{
			margin-right: 30px;
		}
	}
}
.addEdit{
	.el-input--medium .el-input__inner{
		height: 36px;
		line-height: 36px;
	}
}
//影响全局表单验证样式，暂时注释掉，有问题沟通张强
.el-form-item{
  // margin-bottom: 10px;
}