::v-deep .table-part{
    .el-tabs__header{
      margin-top: 16px;
      margin-bottom: 16px !important;
    }
    .el-tabs__nav-wrap::after{
      height: 1px;
    }
    .el-tabs__active-bar{
      height: 1px;
    }
    .table-part-title{
      position: relative;
      .table-part-form{
         position: absolute;
         top: 0;
         right: 0;
         border: none;
      }
			// .el-tabs__content{
			// 	overflow-y:scroll;
			// 	height: calc(100vh - 220px);
			// }
    }
    .el-tabs__nav-wrap::after{
       background-color:#FFFFFF;
    }
  }
  ::v-deep .el-checkbox{
    width: 100% !important;
    margin: 0 !important;
    line-height: 26px !important;
  }
  ::v-deep  .el-table__empty-block{
    width: 100% !important;
  }
  ::v-deep .detail-part{
    margin-bottom: 16px;
    .detail-part-top{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      .btn-group{
        text-align: right;
      }
    }
    .detail-part-view{
      display: flex;
      justify-content: space-between;
      padding: 10px 20px;
      .detail-part-discribe{
        width: 50%;
        .discribe{
          padding: 0 8px;
          border-left: 4px solid #145BFF;
          line-height: 14px
        }
        .detail-part-discribe-title{
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .content{
          margin-top: 30px;
          height: 260px;
          line-height: 26px;
          overflow-y: scroll;
          overflow-x: hidden;
          .echarts-part{
            width: 90%;
            height: 98%;
          }
          .boder-line{
            margin: 0 8px;
            height: 14px;
            line-height: 14px;
            border-left: 1px solid #E2E3E9;
          }
        }
      }
    }
    .el-timeline .el-timeline-item:last-child{
      padding-bottom: 0;
    }
    .el-timeline .el-timeline-item:last-child .el-timeline-item__tail{
      display: block;
    }
  }
  .index-part{
    .index-part-view{
      padding: 10px 20px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .index-part-view-detail{
        width: 273px;
        // height: 154px;
        margin-right: 20px;
        margin-bottom: 20px;
        padding: 16px;
        background-color: #ECF5FF;
        border: 1px solid #E2E3E9;
        .discribe{
          padding: 0 8px;
          border-left: 4px solid #145BFF;
          line-height: 14px;
          color:  #1B1D22;
          font-weight: 500;
          margin-bottom: 16px;
        }
        .color-normal{
          color:  #1B1D22;
        }
        .index-detail{
          line-height: 22px;
          color: #8D959D;
          margin-bottom: 4px;
        }
      }
    }
  }

  .detail-title{
    font-weight: 600;
  }
  .w5{
    font-weight: 600;
  }
  .hide-echarts-part{
    width: 100%;
    height: 600px;
  }
  *::-webkit-scrollbar-track       //scroll轨道背景
  {
      border-radius: 0;
      background-color:#ffffff;
  }
  .c1{
    color: #87CEFA;
  }
  .c2{
    color: #FFFF00;
  }
  .c3{
    color: #FFA500;
  }
  .c4{
    color: #D9001B;
  }
