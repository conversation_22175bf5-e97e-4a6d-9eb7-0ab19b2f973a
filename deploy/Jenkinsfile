pipeline {
    agent {
        node {
            label 'nodejs'
        }

    }

    environment {
        DOCKER_CREDENTIAL_ID = 'dockerhub-id'
        GITHUB_CREDENTIAL_ID = 'github-id'
        KUBECONFIG_CREDENTIAL_ID = 'demo-kubeconfig'
        REGISTRY = '*************'
        DOCKERHUB_NAMESPACE = 'coalmine-cloud'
        APP_NAME = 'coalmine-ui'
        APP_HOME= '.'
    }

    stages {
        stage('拉取代码') {
            agent none
            steps {
                checkout(scm)
            }
        }

        stage('项目编译') {
            agent none
            steps {
                container('nodejs') {
                    //sh 'npm i node-sass --sass_binary_site=https://npm.taobao.org/mirrors/node-sass/'
                    sh 'cd  $APP_HOME && npm install --registry=https://registry.npm.taobao.org'
                    sh 'cd  $APP_HOME && npm run build:prod'
                    sh 'ls'
                }

            }
        }

        stage('构建镜像') {
            agent none
            steps {
                container('nodejs') {
                    sh 'ls'
                    sh 'docker build -f $APP_HOME/deploy/Dockerfile -t $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$BRANCH_NAME-$BUILD_NUMBER .'
                }

            }
        }

        stage('推送镜像') {
            agent none
            steps {
                container('nodejs') {
                    withCredentials([usernamePassword(passwordVariable : 'DOCKER_PASSWORD' ,usernameVariable : 'DOCKER_USERNAME' ,credentialsId : "$DOCKER_CREDENTIAL_ID" ,)]) {
                        sh 'echo "$DOCKER_PASSWORD" | docker login $REGISTRY -u "$DOCKER_USERNAME" --password-stdin'
                        sh 'docker push  $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$BRANCH_NAME-$BUILD_NUMBER'
                    }

                }

            }
        }

         stage('部署到dev环境') {
              steps {
                  container ('nodejs') {
                      withCredentials([
                          kubeconfigFile(
                          credentialsId: env.KUBECONFIG_CREDENTIAL_ID,
                          variable: 'KUBECONFIG')
                          ]) {
                          sh 'kubectl apply -f $APP_HOME/deploy/dev/$APP_NAME-cm.yaml'
                          sh 'envsubst < $APP_HOME/deploy/dev/$APP_NAME.yaml | kubectl apply -f -'
                      }
                  }
              }
         }

        stage('通知') {
        parallel {
            stage('钉钉通知') {
            agent none
            steps {
                sh '''curl --location --request POST \'https://oapi.dingtalk.com/robot/send?access_token=d74c4066015b5285a6d9d1e2a06876a0739f19718b98ca183ad65bb15d87f655\' \\
    --header \'Content-Type: application/json\' \\
    --data-raw \'{
        "msgtype": "text",
        "text": {"content": "[通知]coalmine-ui 第 $BUILD_NUMBER 次构建成功!"},
            "at": {
            "atMobiles":[
                "18700186061"
            ],
            "isAtAll": false
        }
    }\''''
            }
            }

        }
        }

    }
}
