kind: ConfigMap
apiVersion: v1
metadata:
  name: coalmine-ui-config
  namespace: coalmine-dev
data:
  nginx.conf: |-
    upstream gateway {
        server coalmine-gateway:8080;
    }

    server {
        listen       80;
        server_name  _;
        charset utf-8;

        location / {
            root   /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
            index  index.html index.htm;
        }
        location ^~/prod-api {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_buffering off;
            rewrite ^/prod-api/(.*)$ /$1 break;
            proxy_pass http://gateway;
        }
    }