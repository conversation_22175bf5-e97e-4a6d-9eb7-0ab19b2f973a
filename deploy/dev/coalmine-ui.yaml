---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: coalmine-ui
    component: coalmine-ui
    tier: backend
  name: coalmine-ui
  namespace: coalmine-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: coalmine-ui
      component: coalmine-ui
      tier: backend
  template:
    metadata:
      labels:
        app: coalmine-ui
        component: coalmine-ui
        tier: backend
    spec:
      containers:
        - env:
            - name: NACOS_HOST
              value: nacos-dev
          image: $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$BRANCH_NAME-$BUILD_NUMBER
          readinessProbe:
            httpGet:
              path: /
              port: 80
            timeoutSeconds: 10
            failureThreshold: 30
            periodSeconds: 5
          imagePullPolicy: IfNotPresent
          name: coalmine-ui
          ports:
            - containerPort: 80
              protocol: TCP
          resources:
            limits:
              cpu: 300m
              memory: 600Mi
            requests:
              cpu: 100m
              memory: 100Mi
          volumeMounts:
            - name: volume-wq4c04
              readOnly: true
              mountPath: /etc/nginx/conf.d
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      volumes:
        - name: volume-wq4c04
          configMap:
            name: coalmine-ui-config
            items:
              - key: nginx.conf
                path: nginx.conf
            defaultMode: 420
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      imagePullSecrets:
        - name: http-docker-hub
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: coalmine-ui
    component: coalmine-ui
  name: coalmine-ui
  namespace: coalmine-dev
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
      nodePort: 30010
  selector:
    app: coalmine-ui
    component: coalmine-ui
    tier: backend
  sessionAffinity: None
  type: NodePort