<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>demo</title>
        <script type="text/javascript" src="./jquery.min.js"></script>
        <script type="text/javascript" src="./main.js"></script>
    </head>
    <body>
        <div id="doc-viewer-01"></div>

        <script type="text/javascript">
            var viewer = EdoViewer.createViewer('doc-viewer-01', {
                    'width': 100,
                    'height': 600
                  });
            var path = getCookie("fileOpenPath");
            var token = getCookie("file_access_token");
            viewer.load_from_wo(path,token);

            function getCookie(objName) {//获取指定名称的cookie的值
              var arrStr = document.cookie.split("; ");
              for (var i = 0; i < arrStr.length; i++) {
                var temp = arrStr[i].split("=");
                if (temp[0] == objName) {
                  return decodeURI(temp[1]);
                }
              }
            }
        </script>
    </body>
</html>
