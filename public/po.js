function Save() {
  document.getElementById("PageOfficeCtrl1").WebSave();
}
// 保存并关闭
function SaveAndClose() {
  document.getElementById("PageOfficeCtrl1").WebSave();
  if(document.getElementById("PageOfficeCtrl1").CustomSaveResult == 'true'){
    window.external.close();
  }
}
// 打开数据区域
function ShowDefineDataRegions() {
  document.getElementById("PageOfficeCtrl1").ShowHtmlModelessDialog("/security/riskAnalysis/dataRegion", "parameter=xx", "left=700px;top=350px;width=645px;height=450px;frame:no;");
}
function ShowDefineDataTags() {
  document.getElementById("PageOfficeCtrl1").ShowHtmlModelessDialog("DataTagDlg.htm", "parameter=xx", "left=700px;top=320px;width=480px;height=420px;frame:no;");
}
// 打开惯用语
function ShowIdiomsDataRegions() {
  let usermsg = document.getElementById("PageOfficeCtrl1").getAttribute('usermsg')
  document.getElementById("PageOfficeCtrl1").ShowHtmlModelessDialog("/security/docSupport/idiomsRegion", usermsg, "left=700px;top=300px;width=748px;height=450px;frame:no;");
}

//获取后台添加的书签名称字符串
function getBkNames() {
  var bkNames = document.getElementById("PageOfficeCtrl1").DataRegionList.DefineNames;
  return bkNames;
}

//获取后台添加的书签文本字符串
function getBkConts() {
  var bkConts = document.getElementById("PageOfficeCtrl1").DataRegionList.DefineCaptions;
  return bkConts;
}

//定位书签
function locateBK(bkName) {
  var drlist = document.getElementById("PageOfficeCtrl1").DataRegionList;
  drlist.GetDataRegionByName(bkName).Locate();
  document.getElementById("PageOfficeCtrl1").Activate();
  window.focus();
}

//添加书签
function addBookMark(param) {
  var tmpArr = param.split("=");
  var bkName = tmpArr[0];
  var content = tmpArr[1];
  var drlist = document.getElementById("PageOfficeCtrl1").DataRegionList;
  drlist.Refresh();
  try {
      document.getElementById("PageOfficeCtrl1").Document.Application.Selection.Collapse(0);
      drlist.Add(bkName, content);
      return "true";
  } catch (e) {
      return "false";
  }
}

//删除书签
function delBookMark(bkName) {
  var drlist = document.getElementById("PageOfficeCtrl1").DataRegionList;
  try {
      drlist.Delete(bkName);
      return "true";
  } catch (e) {
      return "false";
  }
}

//遍历当前Word中已存在的书签名称
function checkBkNames() {
  var drlist = document.getElementById("PageOfficeCtrl1").DataRegionList;
  drlist.Refresh();
  var bkName = "";
  var bkNames = "";
  for (var i = 0; i < drlist.Count; i++) {
      bkName = drlist.Item(i).Name;
      bkNames += bkName.substr(3) + ",";
  }
  return bkNames.substr(0, bkNames.length - 1);
}

//遍历当前Word中已存在的书签文本
function checkBkConts() {
  var drlist = document.getElementById("PageOfficeCtrl1").DataRegionList;
  drlist.Refresh();
  var bkCont = "";
  var bkConts = "";
  for (var i = 0; i < drlist.Count; i++) {
      bkCont = drlist.Item(i).Value;
      bkConts += bkCont + ",";
  }
  return bkConts.substr(0, bkConts.length - 1);
}
