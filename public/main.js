/*	SWFObject v2.0 <http://code.google.com/p/swfobject/>
	Copyright (c) 2007 <PERSON>, <PERSON>, and <PERSON>
	This software is released under the MIT License <http://www.opensource.org/licenses/mit-license.php>
*/
var swfobject = function() {
  var Z = 'undefined', P = 'object', B = 'Shockwave Flash', h = 'ShockwaveFlash.ShockwaveFlash',
    W = 'application/x-shockwave-flash', K = 'SWFObjectExprInst', G = window, g = document, N = navigator, f = [],
    H = [], Q = null, L = null, T = null, S = false, C = false
  var a = function() {
    var l = typeof g.getElementById != Z && typeof g.getElementsByTagName != Z && typeof g.createElement != Z && typeof g.appendChild != Z && typeof g.replaceChild != Z && typeof g.removeChild != Z && typeof g.cloneNode != Z,
      t = [0, 0, 0], n = null
    if (typeof N.plugins != Z && typeof N.plugins[B] == P) {
      n = N.plugins[B].description
      if (n) {
        n = n.replace(/^.*\s+(\S+\s+\S+$)/, '$1')
        t[0] = parseInt(n.replace(/^(.*)\..*$/, '$1'), 10)
        t[1] = parseInt(n.replace(/^.*\.(.*)\s.*$/, '$1'), 10)
        t[2] = /r/.test(n) ? parseInt(n.replace(/^.*r(.*)$/, '$1'), 10) : 0
      }
    } else {
      if (typeof G.ActiveXObject != Z) {
        var o = null, s = false
        try {
          o = new ActiveXObject(h + '.7')
        } catch (k) {
          try {
            o = new ActiveXObject(h + '.6')
            t = [6, 0, 21]
            o.AllowScriptAccess = 'always'
          } catch (k) {
            if (t[0] == 6) {
              s = true
            }
          }
          if (!s) {
            try {
              o = new ActiveXObject(h)
            } catch (k) {
            }
          }
        }
        if (!s && o) {
          try {
            n = o.GetVariable('$version')
            if (n) {
              n = n.split(' ')[1].split(',')
              t = [parseInt(n[0], 10), parseInt(n[1], 10), parseInt(n[2], 10)]
            }
          } catch (k) {
          }
        }
      }
    }
    var v = N.userAgent.toLowerCase(), j = N.platform.toLowerCase(),
      r = /webkit/.test(v) ? parseFloat(v.replace(/^.*webkit\/(\d+(\.\d+)?).*$/, '$1')) : false, i = false,
      q = j ? /win/.test(j) : /win/.test(v), m = j ? /mac/.test(j) : /mac/.test(v)/*@cc_on i=true;@if(@_win32)q=true;@elif(@_mac)m=true;@end@*/
    return { w3cdom: l, pv: t, webkit: r, ie: i, win: q, mac: m }
  }()
  var e = function() {
    if (!a.w3cdom) {
      return
    }
    J(I)
    if (a.ie && a.win) {
      try {
        g.write('<script id=__ie_ondomload defer=true src=//:><\/script>')
        var i = c('__ie_ondomload')
        if (i) {
          i.onreadystatechange = function() {
            if (this.readyState == 'complete') {
              this.parentNode.removeChild(this)
              V()
            }
          }
        }
      } catch (j) {
      }
    }
    if (a.webkit && typeof g.readyState != Z) {
      Q = setInterval(function() {
        if (/loaded|complete/.test(g.readyState)) {
          V()
        }
      }, 10)
    }
    if (typeof g.addEventListener != Z) {
      g.addEventListener('DOMContentLoaded', V, null)
    }
    M(V)
  }()

  function V() {
    if (S) {
      return
    }
    if (a.ie && a.win) {
      var m = Y('span')
      try {
        var l = g.getElementsByTagName('body')[0].appendChild(m)
        l.parentNode.removeChild(l)
      } catch (n) {
        return
      }
    }
    S = true
    if (Q) {
      clearInterval(Q)
      Q = null
    }
    var j = f.length
    for (var k = 0; k < j; k++) {
      f[k]()
    }
  }

  function J(i) {
    if (S) {
      i()
    } else {
      f[f.length] = i
    }
  }

  function M(j) {
    if (typeof G.addEventListener != Z) {
      G.addEventListener('load', j, false)
    } else {
      if (typeof g.addEventListener != Z) {
        g.addEventListener('load', j, false)
      } else {
        if (typeof G.attachEvent != Z) {
          G.attachEvent('onload', j)
        } else {
          if (typeof G.onload == 'function') {
            var i = G.onload
            G.onload = function() {
              i()
              j()
            }
          } else {
            G.onload = j
          }
        }
      }
    }
  }

  function I() {
    var l = H.length
    for (var j = 0; j < l; j++) {
      var m = H[j].id
      if (a.pv[0] > 0) {
        var k = c(m)
        if (k) {
          H[j].width = k.getAttribute('width') ? k.getAttribute('width') : '0'
          H[j].height = k.getAttribute('height') ? k.getAttribute('height') : '0'
          if (O(H[j].swfVersion)) {
            if (a.webkit && a.webkit < 312) {
              U(k)
            }
            X(m, true)
          } else {
            if (H[j].expressInstall && !C && O('6.0.65') && (a.win || a.mac)) {
              D(H[j])
            } else {
              d(k)
            }
          }
        }
      } else {
        X(m, true)
      }
    }
  }

  function U(m) {
    var k = m.getElementsByTagName(P)[0]
    if (k) {
      var p = Y('embed'), r = k.attributes
      if (r) {
        var o = r.length
        for (var n = 0; n < o; n++) {
          if (r[n].nodeName.toLowerCase() == 'data') {
            p.setAttribute('src', r[n].nodeValue)
          } else {
            p.setAttribute(r[n].nodeName, r[n].nodeValue)
          }
        }
      }
      var q = k.childNodes
      if (q) {
        var s = q.length
        for (var l = 0; l < s; l++) {
          if (q[l].nodeType == 1 && q[l].nodeName.toLowerCase() == 'param') {
            p.setAttribute(q[l].getAttribute('name'), q[l].getAttribute('value'))
          }
        }
      }
      m.parentNode.replaceChild(p, m)
    }
  }

  function F(i) {
    if (a.ie && a.win && O('8.0.0')) {
      G.attachEvent('onunload', function() {
        var k = c(i)
        if (k) {
          for (var j in k) {
            if (typeof k[j] == 'function') {
              k[j] = function() {
              }
            }
          }
          k.parentNode.removeChild(k)
        }
      })
    }
  }

  function D(j) {
    C = true
    var o = c(j.id)
    if (o) {
      if (j.altContentId) {
        var l = c(j.altContentId)
        if (l) {
          L = l
          T = j.altContentId
        }
      } else {
        L = b(o)
      }
      if (!(/%$/.test(j.width)) && parseInt(j.width, 10) < 310) {
        j.width = '310'
      }
      if (!(/%$/.test(j.height)) && parseInt(j.height, 10) < 137) {
        j.height = '137'
      }
      g.title = g.title.slice(0, 47) + ' - Flash Player Installation'
      var n = a.ie && a.win ? 'ActiveX' : 'PlugIn', k = g.title,
        m = 'MMredirectURL=' + G.location + '&MMplayerType=' + n + '&MMdoctitle=' + k, p = j.id
      if (a.ie && a.win && o.readyState != 4) {
        var i = Y('div')
        p += 'SWFObjectNew'
        i.setAttribute('id', p)
        o.parentNode.insertBefore(i, o)
        o.style.display = 'none'
        G.attachEvent('onload', function() {
          o.parentNode.removeChild(o)
        })
      }
      R({ data: j.expressInstall, id: K, width: j.width, height: j.height }, { flashvars: m }, p)
    }
  }

  function d(j) {
    if (a.ie && a.win && j.readyState != 4) {
      var i = Y('div')
      j.parentNode.insertBefore(i, j)
      i.parentNode.replaceChild(b(j), i)
      j.style.display = 'none'
      G.attachEvent('onload', function() {
        j.parentNode.removeChild(j)
      })
    } else {
      j.parentNode.replaceChild(b(j), j)
    }
  }

  function b(n) {
    var m = Y('div')
    if (a.win && a.ie) {
      m.innerHTML = n.innerHTML
    } else {
      var k = n.getElementsByTagName(P)[0]
      if (k) {
        var o = k.childNodes
        if (o) {
          var j = o.length
          for (var l = 0; l < j; l++) {
            if (!(o[l].nodeType == 1 && o[l].nodeName.toLowerCase() == 'param') && !(o[l].nodeType == 8)) {
              m.appendChild(o[l].cloneNode(true))
            }
          }
        }
      }
    }
    return m
  }

  function R(AE, AC, q) {
    var p, t = c(q)
    if (typeof AE.id == Z) {
      AE.id = q
    }
    if (a.ie && a.win) {
      var AD = ''
      for (var z in AE) {
        if (AE[z] != Object.prototype[z]) {
          if (z == 'data') {
            AC.movie = AE[z]
          } else {
            if (z.toLowerCase() == 'styleclass') {
              AD += ' class="' + AE[z] + '"'
            } else {
              if (z != 'classid') {
                AD += ' ' + z + '="' + AE[z] + '"'
              }
            }
          }
        }
      }
      var AB = ''
      for (var y in AC) {
        if (AC[y] != Object.prototype[y]) {
          AB += '<param name="' + y + '" value="' + AC[y] + '" />'
        }
      }
      t.outerHTML = '<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"' + AD + '>' + AB + '</object>'
      F(AE.id)
      p = c(AE.id)
    } else {
      if (a.webkit && a.webkit < 312) {
        var AA = Y('embed')
        AA.setAttribute('type', W)
        for (var x in AE) {
          if (AE[x] != Object.prototype[x]) {
            if (x == 'data') {
              AA.setAttribute('src', AE[x])
            } else {
              if (x.toLowerCase() == 'styleclass') {
                AA.setAttribute('class', AE[x])
              } else {
                if (x != 'classid') {
                  AA.setAttribute(x, AE[x])
                }
              }
            }
          }
        }
        for (var w in AC) {
          if (AC[w] != Object.prototype[w]) {
            if (w != 'movie') {
              AA.setAttribute(w, AC[w])
            }
          }
        }
        t.parentNode.replaceChild(AA, t)
        p = AA
      } else {
        var s = Y(P)
        s.setAttribute('type', W)
        for (var v in AE) {
          if (AE[v] != Object.prototype[v]) {
            if (v.toLowerCase() == 'styleclass') {
              s.setAttribute('class', AE[v])
            } else {
              if (v != 'classid') {
                s.setAttribute(v, AE[v])
              }
            }
          }
        }
        for (var u in AC) {
          if (AC[u] != Object.prototype[u] && u != 'movie') {
            E(s, u, AC[u])
          }
        }
        t.parentNode.replaceChild(s, t)
        p = s
      }
    }
    return p
  }

  function E(k, i, j) {
    var l = Y('param')
    l.setAttribute('name', i)
    l.setAttribute('value', j)
    k.appendChild(l)
  }

  function c(i) {
    return g.getElementById(i)
  }

  function Y(i) {
    return g.createElement(i)
  }

  function O(k) {
    var j = a.pv, i = k.split('.')
    i[0] = parseInt(i[0], 10)
    i[1] = parseInt(i[1], 10)
    i[2] = parseInt(i[2], 10)
    return (j[0] > i[0] || (j[0] == i[0] && j[1] > i[1]) || (j[0] == i[0] && j[1] == i[1] && j[2] >= i[2])) ? true : false
  }

  function A(m, j) {
    if (a.ie && a.mac) {
      return
    }
    var l = g.getElementsByTagName('head')[0], k = Y('style')
    k.setAttribute('type', 'text/css')
    k.setAttribute('media', 'screen')
    if (!(a.ie && a.win) && typeof g.createTextNode != Z) {
      k.appendChild(g.createTextNode(m + ' {' + j + '}'))
    }
    l.appendChild(k)
    if (a.ie && a.win && typeof g.styleSheets != Z && g.styleSheets.length > 0) {
      var i = g.styleSheets[g.styleSheets.length - 1]
      if (typeof i.addRule == P) {
        i.addRule(m, j)
      }
    }
  }

  function X(k, i) {
    var j = i ? 'visible' : 'hidden'
    if (S) {
      c(k).style.visibility = j
    } else {
      A('#' + k, 'visibility:' + j)
    }
  }

  return {
    registerObject: function(l, i, k) {
      if (!a.w3cdom || !l || !i) {
        return
      }
      var j = {}
      j.id = l
      j.swfVersion = i
      j.expressInstall = k ? k : false
      H[H.length] = j
      X(l, false)
    }, getObjectById: function(l) {
      var i = null
      if (a.w3cdom && S) {
        var j = c(l)
        if (j) {
          var k = j.getElementsByTagName(P)[0]
          if (!k || (k && typeof j.SetVariable != Z)) {
            i = j
          } else {
            if (typeof k.SetVariable != Z) {
              i = k
            }
          }
        }
      }
      return i
    }, embedSWF: function(n, u, r, t, j, m, k, p, s) {
      if (!a.w3cdom || !n || !u || !r || !t || !j) {
        return
      }
      r += ''
      t += ''
      if (O(j)) {
        X(u, false)
        var q = (typeof s == P) ? s : {}
        q.data = n
        q.width = r
        q.height = t
        var o = (typeof p == P) ? p : {}
        if (typeof k == P) {
          for (var l in k) {
            if (k[l] != Object.prototype[l]) {
              if (typeof o.flashvars != Z) {
                o.flashvars += '&' + l + '=' + k[l]
              } else {
                o.flashvars = l + '=' + k[l]
              }
            }
          }
        }
        J(function() {
          R(q, o, u)
          if (q.id == u) {
            X(u, true)
          }
        })
      } else {
        if (m && !C && O('6.0.65') && (a.win || a.mac)) {
          X(u, false)
          J(function() {
            var i = {}
            i.id = i.altContentId = u
            i.width = r
            i.height = t
            i.expressInstall = m
            D(i)
          })
        }
      }
    }, getFlashPlayerVersion: function() {
      return { major: a.pv[0], minor: a.pv[1], release: a.pv[2] }
    }, hasFlashPlayerVersion: O, createSWF: function(k, j, i) {
      if (a.w3cdom && S) {
        return R(k, j, i)
      } else {
        return undefined
      }
    }, createCSS: function(j, i) {
      if (a.w3cdom) {
        A(j, i)
      }
    }, addDomLoadEvent: J, addLoadEvent: M, getQueryParamValue: function(m) {
      var l = g.location.search || g.location.hash
      if (m == null) {
        return l
      }
      if (l) {
        var k = l.substring(1).split('&')
        for (var j = 0; j < k.length; j++) {
          if (k[j].substring(0, k[j].indexOf('=')) == m) {
            return k[j].substring((k[j].indexOf('=') + 1))
          }
        }
      }
      return ''
    }, expressInstallCallback: function() {
      if (C && L) {
        var i = c(K)
        if (i) {
          i.parentNode.replaceChild(L, i)
          if (T) {
            X(T, true)
            if (a.ie && a.win) {
              L.style.display = 'block'
            }
          }
          L = null
          T = null
          C = false
        }
      }
    }
  }
}()

function get_watermark_canvas(kwargs) {
  if (kwargs.waterprint_text) {
    var watermark_size = parseInt(kwargs.waterprint_size || 20),
      watermark_text = kwargs.waterprint_text.replace(/\r\n/g, '\n'), watermark_font = kwargs.waterprint_font || '宋体',
      watermark_text_items = watermark_text.split('\n'),
      watermark_height = watermark_text_items.length * watermark_size, watermark_width = 0,
      watermark_rotation = kwargs.waterprint_rotation || -20
    var text_width = 0
    var rotate_height = 0
    var textcanvas = document.createElement('canvas')
    if (!textcanvas.getContext) {
      return
    }
    var textctx = textcanvas.getContext('2d')
    textctx.font = watermark_size + 'px' + ' ' + watermark_font
    for (var row = 0; row < watermark_text_items.length; row++) {
      text_width = textctx.measureText(watermark_text_items[row]).width
      if (row == 0 && watermark_rotation < 0) {
        rotate_height = Math.tan(-watermark_rotation * Math.PI / 180) * text_width
      }
      if (text_width > watermark_width) {
        watermark_width = text_width
      }
    }
    var canvas = document.createElement('canvas')
    canvas.height = watermark_height + rotate_height
    canvas.width = watermark_width
    var ctx = canvas.getContext('2d')
    ctx.transform(1, watermark_rotation / 100, 0, 1, 0, rotate_height)
    ctx.fillStyle = kwargs.waterprint_color
    ctx.font = watermark_size + 'px' + ' ' + watermark_font
    ctx.globalAlpha = kwargs.waterprint_alpha
    for (var row = 0; row < watermark_text_items.length; row++) {
      ctx.fillText(watermark_text_items[row], 0, (row + 1) * watermark_size)
    }
    return canvas
  }
}

function onloadHandle(identify, url, serverURL, allowPrint, folderURL, ext) {
  var url = url + '&subfile='
  var frameDocument = window.frames[identify + '-frame-content'].document
  var scriptTag = frameDocument.createElement('script')
  scriptTag.src = serverURL + '/static/parent.js'
  var head = frameDocument.getElementsByTagName('head')[0]
  head.appendChild(scriptTag)
  var replaceAddress = function(el, url, serverURL) {
    if (!el.notAppend) {
      var linkTag = el.createElement('link')
      linkTag.href = serverURL + '/static/preview.css?v=2'
      linkTag.rel = 'stylesheet'
      var head = el.getElementsByTagName('head')[0]
      head.appendChild(linkTag)
      el.notAppend = true
    }
    if (!allowPrint) {
      el.oncontextmenu = function() {
        return false
      }
      el.onpaste = function() {
        return false
      }
      el.oncopy = function() {
        return false
      }
      el.oncut = function() {
        return false
      }
      el.onselectstart = function() {
        return false
      }
    }
    var trimStr = function(str) {
      return str ? str.replace(/^\s+|\s+$/g, '') : ''
    }
    var re = /^(https?|file|ftps?|mailto|javascript):|^(#|data:image\/[^;]{2,9};)|^\/{2,}/i
    var tagsName = ['link', 'a', 'img', 'frame', 'script']
    for (var t = 0; t < tagsName.length; t++) {
      var elements = el.getElementsByTagName(tagsName[t])
      for (var x = 0; x < elements.length; x++) {
        var obj = elements[x]
        if (tagsName[t] == 'a' || tagsName[t] == 'link') {
          var href = trimStr(obj.getAttribute('href', 2))
          if (re.test(href) || !href) {
            continue
          } else {
            if (folderURL) {
              obj.href = folderURL.replace(/\$path/, href)
            } else {
              obj.href = url + href
            }
          }
        } else {
          var src = trimStr(obj.getAttribute('src', 2))
          if (re.test(src) || !src) {
            continue
          } else {
            if (tagsName[t] == 'img' || tagsName[t] == 'script') {
              if (folderURL) {
                if (tagsName[t] == 'img') {
                  obj.src = folderURL + '/' + src
                } else {
                  var rep = folderURL.replace(/\$path/, src)
                  obj.src = rep
                }
              } else {
                obj.src = url + src
                if (EDOViewer.isMobile && EDOViewer.previewPatterns['flash'].test(ext)) {
                  obj.width = el.body.clientWidth
                }
              }
            } else if (tagsName[t] == 'frame') {
              obj.src = url + src + '&disposition=inline'
              var callback = function(event) {
                var frame = event.srcElement ? event.srcElement : event.target
                var frameSrc = frame.getAttribute('src', 2)
                frameSrc = frameSrc.replace(frameSrc.split('/').pop(), '')
                var frameDocument = frame.contentDocument || frame.document
                if (frame.name == 'frTabs') {
                  frameDocument.notAppend = true
                }
                replaceAddress(frameDocument, frameSrc, serverURL)
                if (frameDocument.body && frame.name != 'frTabs') {
                  frameDocument.body.innerHTML = frameDocument.body.innerHTML.replace(/&lt;!\[endif\]&gt;/g, '')
                }
              }
              if (window.attachEvent) {
                obj.attachEvent('onload', callback)
              } else {
                obj.addEventListener('load', callback)
              }
            }
          }
        }
      }
    }
  }
  replaceAddress(frameDocument, url, serverURL)
  var childNodes = frameDocument.body.childNodes
  if (childNodes.length > 1) {
    if (!-[1]) {
      var pagesNode = childNodes[0]
    } else {
      var pagesNode = childNodes[1]
    }
    if (pagesNode.tagName == 'CENTER' && /[First pageBackContinueLast page]/.test(pagesNode.innerHTML)) {
      var aList = pagesNode.getElementsByTagName('a')
      for (var x = 0; x < aList.length; x++) {
        aList[x].setAttribute('style', 'padding: 0px 6px;background: #e6f2fe;color: #333;text-decoration: none;border: 1px solid #5d9cdf;')
      }
      var pagesHTML = pagesNode.innerHTML
      pagesHTML = pagesHTML.replace('First page', '首页').replace('Back', '上页').replace('Continue', '下页').replace('Last page', '尾页').replace('Text', '文本').replace('Graphics', '图片')
      pagesNode.innerHTML = pagesHTML
    }
  }
  if (frameDocument.getElementsByTagName('frame').length == 0 && frameDocument.getElementById('page-container') == null) {
  }
}

function render_html_viewer(url, identify, kwargs) {
  var callback = kwargs.callback
  if (!callback) {
    ajaxRequest(0, url, 'html', identify, kwargs, 'HEAD')
    return
  }
  var height = kwargs.height || 490
  var allowPrint = eval(kwargs.allow_print) == false ? false : true
  var serverURL = kwargs.server_url
  var waterprintText = kwargs.waterprint_text || ''
  var name = identify + '-frame-content'
  var ext = kwargs.ext
  var folderURL = kwargs.folder_url
  var createFrame = function(data) {
    var $iframe = $('<iframe>').attr('name', name).attr('id', name).attr('height', height - 25).css({
      width: '99%',
      border: '1px solid #c3c3c3'
    }).on('load', function() {
      $iframe.contents().find('body').html(data)
      onloadHandle(identify, url, serverURL, allowPrint, folderURL, ext)
    })
    $('#' + identify).html($iframe)
    var canvas = get_watermark_canvas(kwargs)
    if (canvas) {
      $iframe.css('background-image', 'url(' + canvas.toDataURL('image/png') + ')')
    }
  }
  if ($.browser.msie && window.XDomainRequest) {
    if (window.XDomainRequest) {
      var xdr = new XDomainRequest
      var query = url
      if (xdr) {
        xdr.onload = function() {
          createFrame(xdr.responseText)
        }
        xdr.open('GET', query, true)
        xdr.send()
      }
    }
  } else {
    $.get(url, function(data) {
      createFrame(data)
    })
  }
}

function render_box_viewer(url, identify, kwargs) {
  var callback = kwargs.callback
  url = url.replace(/(mime=)application\/x-shockwave-flash-x/, '$1text/x-paged-html')
  var serverURL = kwargs.server_url
  var allowCopy = eval(kwargs.allow_copy) == false ? false : true
  var splitList = url.split('?')
  var url = splitList[0]
  var queryParams = splitList[1]
  var height = kwargs.height || 537
  var lang = EDOViewer.langs[kwargs.lang]
  var pages = kwargs.pages
  var hasPages = $.isArray(pages) && pages.length > 0
  var obj = $('#' + identify).attr('id', identify).css({ height: height, overflow: 'auto', width: '100%' })
  var viewer = Crocodoc.createViewer(obj, {
    url: url,
    enableTextSelection: true,
    plugins: { loadPage: {}, print: {} },
    viewPages: hasPages ? pages : [],
    autoloadFirstPage: false,
    queryParams: queryParams,
    enableDragging: false,
    conversionIsComplete: false
  })
  obj.on('highlight', function(event, data) {
    var $pg = obj.find('.crocodoc-page-content').eq(data.page - 1)
    if ($pg.is(':visible')) {
      $pg.unmark({ element: 'span' })
      $pg.mark(data.text, { element: 'span', className: 'highlight', acrossElements: true })
    }
  })
  var $input = $('<input type="text" style="height: inherit;width: 55px;text-align: center;padding: 5px;line-height: 15px;">')
  viewer.on('ready', function(event) {
    var controls = $('<div class="preview-controls preview-actions"></div>')
    var allowPrint = eval(kwargs.allow_print) == false ? false : true
    if (allowPrint && !EDOViewer.isMobile) {
      controls.append($('<img class="action" id="' + identify + '-print" src="' + serverURL + '/static/print2.png" title="' + lang['print'] + '">').data('pages', event.data.numPages).click(function() {
        viewer.print()
      }))
    }
    controls.append($('<img class="action" src="' + serverURL + '/static/zoom-in2.png" title="' + lang['zoom_in'] + '">').click(function() {
      viewer.destroyPage($input.data('page'))
      viewer.zoom(Crocodoc.ZOOM_IN)
    }))
    controls.append($('<img class="action" src="' + serverURL + '/static/zoom-out2.png" title="' + lang['zoom_out'] + '">').click(function() {
      viewer.zoom(Crocodoc.ZOOM_OUT)
    }))
    controls.append($('<img class="action" src="' + serverURL + '/static/prev2.png" title="' + lang['prev'] + '">').click(function() {
      viewer.scrollTo(Crocodoc.SCROLL_PREVIOUS, pages)
    }))
    controls.append($input.data({
      page: event.data.page,
      numPages: event.data.numPages
    }).val(event.data.page + ' / ' + event.data.numPages).focus(function() {
      $(this).val('')
    }).blur(function() {
      var data = $(this).data()
      $(this).val(data.page + ' / ' + data.numPages)
    }).keyup(function() {
      var $ths = $(this)
      var $val = $ths.val()
      if (!/^([1-9]\d*)$/.test($ths.val())) {
        return
      } else {
        window.setTimeout(function() {
          if ($val == $ths.val()) {
            viewer.scrollTo($ths.val())
            $ths.val('')
          }
        }, 250)
      }
    }))
    controls.append($('<img class="action" src="' + serverURL + '/static/next2.png" title="' + lang['next'] + '">').click(function() {
      viewer.scrollTo(Crocodoc.SCROLL_NEXT, pages)
    }))
    viewer.setLayout(kwargs.layout)
    if (obj.width() <= 767) {
      viewer.zoom(Crocodoc.ZOOM_AUTO)
    } else {
      var firstPage = obj.find('.crocodoc-page:first')
      if (firstPage.height() > firstPage.width()) {
        viewer.zoom(Crocodoc.ZOOM_FIT_WIDTH)
      } else {
        viewer.zoom(Crocodoc.ZOOM_AUTO)
      }
    }
    var bookmark = $(obj).data('bookmark')
    if (window.localStorage && bookmark) {
      try {
        var pageData = JSON.parse(window.localStorage.getItem(bookmark))
        var page = pageData.page
        if (page > 1 && page < event.data.numPages) {
          var message = $('<span>' + lang['last_view_page'].replace(/{page}/, page) + '  ' + '<a href="#" onclick="Crocodoc.getViewer(Crocodoc.getViewerCount()).scrollTo(' + page + ');' + '$(this).closest(\'.jquery-message\').hide();return false;">' + lang['continue_view'] + '</a></span>')
          $().message(message.prop('outerHTML'), 'info')
        }
      } catch (ex) {
      }
    }
    controls.append('<img class="action" src="' + serverURL + '/static/search2.png" style="vertical-align: middle;display:none;" ' + 'id="' + identify + '-h5search" onclick="$(this).hide().next().show().find(&quot;input&quot;).focus();" />' + '<span style="display: none;margin-left: 10px;">' + '<input style="width: 120px;padding: 0 3px;" class="search-input" />' + '<a href="javascript:;" style="margin-left: -30px;" ' + 'onclick="$(this).parent().hide();$(this).parent().prev().show();">' + '<img src="' + serverURL + '/static/close.png" style="vertical-align: middle;" /></a></span>')
    controls.append($('<img class="action" src="' + serverURL + '/static/rotate2.png" title="' + lang['rotate'] + '">').click(function() {
      var $pg = obj.find('.crocodoc-page-content').eq($input.data('page') - 1)
      $pg.find('img').rotate({ angle: 90, direction: true, scale: true, speed: .5 })
    }))
    var oldCSS = {
      position: obj.css('position'),
      height: obj.css('height'),
      top: obj.css('top'),
      left: obj.css('left'),
      'z-index': obj.css('z-index')
    }
    var screen = $('<img class="action" src="' + serverURL + '/static/fullscreen2.png" title="' + lang['fullscreen'] + '">').click(function() {
      if (!obj.data('isFullScreen')) {
        $('body').css('overflow', 'hidden')
        obj.css({
          position: 'fixed',
          height: $(window).height(),
          top: 0,
          left: 0,
          'z-index': 3e3
        }).data('isFullScreen', true)
        screen.attr('src', serverURL + '/static/cancelfullscreen2.png').attr('title', lang['back'])
      } else {
        $('body').css('overflow', 'visible')
        obj.css(oldCSS).data('isFullScreen', false)
        screen.attr('src', serverURL + '/static/fullscreen2.png').attr('title', lang['fullscreen'])
      }
    })
    $(document).keyup(function(e) {
      if (e.keyCode === 27) {
        if (obj.data('isFullScreen')) {
          screen.trigger('click')
          e.stopPropagation()
        }
      }
    })
    controls.append(screen)
    if (event.data.links && event.data.links.length > 0) {
      var menu = $('<img src="' + serverURL + '/static/menu2.png" title="' + lang['menu'] + '">')
      menu.css({ position: 'absolute', top: '4px', left: '0' })
      menu.on('click', function() {
        var $menu = $('#' + identify + '-menu')
        if ($menu.is(':visible')) {
          $menu.hide()
        } else {
          $menu.show()
        }
      })
      obj.find('.crocodoc-viewport').on('click', function() {
        $('#' + identify + '-menu').hide()
      })
      var getMenu = function(items, sub) {
        if (sub) {
          var $menu = $('<ul style="padding-left: 20px;">')
        } else {
          var $menu = $('<ul class="" id="' + identify + '-menu">')
        }
        $.each(items, function() {
          if ($.isArray(this)) {
            $menu.append(getMenu(this, true))
          } else {
            var page = this.page
            var link = $('<a href="#">' + this.title + '</a>').on('click', function() {
              viewer.scrollTo(page)
              return false
            })
            $menu.append($('<li>').append(link))
          }
        })
        return $menu
      }
      controls.append(menu)
      controls.append(getMenu(event.data.links).css({
        display: 'none',
        height: obj.height() / 1.5,
        overflow: 'auto',
        width: obj.width() / 2,
        position: 'absolute',
        top: 44,
        'z-index': 1,
        'text-align': 'left',
        left: 12,
        padding: 10,
        'box-shadow': '1px 1px 1px #9e9e9e',
        'background-color': 'rgba(255,255,255,0.8)'
      }))
    }
    obj.prepend($('<div class="preview-controls-wrapper"></div>').html(controls))
    if (obj.find('.preview-actions').is(':visible')) {
      obj.find('.crocodoc-viewport').css('top', 44)
    }
    var viewerCount = Crocodoc.getViewerCount()
    if (viewerCount > 1) {
      Crocodoc.getViewer(viewerCount - 1).destroyCSS()
    }
    obj.on('destroyed', function() {
      document.onselectstart = null
      viewer.destroy()
      if (viewerCount - 1 > 0) {
        Crocodoc.getViewer(viewerCount - 1).restoreCSS()
      }
    })
    obj.trigger('page-initial', { viewer: 'h5', pages: event.data.numPages })
    if (hasPages && $.inArray(1, pages) == -1) {
      viewer.scrollTo(Crocodoc.SCROLL_NEXT, pages)
    }
    if (!allowCopy) {
      document.onselectstart = function() {
        return false
      }
    }
  })
  var canvas = get_watermark_canvas(kwargs)
  viewer.on('pageload', function(event) {
    var $pg = obj.find('.crocodoc-page-content').eq(event.data.page - 1)
    var $pc = $pg.find('.pc')
    if (!allowCopy) {
      $pg.addClass('crocodoc-page-no-select').bind('contextmenu', function() {
        return false
      })
    }
    var reloadCSS = function() {
      if ($pc.is(':hidden')) {
        $pg.addClass('crocodoc-page-loading')
        viewer.reloadCSS(function() {
          if ($pc.is(':hidden')) {
            window.setTimeout(reloadCSS, 1e3)
          }
        })
      } else {
        viewer.reloadCSS(function(tmp) {
          if (tmp) {
            window.setTimeout(reloadCSS, 2e3)
          }
        })
      }
    }
    reloadCSS()
    if (canvas) {
      var $wm = $('<div style="width:100%;height:100%;position:relative;"></div>')
      $wm.css('background-image', 'url(' + canvas.toDataURL('image/png') + ')')
      var $bg = $pc.find('img')
      if ($bg.length > 0) {
        $bg.after($wm)
      } else {
        $pc.prepend($wm)
      }
    }
    $pg.on('click', { page: event.data.page }, function(event) {
      var ths = $(this), pageX = event.pageX - ths.offset().left, pageY = event.pageY - ths.offset().top
      Viewer(identify).trigger('position', {
        page_number: event.data.page,
        x_persent: Math.round(pageX / ths.width() * 1e4) / 100,
        y_persent: Math.round(pageY / ths.height() * 1e4) / 100
      })
    })
    var searchInput = $('#' + identify + ' .search-input:visible')
    if (searchInput.is(':visible') && searchInput.val()) {
      obj.trigger('highlight', { page: event.data.page, text: searchInput.val() })
    }
    $pg.find('a[href^="#pf"]').on('click', function(event) {
      event.preventDefault()
      var page = $(this).data('dest-detail')[0]
      viewer.scrollTo(page)
    })
  })
  viewer.on('pagefocus', function(event) {
    var page = event.data.page
    var numPages = event.data.numPages
    $input.data('page', event.data.page).val(page + ' / ' + numPages)
    if (!hasPages || $.inArray(page, pages) != -1) {
      viewer.loadPage(page)
    } else {
      obj.find('.crocodoc-page').eq(page - 1).find('.crocodoc-page-content').css('background', '#FFF')
    }
    if (page + 2 <= numPages) {
      if (!hasPages || $.inArray(page + 1, pages) != -1) {
        window.setTimeout(function() {
          viewer.loadPage(page + 1)
        }, 10)
      } else {
        obj.find('.crocodoc-page').eq(page).find('.crocodoc-page-content').css('background', '#FFF')
      }
      if (!hasPages || $.inArray(page + 2, pages) != -1) {
        window.setTimeout(function() {
          viewer.loadPage(page + 2)
        }, 20)
      } else {
        obj.find('.crocodoc-page').eq(page + 1).find('.crocodoc-page-content').css('background', '#FFF')
      }
    }
    var searchInput = $('#' + identify + ' .search-input:visible')
    if (searchInput.is(':visible') && searchInput.val()) {
      obj.trigger('highlight', { page: event.data.page, text: searchInput.val() })
    }
  })
  viewer.on('scrollend', function(event) {
    var bookmark = $(obj).data('bookmark')
    var page = $input.data('page')
    if (window.localStorage && bookmark) {
      if (page == 0) {
        window.localStorage.removeItem(bookmark)
      } else {
        window.localStorage.setItem(bookmark, JSON.stringify({ page: $input.data('page'), timestamp: $.now() }))
      }
    }
    for (var p = 1; p < page; p++) {
      if (!hasPages || $.inArray(p, pages) != -1) {
        viewer.loadPage(p)
      } else {
        obj.find('.crocodoc-page').eq(p - 1).find('.crocodoc-page-content').css('background', '#FFF')
      }
    }
  })
  viewer.load()
  viewer.on('asseterror', function(event) {
    window.setTimeout(function() {
      viewer.load()
    }, 1e3)
  })
}

function render_flash_viewer(url, identify, kwargs) {
  var width = kwargs.width || 700
  var height = kwargs.height || 537
  var allowPrint = eval(kwargs.allow_print) == false ? false : true
  var allowCopy = eval(kwargs.allow_copy) == false ? false : true
  var serverURL = kwargs.server_url
  var bgcolor = kwargs.bgcolor || '#f4f4f4'
  var lang = EDOViewer.langs[kwargs.lang]
  var flashURL = kwargs.flash_url || 'https://get.adobe.com/cn/flashplayer'
  var pages = kwargs.pages
  var hasPages = $.isArray(pages) && pages.length > 0
  var node = document.getElementById(identify)
  var cloneNode = node.cloneNode()
  var flashID = identify + '-flash'
  var actions_width = /\d$/.test(width) ? width + 'px' : width
  var $actions = $('<div class="flash-actions preview-actions" id="' + identify + '-topbar"></div>').css({
    display: 'none',
    width: actions_width,
    'background-color': bgcolor
  }).attr('width', actions_width)
  if (node.style.textAlign = 'center') {
    $actions.css('margin', '0 auto')
  }
  if (allowPrint) {
    var printAction = '<a class="action" href="javascript:;" title="' + lang['print'] + '" ' + 'id="' + identify + '-print"><img src="' + serverURL + '/static/print.png" /></a>'
  } else {
    var printAction = ''
  }
  var obj = $(node)
  var screen = $('<a class="action" href="javascript:;"><img src="' + serverURL + '/static/fullscreen.png" title="' + lang['fullscreen'] + '"></a>').click(function() {
    if (!obj.data('isFullScreen')) {
      $('body').css('overflow', 'hidden')
      obj.css({
        position: 'fixed',
        height: $(window).height(),
        width: $(window).width(),
        top: 0,
        left: 0,
        'z-index': 3e3
      }).addClass('crocodoc-viewer').data('isFullScreen', true)
      screen.find('img').attr('src', serverURL + '/static/cancelfullscreen.png').attr('title', lang['back'])
      $actions.css('width', '100%')
      $('#' + flashID).css({ width: '100%', height: $(window).height() - $actions.outerHeight() })
    } else {
      $('body').css('overflow', 'visible')
      obj.css({
        position: 'static',
        height: 'auto',
        width: 'auto',
        top: 'auto',
        left: 'auto',
        'z-index': 'auto'
      }).removeClass('crocodoc-viewer').data('isFullScreen', false)
      $actions.css('width', width)
      $('#' + flashID).css({ width: width, height: height })
      screen.find('img').attr('src', serverURL + '/static/fullscreen.png').attr('title', lang['fullscreen'])
    }
  })
  $(document).keyup(function(e) {
    if (e.keyCode === 27) {
      if (obj.data('isFullScreen')) {
        screen.trigger('click')
        e.stopPropagation()
      }
    }
  })
  $actions.append('<a href="javascript:;" title="' + lang['prev'] + '" ' + 'onclick="Viewer(&quot;' + identify + '&quot;).prev();">' + '<img src="' + serverURL + '/static/prev.png" /></a>' + '<input onfocus="this.value=&quot;&quot;;" onblur="this.value=this.getAttribute(&quot;text&quot;)" ' + 'onkeyup="var ths=this;var val=this.value;' + 'window.setTimeout(function(){' + 'if(val == ths.value){Viewer(&quot;' + identify + '&quot;).scrollTo(ths.value);}' + '}, 250);" ' + 'id="' + identify + '-page" type="text" class="page-input" />' + '<a href="javascript:;" title="' + lang['next'] + '" ' + 'onclick="Viewer(&quot;' + identify + '&quot;).next();">' + '<img src="' + serverURL + '/static/next.png" /></a>').append(printAction + '<a class="action" href="javascript:;" title="' + lang['zoom_in'] + '" ' + 'onclick="Viewer(&quot;' + identify + '&quot;).zoomIn();">' + '<img src="' + serverURL + '/static/zoom-in.png" /></a>' + '<a class="action" href="javascript:;" title="' + lang['zoom_out'] + '" ' + 'onclick="Viewer(&quot;' + identify + '&quot;).zoomOut();">' + '<img src="' + serverURL + '/static/zoom-out.png" /></a>').append('<a class="action hide" href="javascript:;" id="' + identify + '-search" ' + 'onclick="$(this).hide().next().show().find(&quot;input&quot;).focus();">' + '<img src="' + serverURL + '/static/search.png"  /></a>' + '<span class="action hide"><input class="search-input" id="' + identify + '-search-input" />' + '<a class="search-input-close" href="javascript:;" ' + 'onclick="$(this).parent().hide();$(this).parent().prev().show();">' + '<img src="' + serverURL + '/static/close.png"  /></a></span>' + '<a class="action" href="javascript:;" title="' + lang['rotate'] + '" ' + 'onclick="Viewer(&quot;' + identify + '&quot;).rotate();">' + '<img src="' + serverURL + '/static/rotate.png" /></a>').append(screen)
  var menu = $('<a class="menu" href="javascript:;" title="' + lang['menu'] + '"></a>')
  menu.append('<img src="' + serverURL + '/static/menu.png" />')
  menu.on('click', function() {
    var $menu = $('#' + identify + '-menu')
    if ($menu.length > 0) {
      if ($menu.is(':visible')) {
        $menu.hide()
      } else {
        $menu.show()
      }
    } else {
      $actions.append(getMenu([]).append('<li>' + lang['loading'] + '</li>'))
      var jsonURL = decodeURIComponent(url).replace('&mime=application/x-shockwave-flash-x', '&mime=application/json')
      var successFunc = function(data) {
        if (data.links.length == 0) {
          $('#' + identify + '-menu').html(lang['no_menu_info'])
        } else {
          $('#' + identify + '-menu').replaceWith(getMenu(data.links))
        }
      }
      var errorCount = 0
      var failFunc = function() {
        var $menu = $('#' + identify + '-menu')
        if ($('#' + identify + '-menu').length > 0 && errorCount < EDOViewer.retryCount) {
          window.setTimeout(function() {
            errorCount++
            getJSON()
          }, EDOViewer.intervalSecond * 1e3)
        }
      }
      var getJSON = function() {
        if ($.browser.msie && window.XDomainRequest) {
          var xdr = new XDomainRequest
          if (xdr) {
            xdr.onload = function() {
              var data = xdr.responseText ? JSON.parse(xdr.responseText) : {}
              successFunc(data)
            }
            xdr.onerror = function() {
              failFunc()
            }
            xdr.open('GET', jsonURL, true)
            xdr.send()
          }
        } else {
          $.ajax({
            type: 'GET', dataType: 'json', url: jsonURL, success: function(data) {
              successFunc(data)
            }, error: function() {
              failFunc()
            }
          })
        }
      }
      getJSON()
    }
    return false
  })
  var getMenu = function(items, sub) {
    if (sub) {
      var $menu = $('<ul style="padding-left: 20px;">')
    } else {
      var $menu = $('<ul class="" id="' + identify + '-menu">').css({
        height: height / 1.5,
        overflow: 'auto',
        width: $actions.width() / 2,
        position: 'absolute',
        'z-index': 1,
        'text-align': 'left',
        padding: 10,
        margin: 10,
        border: '1px solid #9e9e9e',
        'box-shadow': '0 0 1px #9e9e9e',
        'background-color': 'rgba(255,255,255,0.8)'
      })
      if (typeof document.addEventListener == 'undefined') {
        $menu.css({ 'background-color': '#fff', opacity: .8 })
      }
    }
    $.each(items, function() {
      if ($.isArray(this)) {
        $menu.append(getMenu(this, true))
      } else {
        var page = this.page
        var link = $('<a href="#">' + this.title + '</a>').on('click', function() {
          document.getElementById(flashID).scrollTo(page)
          return false
        })
        $menu.append($('<li>').append(link))
      }
    })
    return $menu
  }
  $actions.append(menu)
  node.id = identify
  $('#' + node.id).html($actions).append($(cloneNode).attr('id', flashID))
  var isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor)
  if (isChrome) {
    var iframe = '<iframe height="0" width="0" border="0" wmode="Opaque" name="' + flashID + '-iframe"></iframe>'
    document.getElementById(flashID).innerHTML = iframe + '<a href="https://get.adobe.com/flashplayer" target="' + flashID + '-iframe"><img src="' + serverURL + '/static/enable_flash.png" /></a>'
  } else {
    document.getElementById(flashID).innerHTML = '<div style="padding: 10px 0">' + lang['no_install_flash'].replace(/{flash_url}/, flashURL) + '</div>'
  }
  var flashvars = {}
  flashvars['swf_file'] = url
  flashvars['subfile'] = true
  flashvars['allow_print'] = allowPrint
  flashvars['allow_copy'] = allowCopy
  flashvars['allow_debug'] = false
  if (hasPages) {
    flashvars['pages'] = JSON.stringify(pages)
  }
  if (kwargs.waterprint_text != undefined) {
    flashvars['waterprint_text'] = kwargs.waterprint_text
    flashvars['waterprint_size'] = kwargs.waterprint_size
    flashvars['waterprint_color'] = kwargs.waterprint_color
    flashvars['waterprint_font'] = kwargs.waterprint_font
    flashvars['waterprint_x'] = kwargs.waterprint_x
    flashvars['waterprint_y'] = kwargs.waterprint_y
    flashvars['waterprint_alpha'] = kwargs.waterprint_alpha
    flashvars['waterprint_rotation'] = kwargs.waterprint_rotation
  }
  var params = {
    menu: false,
    bgcolor: bgcolor,
    allowFullScreen: true,
    allowScriptAccess: 'sameDomain',
    wmode: 'opaque'
  }
  var attributes = { name: identify }
  swfobject.embedSWF(serverURL + '/static/zviewer.swf?v=10', flashID, width, height, '9.0.45', null, flashvars, params, attributes)
  Viewer(identify).on('page-scroll', function(event, data) {
    $('#' + identify + '-page').val(data.page + ' / ' + data.total).attr('text', data.page + ' / ' + data.total).blur()
    var bookmark = $(node).data('bookmark')
    if (window.localStorage && bookmark) {
      if (data.page == 1) {
        window.localStorage.removeItem(bookmark)
      } else {
        window.localStorage.setItem(bookmark, JSON.stringify({ page: data.page, timestamp: $.now() }))
      }
    }
  })
  Viewer(identify).on('page-click', function(event, data) {
    $('#' + identify + '-menu').hide()
    $('.live-search').slideUp()
  })
  Viewer(identify).on('page-initial', function(event, data) {
    var topbar = document.getElementById(identify + '-topbar')
    topbar.style.display = ''
    for (var x = 0; x < topbar.childNodes.length; x++) {
      var child = topbar.childNodes[x]
      if (child.tagName != 'A') {
        continue
      } else {
        child.onmouseover = function() {
          this.firstChild.src = this.firstChild.getAttribute('src').replace(/(.*).png$/, '$12.png')
        }
        child.onmouseout = function() {
          this.firstChild.src = this.firstChild.getAttribute('src').replace(/(\d).png$/, '.png')
        }
      }
    }
    document.getElementById(identify + '-page').value = 1 + ' / ' + data.total
    document.getElementById(identify + '-page').setAttribute('text', 1 + ' / ' + data.total)
    $('#' + identify + '-print').data('pages', data.total).on('click', function() {
      Viewer(identify).print()
    })
    var bookmark = $(node).data('bookmark')
    if (window.localStorage && bookmark) {
      try {
        var pageData = JSON.parse(window.localStorage.getItem(bookmark))
        var page = pageData.page
        if (page > 1 && page < data.total) {
          var message = $('<span>' + lang['last_view_page'].replace(/{page}/, page) + '  ' + '<a href="#" onclick="document.getElementById(\'' + flashID + '\').scrollTo(' + page + ');' + '$(this).closest(\'.jquery-message\').hide();return false;">' + lang['continue_view'] + '</a></span>')
          $().message(message.prop('outerHTML'), 'info')
        }
      } catch (ex) {
      }
    }
    for (var x = 1; x <= 3; x++) {
      try {
        this.loadPage(x * 3)
      } catch (ex) {
      }
    }

    function thisMovie(movieName) {
      if (navigator.appName.indexOf('Microsoft') != -1) {
        return window[movieName]
      } else {
        return document[movieName]
      }
    }

    var eventSupported = function(eventName, el) {
      el = el || document.createElement('div')
      eventName = 'on' + eventName
      var isSupported = eventName in el
      if (el.setAttribute && !isSupported) {
        el.setAttribute(eventName, 'return;')
        isSupported = typeof el[eventName] === 'function'
      }
      el = null
      return isSupported
    }
    var addEvent = function(obj, type, callback) {
      if (obj.addEventListener) {
        obj.addEventListener(type, callback, { passive: false })
      } else if (obj.attachEvent) {
        obj.attachEvent('on' + type, callback)
      }
    }
    var type = eventSupported('mousewheel') ? 'mousewheel' : 'DOMMouseScroll'
    var wheel = function(obj, callback) {
      addEvent(obj, type, function(event) {
        event = event || window.event
        event.preventDefault()
        var delta = 0
        if (event.wheelDelta) {
          delta = event.wheelDelta / 120
          if (window.opera && window.opera.version() < 10) delta = -delta
        } else if (event.detail) {
          delta = -event.detail / 3
        }
        event.delta = Math.round(delta)
        callback.call(obj, event)
      })
    }
    wheel(document.getElementById(flashID), function(e) {
      thisMovie(flashID).MOUSE_WHEEL_detail(e.delta)
    })
    $('#' + identify).trigger('page-initial', { viewer: 'flash', pages: data.total })
    if (hasPages) {
      Viewer(identify).next()
    }
  })
}

function render_compress_viewer(url, identify, kwargs) {
  var callback = kwargs.callback
  if (!callback) {
    ajaxRequest(0, url, 'compress', identify, kwargs, 'GET')
    return
  }
  var height = kwargs.height
  height = /\d$/.test(height) ? height + 'px' : height
  var data = kwargs.data
  var serverURL = kwargs.server_url
  var lang = EDOViewer.langs[kwargs.lang]
  var allowPrint = eval(kwargs.allow_print) == false ? false : true
  var allowCopy = eval(kwargs.allow_copy) == false ? false : true

  function getChildHTML(children) {
    var html = '(' + children.length + lang['items'] + ')<ul style="padding-left: 25px;">' + renderHTML(children) + '</ul>'
    return html
  }

  function getDisplaySize(size) {
    if (size == 0) {
      return '0 KB'
    } else if (size <= 1024) {
      return '1 KB'
    } else if (size > 1048576) {
      return Math.round(size / 1048576 * 100) / 100 + ' MB'
    } else {
      return Math.round(size / 1024 * 100) / 100 + ' KB'
    }
  }

  function renderHTML(current) {
    var href_url = url.split('?')
    var params = href_url[1].split('&')
    var lastParams = ''
    locationParams = ''
    mimeParams = ''
    for (var x = 0; x < params.length; x++) {
      var param = params[x]
      if (!param) {
        continue
      }
      if (param.indexOf('mime=') == 0) {
        mimeParams = '&' + param
      } else if (param.indexOf('filename=') == 0) {
        continue
      } else if (param.indexOf('location=') == 0) {
        locationParams = param
      } else {
        lastParams += '&' + param
      }
    }
    var html = ''
    for (var child = 0; child < current.length; child++) {
      var children = ''
      var path = encodeURIComponent(current[child]['path'])
      if (current[child]['type'] == 'folder') {
        if (current[child]['children'].length > 0) {
          children = getChildHTML(current[child]['children'])
        }
      }
      var down_url = ''
      if (kwargs.download_signcode) {
        down_url = serverURL + '/@@download?' + locationParams + '$$$' + path + lastParams
        down_url = down_url.replace(/signcode=.*(&.*)|signcode=.*/, 'signcode=' + kwargs.download_signcode + '$1')
      } else if (kwargs.down_url) {
        var down_url_params = kwargs.down_url.split('&')
        for (var x = 1; x < down_url_params.length; x++) {
          var param = down_url_params[x]
          if (/^location=/.test(param)) {
            down_url += '&' + param + '$$$' + path
          } else if (!/^filename=/.test(param)) {
            down_url += '&' + param
          }
        }
        down_url = down_url_params[0] + down_url
      }
      if (down_url) {
        var download = '<span style="margin-left: 20px;">' + '<img src="' + serverURL + '/static/download.gif" style="vertical-align: middle;">' + '<a href=' + down_url + ' title="' + lang['download'] + '">' + lang['download'] + '</a></span>'
      } else {
        var download = ''
      }
      var size = ''
      if (typeof current[child]['size'] !== 'undefined' && current[child]['size'] !== null) {
        size = getDisplaySize(current[child]['size'])
      }
      if (current[child]['type'] == 'folder') {
        html += '<li style="list-style-type: none;">' + '<img src="' + serverURL + '/static/folder.gif" style="vertical-align: middle;">' + '<span style="margin:0 5px;">' + current[child]['name'] + '</span>' + children + size + '</li>'
      } else {
        var preview_url = kwargs.preview_url + '&zip_path=' + path
        html += '<li style="list-style-type: none;">' + '<img src="' + serverURL + '/static/file.png" style="vertical-align: middle;">' + '<a href=' + preview_url + ' target="_blank" title="' + lang['preview'] + '" style="margin: 0 5px;">' + current[child]['name'] + '</a>' + '<span style="">[' + size + ']</span>' + children + download + '</li>'
      }
    }
    return html
  }

  var html = renderHTML(data['children'])
  if (kwargs.attachment && data['children'].length > 0) {
    document.getElementById(identify).innerHTML = '<ul style="text-align: left;">' + '<li style="color: #888;">' + lang['attachment'] + '(' + data['children'].length + ')</li>' + html + '</ul>'
  } else {
    document.getElementById(identify).innerHTML = '<ul style="text-align: left;max-height: ' + height + ';overflow: auto;">' + html + '</ul>'
  }
  $('#' + identify).show()
}

function render_audio_viewer(url, identify, kwargs) {
  var width = '80%'
  var serverURL = kwargs.server_url
  var supportHTML5 = typeof Worker !== 'undefined'
  var lang = EDOViewer.langs[kwargs.lang]
  var flashURL = kwargs.flash_url || 'https://get.adobe.com/cn/flashplayer/'
  var noInstallInfo = lang['no_install_flash'].replace(/{flash_url}/, flashURL) + '</div>'
  var $obj = $('#' + identify)
  if (supportHTML5) {
    var primary = 'html5'
  } else if (!swfobject.getFlashPlayerVersion()['major']) {
    $obj.html(noInstallInfo)
    return
  } else {
    var primary = 'flash'
  }
  var $loading = $('<div></div>').hide()
  $obj.html($('<center></center>').append($('<center></div>').attr('id', identify + '-player')).append($loading))
  jwplayer.key = 'lzyNU9Lr9SFs/f1DlEawhnAfUHPFu4klHnebLA=='
  var playerInstance = jwplayer(identify + '-player').setup({
    file: url,
    type: 'mp3',
    width: width,
    height: 40,
    flashplayer: serverURL + '/static/jwplayer/jwplayer.flash.swf',
    html5player: serverURL + '/static/jwplayer/jwplayer.html5.js',
    primary: primary
  })
  playerInstance.onReady(function(event) {
    var count = 0
    playerInstance.onError(function(event) {
      if (count == 2) {
        count = 0
        $loading.show().text(event.message)
      } else {
        $loading.show().text(lang['play_fail'])
        window.setTimeout(function() {
          $loading.hide()
          playerInstance.play()
          count++
        }, 3e3)
      }
    })
    playerInstance.onPlay(function(event) {
      $(document).trigger('jwplay')
    })
    $(document).trigger('jwdestroy')
  })
}

function render_video_viewer(url, identify, kwargs) {
  var url = url
  var width = '80%'
  var height = kwargs.height || 330
  var ext = kwargs.ext
  var serverURL = kwargs.server_url
  var supportHTML5 = typeof Worker !== 'undefined'
  var type = kwargs.mode == 'flv' ? 'flv' : 'mp4'
  var lang = EDOViewer.langs[kwargs.lang]
  var flashURL = kwargs.flash_url || 'https://get.adobe.com/cn/flashplayer/'
  var noInstallInfo = lang['no_install_flash'].replace(/{flash_url}/, flashURL) + '</div>'
  var $obj = $('#' + identify)
  if (supportHTML5) {
    var primary = 'html5'
  } else if (!swfobject.getFlashPlayerVersion()['major']) {
    $obj.html(noInstallInfo)
    return
  } else {
    var primary = 'flash'
  }
  var render = function(autostart) {
    var $loading = $('<div></div>').hide()
    $obj.html($('<center></center>').append($('<div></div>').attr('id', identify + '-player')).append($loading))
    jwplayer.key = 'lzyNU9Lr9SFs/f1DlEawhnAfUHPFu4klHnebLA=='
    var playerInstance = jwplayer(identify + '-player').setup({
      file: url,
      image: kwargs.image_url || '',
      type: type,
      width: width,
      height: height,
      autostart: autostart,
      bufferlength: 5,
      flashplayer: serverURL + '/static/jwplayer/jwplayer.flash.swf',
      html5player: serverURL + '/static/jwplayer/jwplayer.html5.js',
      primary: primary
    })
    playerInstance.onReady(function(event) {
      playerInstance.onError(function(event) {
        $loading.show().text(lang['play_fail'])
        window.setTimeout(function() {
          render('autostart')
        }, 3e3)
      })
      playerInstance.onPlay(function(event) {
        $(document).trigger('jwplay')
      })
      $(document).trigger('jwdestroy')
    })
  }
  if (kwargs.source_mime == 'video/mp4' && kwargs.metadata_url && kwargs.mp4b_url) {
    var dataURL = kwargs.metadata_url
    var successFunc = function(data) {
      if (data.video_encoding == 'h264') {
        render()
      } else {
        url = kwargs.mp4b_url
        render()
      }
    }
    var failFunc = function(errorCount) {
      if ($('#' + identify).length == 0) return
      if (errorCount < EDOViewer.retryCount) {
        window.setTimeout(function() {
          getData(errorCount + 1)
        }, EDOViewer.intervalSecond * 1e3)
      } else {
        $obj.html(lang['timeout'])
      }
    }
    var getData = function(errorCount) {
      $obj.html($('<div>' + lang['converting'] + '</div>').append($('<img>').attr('src', serverURL + '/static/waiting.gif')))
      if ($.browser.msie && window.XDomainRequest) {
        if (window.XDomainRequest) {
          var xdr = new XDomainRequest
          if (xdr) {
            xdr.onload = function() {
              var data = xdr.responseText ? JSON.parse(xdr.responseText) : {}
              successFunc(data)
            }
            xdr.onerror = function() {
              failFunc(errorCount)
            }
            xdr.open('GET', dataURL, true)
            xdr.send()
          }
        }
      } else {
        $.ajax({
          type: 'GET', url: dataURL, dataType: 'json', success: function(data) {
            successFunc(data)
          }, error: function() {
            failFunc(errorCount)
          }
        })
      }
    }
    var errorCount = 0
    getData(errorCount)
  } else {
    render()
  }
}

function render_image_viewer(url, identify, kwargs) {
  var callback = kwargs.callback
  var src = url
  var serverURL = kwargs.server_url
  var lang = EDOViewer.langs[kwargs.lang]
  var allowPrint = eval(kwargs.allow_print) == false ? false : true
  if (kwargs.download_signcode || kwargs.down_url) {
    if (kwargs.download_signcode) {
      var download = url.replace(/(signcode=).*/, '$1' + kwargs.download_signcode)
    } else {
      var download = kwargs.down_url
    }
    if (kwargs.filesize < 300 * 1024) {
      src = download
    }
  } else {
    var download = ''
  }
  var $obj = $('#' + identify)
  var $image = $('<img>').attr('src', src).css({ transition: 'all 0.5s ease-in-out 0s' })
  var $loading = $('<div>' + lang['converting'] + '</div>').append($('<img>').attr('src', serverURL + '/static/waiting.gif'))
  $obj.html($image).append($loading).css({
    width: '100%',
    'background-color': '#FFF',
    margin: '0 auto'
  }).css('width', $obj.width()).data('width', $obj.width())
  var errorCount = 0
  $image.bind('error', function() {
    $image.hide()
    $loading.show()
    errorCount += 1
    if (errorCount < EDOViewer.retryCount) {
      window.setTimeout(function() {
        $image.show().attr('src', src + '&_=' + $.now())
      }, 1e3 * EDOViewer.intervalSecond)
    } else {
      $loading.html(lang['timeout'])
    }
  })
  $image.bind('contextmenu', function() {
    return false
  })
  $image.load(function() {
    $loading.hide()
    var $viewer = $('<div></div>').css({
      position: 'relative',
      overflow: 'auto',
      margin: '0 auto',
      width: '100%',
      height: kwargs.height,
      textAlign: 'center',
      border: '1px solid #c3c3c3'
    }).append($image)
    var $rotate = $('<a href="javascript:;" class="action"></a>').prepend($('<img>').attr('src', serverURL + '/static/rotation.gif').attr('title', lang['rotate'])).css({
      position: 'relative',
      padding: '0 10px',
      'z-index': 1
    }).on('click', function(event) {
      $image.rotate({ angle: 90, direction: true, speed: .5 })
      return false
    })
    var $print = $('<a href="javascript:;" class="action"></a>').prepend($('<img>').attr('src', serverURL + '/static/print.gif').attr('title', lang['print'])).css({
      position: 'relative',
      padding: '0 10px',
      'z-index': 1
    }).on('click', function(event) {
      window.open(serverURL + '/fullscreenview.html?src=' + encodeURIComponent(src) + '&action=print')
      return false
    })
    var $zoomOut = $('<a href="javascript:;" class="action"></a>').prepend($('<img>').attr('src', serverURL + '/static/zoom-out.gif').attr('title', lang['zoom_out'])).css({
      position: 'relative',
      padding: '0 10px',
      'z-index': 1
    }).on('click', function(event) {
      var scale = 150 / 100
      var pos = $image.offset()
      var clickX = event.pageX - pos.left
      var clickY = event.pageY - pos.top
      $viewer.css({ width: $viewer.width(), height: $viewer.height() })
      $image.css({ width: $image.width() / scale, height: $image.height() / scale })
      return false
    })
    var $zoomIn = $('<a href="javascript:;" class="action"></a>').prepend($('<img>').attr('src', serverURL + '/static/zoom-in.gif').attr('title', lang['zoom_in'])).css({
      position: 'relative',
      padding: '0 10px',
      'z-index': 1
    }).on('click', function(event) {
      var scale = 150 / 100
      var pos = $image.offset()
      var clickX = event.pageX - pos.left
      var clickY = event.pageY - pos.top
      $viewer.css({ width: $viewer.width(), height: $viewer.height() })
      $image.css({ width: $image.width() * scale, height: $image.height() * scale })
      return false
    })
    var $actions = $('<div class="preview-actions">').css({
      padding: '5px 0',
      'text-align': 'center',
      width: '100%',
      margin: '0 auto'
    })
    if (typeof Worker !== 'undefined') {
      $actions.append($rotate)
    }
    if (allowPrint) {
      $actions.append($print)
    }
    $actions.append($zoomOut)
    $actions.append($zoomIn)
    var $origin = $('<a href="javascript:;" class="action"></a>').prepend($('<img>').attr('src', serverURL + '/static/originlabel.gif').attr('title', lang['origin'])).css({
      position: 'relative',
      padding: '0 10px',
      'z-index': 1
    }).on('click', function(event) {
      window.open(serverURL + '/fullscreenview.html?src=' + encodeURIComponent(download))
    })
    if (/^.(jpg|jpeg|png|gif|bmp)$/i.test(kwargs.ext) && download) {
      $actions.append($origin)
    }
    var $screen = $('<a href="javascript:;" class="action"></a>').prepend($('<img>').attr('src', serverURL + '/static/fullscreen.png').attr('title', lang['fullscreen'])).css({
      position: 'relative',
      padding: '0 10px',
      'z-index': 1
    }).click(function() {
      if (!$obj.data('isFullScreen')) {
        $('body').css('overflow', 'hidden')
        $obj.css({
          position: 'fixed',
          height: $(window).height(),
          width: '100%',
          top: 0,
          left: 0,
          'z-index': 3e3
        }).addClass('crocodoc-viewer').data('isFullScreen', true)
        $screen.find('img').attr('src', serverURL + '/static/cancelfullscreen.png').attr('title', lang['back'])
        $viewer.css({ width: '100%', height: $(window).height() - $actions.outerHeight() })
      } else {
        $('body').css('overflow', 'visible')
        $obj.css({
          position: 'static',
          width: $obj.data('width'),
          height: 'auto'
        }).removeClass('crocodoc-viewer').data('isFullScreen', false)
        $screen.find('img').attr('src', serverURL + '/static/fullscreen.png').attr('title', lang['fullscreen'])
        $viewer.css({ height: kwargs.height, width: '100%' })
      }
      return false
    })
    $(document).keyup(function(e) {
      if (e.keyCode === 27) {
        if ($obj.data('isFullScreen')) {
          $screen.trigger('click')
          e.stopPropagation()
        }
      }
    })
    $actions.append($screen)
    $obj.html($viewer).prepend($actions)
    var canvas = document.createElement('canvas')
    if (kwargs.waterprint_text && !!(canvas.getContext && canvas.getContext('2d'))) {
      $viewer.append(canvas)
      canvas.width = $image[0].naturalWidth
      canvas.height = $image[0].naturalHeight
      var ctx = canvas.getContext('2d')
      var watermark_canvas = get_watermark_canvas(kwargs)
      ctx.drawImage($image.get(0), 0, 0)
      var pat = ctx.createPattern(watermark_canvas, 'repeat')
      ctx.rect(0, 0, canvas.width, canvas.height)
      ctx.fillStyle = pat
      ctx.fill()
      $image.remove()
      $image = $(canvas)
      $image.bind('contextmenu', function() {
        return false
      })
    }
    var clicked = false, clickY, clickX
    $image.on({
      mousemove: function(e) {
        clicked && updateScrollPos(e)
      }, mousedown: function(e) {
        clicked = true
        clickY = e.pageY
        clickX = e.pageX
      }, mouseup: function() {
        clicked = false
      }
    }).css('cursor', 'move')
    var updateScrollPos = function(e) {
      $viewer.scrollTop($viewer.scrollTop() + (clickY - e.pageY))
      $viewer.scrollLeft($viewer.scrollLeft() + (clickX - e.pageX))
    }
    $obj.trigger('page-initial', { viewer: 'image' })
  })
}

function callViewerEvent(type, objectID, data) {
  Viewer(objectID.replace(/-flash$/, '')).trigger(type, data)
}

var Viewer = function(identify) {
  var obj = document.getElementById(identify)
  var flash_obj = document.getElementById(identify + '-flash')
  var on = function(type, func) {
    var handler = function(event, data) {
      event.data = data
      func.apply(obj, [event, data])
    }
    $('html').off(type + identify).on(type + identify, $(obj), handler)
  }
  var trigger = function(type, data) {
    $(obj).trigger(type + identify, data)
  }
  var print = function(name) {
    if (confirm('确认要打印吗？')) {
      if (name) {
        var src = document.getElementById(name).src
        document.getElementById(name).src = src + '#print'
        window.setTimeout(function() {
          document.getElementById(name).src = src + '#'
        }, 1500)
      } else {
        flash_obj.print()
      }
      Viewer(identify).trigger('print')
    }
  }
  return {
    on: on, trigger: trigger, zoomIn: function() {
      flash_obj.zoomIn()
    }, zoomOut: function() {
      flash_obj.zoomOut()
    }, heightFit: function() {
      flash_obj.heightFit()
    }, widthFit: function() {
      flash_obj.widthFit()
    }, print: function(name) {
      print(name)
    }, setMode: function(mode) {
      flash_obj.setMode(mode)
    }, scrollTo: function(page) {
      flash_obj.scrollTo(page)
    }, prev: function() {
      flash_obj.prev()
    }, next: function() {
      flash_obj.next()
    }, rotate: function() {
      flash_obj.rotate()
    }
  }
}

var EDOViewer = {
  previewPatterns: {
    flash: /^(.doc|.docm|.docx|.dot|.dotx|.pot|.potm|.ppt|.pps|.pptx|.mpp|.ppsx|.pdf|.rtf|.wps|.wpt|.et|.ett|.dps|.dpt|.odt|.odp|.ott|.ots|.otp|.vsd|.vsdx|.psd|.vss|.vst|.dwg|.dwt|.dxf|.cdr|.wmf|.key|.pages|.numbers|.tif|.tiff|.xmind|.xlt|.xltx|.emf|.wpsx|.etx|.eps)$/i,
    html: /^(.json|.bat|.md|.eml|.xls|.xlsx|.xlsm|.mht|.html|.htm|.txt|.rst|.xml|.css|.csv|.java|.c|.cpp|.jsp|.asp|.php|.py|.as|.sh|.ini|.yaml|.dart|.js|.go|.coffee|.log|.ods|.dot|.fodt|.uot|.odg|.otg|.fodg|.fodp|.uop|.fods|.xlt|.xmind|.slk|.msg|.chm)$/i,
    compress: /^(.rar|.zip|.tar|.tgz|.gz|.7z)$/i,
    audio: /^(.mp3|.wma|.wav|.mid|.ogg|.m4a)$/i,
    video: /^(.avi|.rmvb|.rmvb|.mov|.mp4|.swf|.flv|.mpg|.ram|.wmv|.m4v|.3gp|.vob|.dat|.rm|.mts)$/i,
    image: /^(.png|.gif|.jpg|.jpeg|.bmp|.ppm|.ico)$/i
  },
  retryCount: 200,
  intervalSecond: 3,
  langs: {
    zh: {
      loading: '加载中请稍候',
      converting: '转换中请稍候',
      timeout: '转换超时，请刷新后重试',
      menu: '目录',
      no_menu_info: '没有目录信息',
      print: '打印',
      rotate: '旋转',
      origin: '原图',
      fullscreen: '全屏',
      back: '还原',
      no_support_preview: '该文件不支持预览！',
      zoom_in: '放大',
      zoom_out: '缩小',
      prev: '上一页',
      next: '下一页',
      fit_width: '合适宽度',
      fit_height: '合适高度',
      no_install_flash: '您的浏览器未安装Flash Player，请 <a href="{flash_url}" target="_blank">安装</a> 后再刷新页面查看',
      items: '项',
      download: '下载',
      preview: '预览',
      attachment: '附件',
      play_fail: '播放失败，文件正在加载，请稍候',
      last_view_page: '上次查看到第 {page} 页',
      continue_view: '继续查看'
    },
    en: {
      loading: 'Loading',
      converting: 'Converting',
      timeout: 'Conversion timeout',
      menu: 'Menu',
      no_menu_info: 'No menu info',
      print: 'Print',
      rotate: 'Rotate',
      origin: 'Origin',
      fullscreen: 'Fullscreen',
      back: 'Back',
      no_support_preview: 'This file does not support preview!',
      zoom_in: 'Zoom in',
      zoom_out: 'Zoom out',
      prev: 'Prev',
      next: 'Next',
      fit_width: 'Fit width',
      fit_height: 'Fit height',
      no_install_flash: 'Flash Player is not installed in your browser, please <a href="{flash_url}" target="_blank">install</a> and then refresh the page view',
      items: ' items',
      download: 'Download',
      preview: 'Preview',
      attachment: 'Attachment',
      play_fail: 'Failed to play, the file is loading, please wait',
      last_view_page: 'You last read at page {page}',
      continue_view: 'continue view'
    }
  },
  isMobile: /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(navigator.userAgent),
  flashVersion: swfobject.getFlashPlayerVersion()['major']
}

function tipsFunc(serverURL, type, info, lang) {
  if (info == undefined) {
    var lang = EDOViewer.langs[lang]
    if (type == 'loading') {
      return lang[type] + ' <img src="' + serverURL + '/static/waiting.gif">'
    } else if (type == 'converting') {
      return lang[type] + ' <img src="' + serverURL + '/static/waiting.gif">'
    } else {
      return lang['timeout']
    }
  } else {
    var re = /^function\s?(.*)/
    if (info instanceof Function) {
      return info()
    } else if (re.test(info)) {
      var func = info.match(re)[1]
      if (func) {
        return eval('info=' + info + ';info();')
      } else {
        return info
      }
    } else {
      return info
    }
  }
}

function statusFunc(xmlHttp, kwargs) {
  if (xmlHttp.status == 408) {
    return 'IP不匹配，此文件只允许 ' + kwargs.ip + ' 机器访问，但本次访问的IP是：' + xmlHttp.getResponseHeader('signerror')
  } else {
    var statusText = {
      400: '签名不正确',
      401: '超时',
      403: '路径无权限',
      404: '无此文件',
      405: '正在转换',
      406: '转换失败',
      407: '正在下载',
      409: '账户不存在'
    }
    return statusText[xmlHttp.status] || 'Error: status code is ' + xmlHttp.status
  }
}

String.prototype.encodeJs = function() {
  return encodeURIComponent(this)
}
Object.serializeStr = function(obj) {
  if (obj == null) return null
  if (obj.serializeStr) return obj.serializeStr()
  var cst = obj.constructor
  switch (cst) {
    case String:
      return '"' + obj.encodeJs() + '"'
    case Date:
      return 'new Date(' + obj.getTime() + ')'
    case Array:
      var ar = []
      for (var i = 0; i < obj.length; i++) ar[i] = Object.serializeStr(obj[i])
      return '[' + ar.join(',') + ']'
    case Object:
      var ar = []
      for (var i in obj) {
        ar.push('"' + (i + '').encodeJs() + '":' + Object.serializeStr(obj[i]))
      }
      return '{' + ar.join(',') + '}'
    case Function:
      return '"' + obj.toString().encodeJs() + '"'
    default:
      return obj
  }
}

function embedFrame(type, identify, kwargs, url) {
  var newKwargs = {}
  var re = /^server_url|account|instance|location|username|preview_signcode|pdf_signcode|download_signcode|ip|app_id|timestamp|height|width|allow_copy|allow_print|mode|flash_first|paged_excel|folder_url|image_url|waterprint_text|waterprint_size|waterprint_color|waterprint_font|waterprint_alpha|waterprint_rotation|lang|device|filename|params|source_mime|mime|file_url$/
  for (var key in kwargs) {
    if (re.test(key)) {
      if (key == 'width') {
        newKwargs[key] = '99%'
      } else {
        newKwargs[key] = kwargs[key]
      }
    }
  }
  newKwargs['embedded'] = true
  var src = kwargs.server_url + '/edo_viewer?kwargs=' + Object.serializeStr(newKwargs)
  var width = getParentValue('100%')
  var height = getParentValue(kwargs.height)
  var name = identify + '-frame'
  var allowPrint = eval(kwargs.allow_print) == false ? false : true
  var lang = EDOViewer.langs[kwargs.lang]
  var obj = $('#' + identify)
  var $iframe = $('<iframe frameBorder="0"></iframe>').attr('src', src).attr('id', name).attr('name', name).css({
    width: width[0],
    height: height[0],
    'background-color': '#FFF'
  })
  if (type == 'html') {
    var $actions = $('<div class="preview-actions"></div>').css({
      background: '#FFF',
      'text-align': 'right',
      'font-size': '16px',
      padding: '2px 0',
      width: '100%'
    })
    if (allowPrint && !EDOViewer.isMobile) {
      $actions.append($('<a href="javascript:;" style="margin-right: 10px"><i class="fa fa-print"></i></a>').attr('title', lang['print']).on('click', function() {
        Viewer(identify).print(name)
      }))
    }
    var $fullscreen = $('<a href="javascript:;" style="margin-right: 10px"><i class="fa fa-expand"></i></a>').attr('title', lang['fullscreen']).on('click', function() {
      if (!obj.data('isFullScreen')) {
        $('body').css('overflow', 'hidden')
        obj.css({
          position: 'fixed',
          height: $(window).height(),
          width: $(window).width(),
          top: 0,
          left: 0,
          'z-index': 3e3
        }).addClass('crocodoc-viewer').data('isFullScreen', true)
        $iframe.css('height', $(window).height())
        $fullscreen.find('i').removeClass('fa-expand').addClass('fa-compress')
        $fullscreen.attr('title', lang['back'])
      } else {
        $('body').css('overflow', 'visible')
        obj.css({
          position: 'static',
          height: kwargs.height,
          width: '100%',
          top: 'auto',
          left: 'auto',
          'z-index': 'auto'
        }).removeClass('crocodoc-viewer').data('isFullScreen', false)
        obj.find('iframe').css('height', height[0])
        $fullscreen.find('i').addClass('fa-expand').removeClass('fa-compress')
        $fullscreen.attr('title', lang['fullscreen'])
      }
    })
    $actions.append($fullscreen)
    $(document).keyup(function(e) {
      if (e.keyCode === 27) {
        if (obj.data('isFullScreen')) {
          $fullscreen.trigger('click')
          e.stopPropagation()
        }
      }
    })
    obj.html($actions)
  } else {
    obj.html('')
  }
  if (EDOViewer.isMobile) {
    obj.append($('<div style="overflow: scroll;-webkit-overflow-scrolling: touch"></div>').css({
      width: width[1],
      height: height[1]
    }).append($iframe))
  } else {
    obj.append($iframe)
  }
  if (/eml$/.test(kwargs.ext) || kwargs.source_mime == 'message/rfc822') {
    obj.append($('<div style="display:none;"></div>').attr('id', identify + '-attachment'))
    kwargs['attachment'] = true
    if (kwargs.file_url) {
      var url = kwargs.file_url.replace('&mime=text/html', '&mime=application/json')
    } else {
      var url = getURL('compress', kwargs)
    }
    render_compress_viewer(url, identify + '-attachment', kwargs)
  }
}

function xmlHttpRequest(n, url, type, identify, kwargs, method, onlyRequest) {
  if (n > EDOViewer.retryCount - 1) {
    if (!onlyRequest) {
      document.getElementById(identify).innerHTML = tipsFunc(kwargs.server_url, 'timeout', kwargs.timeout_info, kwargs.lang)
    }
    return
  }
  var xhr = null
  if (window.XMLHttpRequest) {
    xhr = new XMLHttpRequest
  } else {
    if (window.ActiveXObject) {
      xhr = new ActiveXObject('MSXML2.XMLHTTP.3.0')
    }
  }
  if (!xhr) {
    document.getElementById(identify).innerHTML = 'Error initializing XMLHttpRequest!'
    return
  }
  xhr.open(method, url, true)
  xhr.setRequestHeader('Cache-Control', 'no-cache')
  xhr.send(null)
  xhr.onreadystatechange = function() {
    callbackFunc(xhr, n, url, type, identify, kwargs, method, onlyRequest)
  }
}

function ajaxRequest(n, url, type, identify, kwargs, method, onlyRequest) {
  if (n == 0 && !onlyRequest) {
    document.getElementById(identify).innerHTML = tipsFunc(kwargs.server_url, 'loading', kwargs.loading_info, kwargs.lang)
  }
  var origin = window.location.protocol + '//' + window.location.host
  if (navigator.appName == 'Microsoft Internet Explorer' && origin.indexOf(kwargs.server_url) == -1 && type != 'html') {
    var version = navigator.appVersion.split(';')[1].replace(/ +MSIE +/, '')
    if (version > 8 || version == 8) {
      if (n > EDOViewer.retryCount - 1) {
        if (!onlyRequest) {
          document.getElementById(identify).innerHTML = tipsFunc(kwargs.server_url, 'timeout', kwargs.timeout_info, kwargs.lang)
        }
        return
      }
      var xdr = new XDomainRequest
      xdr.open('GET', url)
      xdr.onload = function() {
        if (method == 'GET') {
          responseSuccess(xdr, url, type, identify, kwargs)
        }
      }
      xdr.onerror = function() {
        window.setTimeout(function() {
          ajaxRequest(n + 1, url, type, identify, kwargs, method, onlyRequest)
        }, EDOViewer.intervalSecond * 1e3)
        if (!onlyRequest) {
          document.getElementById(identify).innerHTML = tipsFunc(kwargs.server_url, 'converting', kwargs.converting_info, kwargs.lang)
        }
      }

      function progres() {
        if (method == 'HEAD') {
          responseSuccess(xdr, url, type, identify, kwargs)
        }
      }

      xdr.onprogress = progres
      try {
        xdr.send(null)
      } catch (ex) {
      }
    } else {
      embedFrame(type, identify, kwargs, url)
    }
  } else {
    if (type == 'html' && !kwargs.embedded) {
      embedFrame(type, identify, kwargs, url)
    } else {
      if (kwargs.mime && kwargs.source_mime && kwargs.mime == kwargs.source_mime) {
        responseSuccess(undefined, url, type, identify, kwargs)
      } else {
        xmlHttpRequest(n, url, type, identify, kwargs, method, onlyRequest)
      }
    }
  }
}

function callbackFunc(xmlHttp, n, url, type, identify, kwargs, method, onlyRequest) {
  if (xmlHttp.readyState == 4) {
    try {
      if (document.getElementById(identify) === null) {
        return
      }
    } catch (e) {
      return
    }
    var status = xmlHttp.status
    if (status == 200) {
      responseSuccess(xmlHttp, url, type, identify, kwargs)
    } else if (status == 404 || status == 0) {
      var delay = status == 0 ? 50 : EDOViewer.intervalSecond * 1e3
      if (status == 0 && EDOViewer.isMobile && method == 'HEAD') {
        method = 'GET'
      }
      window.setTimeout(function() {
        ajaxRequest(n + 1, url, type, identify, kwargs, method, onlyRequest)
      }, delay)
      if (!onlyRequest) {
        document.getElementById(identify).innerHTML = tipsFunc(kwargs.server_url, 'converting', kwargs.converting_info, kwargs.lang)
      }
    } else {
      if (!onlyRequest) {
        document.getElementById(identify).innerHTML = statusFunc(xmlHttp, kwargs)
      }
    }
  }
}

function responseSuccess(xmlHttp, url, type, identify, kwargs) {
  kwargs['callback'] = true
  if (type == 'html') {
    render_html_viewer(url, identify, kwargs)
  } else if (type == 'compress') {
    try {
      kwargs['data'] = eval('(' + xmlHttp.responseText + ')')
    } catch (ex) {
      kwargs['data'] = new Object
    }
    render_compress_viewer(url, identify, kwargs)
  } else if (type == 'audio') {
    render_audio_viewer(url, identify, kwargs)
  } else if (type == 'video') {
    render_video_viewer(url, identify, kwargs)
  } else if (type == 'image') {
    render_image_viewer(url, identify, kwargs)
  } else if (type == 'image-exif') {
    try {
      kwargs['data'] = eval('(' + xmlHttp.responseText + ')')
    } catch (ex) {
      kwargs['data'] = new Object
    }
    render_exif_viewer(url, identify, kwargs)
  } else if (type == 'box') {
    render_box_viewer(url, identify, kwargs)
  }
}

function removeLastSlash(url) {
  if (url.charAt(url.length - 1) == '/') {
    var url = url.substring(0, url.length - 1)
  }
  return url
}

function getExt(url) {
  var ext = /[.]/.exec(url) ? /.[^.]+$/.exec(url)[0].replace(/\?.*/, '').toLowerCase() : ''
  if (ext.indexOf('/') == -1) {
    return ext
  } else {
    return ''
  }
}

function getType(ext, pagedExcel) {
  if (/^(.xls|.xlsx|.xlsm|.ods)$/.test(ext) && pagedExcel != false) {
    return 'flash'
  }
  for (var type in EDOViewer.previewPatterns) {
    if (EDOViewer.previewPatterns[type].test(ext)) {
      return type
    }
  }
}

function getURL(type, kwargs) {
  var patterns = {
    flash: 'application/x-shockwave-flash-x',
    h5: 'application/x-shockwave-flash-x',
    html: 'text/html',
    compress: 'application/json',
    audio: 'audio/x-mpeg',
    image: 'image/x-thumbnail-png',
    'image-exif': 'application/json'
  }
  if (kwargs.mode == 'flv') {
    patterns['video'] = 'video/x-flv'
    var reExt = /(mp3|swf|mid|ogg|flv)$/i
  } else {
    patterns['video'] = 'video/mp4'
    var reExt = /(mp3|swf|mid|ogg|mp4)$/i
  }
  var pattern = patterns[type]
  if (pattern == undefined) {
    return
  } else {
    var location = kwargs.location || '', filename = kwargs.filename || '', ip = kwargs.ip || '',
      timestamp = kwargs.timestamp || '', device = kwargs.device || '', app_id = kwargs.app_id || '',
      account = kwargs.account || '', source_mime = kwargs.source_mime || '', instance = kwargs.instance || '',
      username = kwargs.username || '', params = kwargs.params || '',
      signcode = reExt.test(kwargs.ext) && !kwargs.source_mime ? kwargs.download_signcode : kwargs.preview_signcode || ''
    var url = kwargs.server_url + '/download?'
    url += 'location=' + encodeURIComponent(location)
    var paramsObject = {
      filename: filename,
      ip: ip,
      timestamp: timestamp,
      device: device,
      app_id: app_id,
      account: account,
      source_mime: source_mime,
      instance: instance,
      username: username,
      signcode: signcode
    }
    for (var key in paramsObject) {
      if (!paramsObject[key]) {
        continue
      }
      url += '&' + key + '=' + encodeURIComponent(paramsObject[key])
    }
    if (!reExt.test(kwargs.ext) || kwargs.source_mime) {
      url += '&mime=' + pattern
    }
    if (type == 'image') {
      url += '&subfile=image_' + (kwargs.image_size || 'large')
    }
    if (!$.isEmptyObject(params)) {
      url += '&params=' + Object.serializeStr(params)
    }
    return url
  }
}

function getParentValue(value) {
  var pxValue = '800px'
  if (value == undefined) {
    value = pxValue
  } else if (/px$/i.test(value)) {
    pxValue = value = parseInt(value.replace(/px/i, '')) + 'px'
  } else if (/em$/i.test(value)) {
    var reValue = parseInt(value.replace(/em/i, ''))
    pxValue = reValue + 'px'
    value = reValue + 'em'
  } else if (/\d$/.test(value)) {
    pxValue = parseInt(value) + 'px'
    value = parseInt(value)
  } else if (/%$/.test(value)) {
    pxValue = '100%'
    value = '100%'
  }
  return [value, pxValue]
}

var EdoViewer = {
  createViewer: function(identify, kwargs) {
    if (typeof $ === 'undefined') {
      throw new Error('jQuery is required')
    }

    function renderViewer() {
      var serverURL = removeLastSlash(kwargs.server_url), location = kwargs.location, filename = kwargs.filename
      if (!serverURL) {
        return false
      }
      if (!kwargs.height) {
        kwargs.height = $(window).height() - ($('#' + identify).offset().top - $(document).scrollTop() + 65)
        if (kwargs.height < 400) {
          kwargs.height = 400
        }
      }
      if (!kwargs.width) {
        kwargs.width = $(window).width() * .6
      }
      kwargs['layout'] = layout
      var ext = getExt(filename) || getExt(location), mode = kwargs.mode,
        flashFirst = eval(kwargs.flash_first) == false || /^.tif|.tiff$/i.test(ext) ? false : true,
        pagedExcel = eval(kwargs.paged_excel) == false ? false : true,
        type = /^(flash|html|compress|audio|video|image|h5)$/.test(mode) ? mode : getType(ext, pagedExcel)
      kwargs.server_url = serverURL
      if (type) {
        kwargs['ext'] = ext
        var url = kwargs.file_url || getURL(type, kwargs)
      }
      if (!/^(zh|en)$/.test(kwargs.lang)) {
        kwargs['lang'] = 'zh'
      }
      if (type == 'flash' || type == 'h5') {
        if (flashFirst == true && EDOViewer.flashVersion >= 9 && !EDOViewer.isMobile && mode != 'h5' || mode == 'flash') {
          $('head').append('<link media="all" href="' + serverURL + '/static/flash.viewer.css?v=1" rel="stylesheet" type="text/css">')
          render_flash_viewer(encodeURIComponent(url), identify, kwargs)
        } else if (EDOViewer.isMobile || typeof document.addEventListener != 'undefined' || mode == 'h5') {
          if (typeof window.Crocodoc === 'undefined') {
            $('head').append('<link media="all" href="' + serverURL + '/static/box/dist/my.crocodoc.viewer.css?v=5" rel="stylesheet" type="text/css">')
            $.getScript(serverURL + '/static/box/dist/my.crocodoc.viewer.min.js?v=5', function() {
              if (!$().mark) {
                $.getScript(serverURL + '/static/jquery.mark.min.js')
              }
              if (!$().slimbox) {
                $.getScript(serverURL + '/static/slimbox2.js')
              }
              render_box_viewer(url, identify, kwargs)
            })
          } else {
            render_box_viewer(url, identify, kwargs)
          }
        } else {
          render_flash_viewer(encodeURIComponent(url), identify, kwargs)
        }
      } else if (type == 'html') {
        render_html_viewer(url, identify, kwargs)
      } else if (type == 'compress') {
        render_compress_viewer(url, identify, kwargs)
      } else if (type == 'audio') {
        if (typeof window.jwplayer === 'undefined') {
          $.getScript(serverURL + '/static/jwplayer/jwplayer.js', function() {
            render_audio_viewer(url, identify, kwargs)
          })
        } else {
          render_audio_viewer(url, identify, kwargs)
        }
      } else if (type == 'video') {
        if (typeof window.jwplayer === 'undefined') {
          $.getScript(serverURL + '/static/jwplayer/jwplayer.js', function() {
            render_video_viewer(url, identify, kwargs)
          })
        } else {
          render_video_viewer(url, identify, kwargs)
        }
      } else if (type == 'image') {
        if (typeof $.slimbox === 'undefined') {
          $('head').append('<link media="all" href="' + serverURL + '/static/slimbox2.css" rel="stylesheet" type="text/css">')
          $.getScript(serverURL + '/static/slimbox2.js', function() {
            render_image_viewer(url, identify, kwargs)
          })
        } else {
          render_image_viewer(url, identify, kwargs)
        }
      } else {
        $('#' + identify).trigger('file-no-support')
        document.getElementById(identify).innerHTML = EDOViewer.langs[kwargs.lang]['no_support_preview']
      }
    }

    var layout = 'vertical'
    var viewer = {
      set_layout: function(value) {
        layout = value
      },
      load: function() {
        return renderViewer()
      },
      load_from_wo: function(url, access_token) {
        $.getJSON(url + '/@@get_viewer_args?access_token=' + (access_token || ''), function(data) {
          kwargs = $.extend(data, kwargs)
          return viewer.load()
        })
      },
      on: function(type, func) {
        return Viewer(identify).on(type, func)
      }
    }
    return viewer
  }, load: function() {
    return viewer.load()
  }
}
