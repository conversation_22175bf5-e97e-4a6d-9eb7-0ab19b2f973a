# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=पा़हिलाक् साहटा
next.title=ᱤᱱᱟᱹ ᱛᱟᱭᱚᱢ ᱥᱟᱦᱴᱟ

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom.title=हुडिञ ला़टु तेयार
presentation_mode.title=उदुक् सोदोर ओबोसता रे ओताय मे
presentation_mode_label=उदुक् सोदोर ओबोसता
open_file.title=रेत् झिज मे
open_file_label=झिज मे झिच्
bookmark.title=नितोगाक् ञेल (नावा विंडो रे नोकोल आर बाङ झिज मे )
bookmark_label=नितोगाक् ञेंल

# Secondary toolbar and context menu




# Document properties dialog box
document_properties_file_name=रेत् ञुतुम:
document_properties_file_size=रेत् माराङ तेत्:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} बाइट्स)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} बाइट्स)
document_properties_title=एम ञुतुम:
document_properties_author=ओनोलिया़:
document_properties_subject=बिसोय:
document_properties_keywords=का़ठी बोर्ड:
document_properties_creation_date=तेयार मा़हित्:
document_properties_modification_date=बोदोल होचो मा़हित्:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=बेनाविच्:
document_properties_producer=PDF तेयार ओडोकिच्:
document_properties_version=PDF बार्सान:
document_properties_page_count=साहटा लेखा:
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
document_outline_label=दोलिल तेयार तेत्
attachments.title=ᱞᱟᱴᱷᱟ ᱥᱮᱞᱮᱫ ᱠᱚ ᱩᱫᱩᱜᱽ ᱢᱮ
attachments_label=ᱞᱟᱴᱷᱟ ᱥᱮᱞᱮᱫ ᱠᱚ
thumbs.title=चिता़र आहला को उदुगा मे
thumbs_label=चिता़र आहला को
findbar.title=दोलिल रे ञाम

# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=साहटा {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=साहटा रेयाक् चिता़र आहला {{page}}

# Find panel button title and messages
find_previous.title=आयात् हिंस रेयाक् पा़हिल सेदाक् ओडोक् ञाम मे
find_next.title=आयात् हिंस रेयाक् इना़ तायोम ओडोक् ञाम मे
find_highlight=जोतो उदुक् राकाब
find_match_case_label=जोड़ मामला
find_reached_top=दोलिल रेयाक् चोट रे सेटेर, लातार खोन लेताड़
find_reached_bottom=दोलिल रेयाक् मुचा़त् रे सेटेर, चोट खोन लेताड़
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_not_found=आयात् हिंस बाय ञाम लेना

# Error panel labels
error_more_info=बाड़ती ला़य सोदोरढेर ला़य सोदोर
error_less_info=कोम ला़य सोदोर
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (तेयार: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=खोबोर: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=डांग: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=रेत्: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=गार: {{line}}
rendering_error=साहटा एम जोहोक मित् भुल हुय एना .

# Predefined zoom values
page_scale_width=साहटा ओसार
page_scale_fit=साहटा खाप
page_scale_auto=आच् आच् ते हुडिञ ला़टु तेयार
page_scale_actual=ᱴᱷᱤᱠ ᱢᱟᱨᱟᱝ ᱛᱮᱫ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages
loading_error=\u0020PDFलादे जोहोक् मित् भुल हुय एना.
invalid_file_error=बाङ बाताव आर बाङ  PDF रेत्.
missing_file_error=आदाक् PDF रेत्.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} बेयान एम]
password_label=नोवा PDF रेत् झिज ला़गित् दानाङ साबाद आदेर मे.
password_invalid=बाङ बातावाक् दानाङ साबाद. दोहड़ा कुरुमुटुय मे.
password_ok=OK

printing_not_supported=होसियार: छापा नोवा पानतेयाक् दाराय ते पुरा़व बाय गोड़ोवाकाना .
printing_not_ready=होंसिया़र: छापा ला़गित्  PDF पुरा़ बाय लादे आकाना.
web_fonts_disabled=वेब फॉन्ट बाङ हुय होचो आकाना: भितिर थापोन PDF फॉन्ट्स बेभार बाङ हुय केया.
